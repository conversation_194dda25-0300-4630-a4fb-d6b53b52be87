@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
@section('pageTitle', 'Teachers')
@section('breadcrumbs', Breadcrumbs::render('teachers'))
@section('content')
    <style>
        #teachers-table th {
            font-family: '<PERSON>', sans-serif !important;
            letter-spacing: 2px;
            font-size: 16px;
            color: #000;
        }

        #teachers-table .fa.fa-eye {
            color: #000;
        }

        .tooltip-inner {
            text-align: left;
        }
    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Search
                    </div>
                </div>
                <div class="card-block">
                    <form class="" action="" method="post" id="search-form">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-3">
                                <div class="form-group form-group-default">
                                    <label>Teacher's name</label>
                                    <input type="text" class="form-control" name="name" />
                                </div>
                            </div>
                            @if ($campuses->count() > 1)
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Campuses</label>
                                        <select class="full-width" name="campus" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($campuses as $campus)
                                                <option value="{{ $campus->id }}"> {{ $campus->name }} </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-12 text-right">
                                <br>
                                <button class="btn btn-primary" type="submit">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Teachers
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                <!-- <a data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="" href="#">
                                        <i class="fs-14 pg-plus"></i>
                                    </a> -->
                                <button data-target="#modalTeachersImport" data-toggle="modal" id="btnTeachersImport" class="btn btn-primary btn-cons">
                                    <i class="fa fa-upload"></i> Bulk Import
                                </button>
                                <a class="btn btn-primary" id="exportTeachers" href="#">Export All <i class="fa fa-download"></i></a>
                            </li>
                            <li>
                                <!-- <a data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="" href="#">
                <i class="fs-14 pg-plus"></i>
               </a> -->
                                <button data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                                    <i class="fa fa-plus"></i> Add New
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-block">
                    <table class="table table-hover custom-datatable no-footer" id="teachers-table">
                        <thead>
                            <tr>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Access</th>
                                <th>Country</th>
                                <th>State</th>
                                @if (Auth::user()->school->campuses()->exists())
                                    <th>Campuses</th>
                                @endif
                                <th>Registration Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @include ('teachers.modals.addteacher')

        @include ('teachers.modals.editteacher')

        @include ('teachers.modals.import')
    @endpush
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                $.fn.dataTable.ext.errMode = 'none';
                var oTable = $('#teachers-table').DataTable({
                    "order": [
                        [0, "asc"]
                    ],
                    stateSave: true,
                    processing: true,
                    serverSide: true,
                    dom: "<'row'<'col-md-12'lB>>" + "<'table-responsive'rt><'row'<'col-md-12'pi>>",
                    "autoWidth": false,
                    "lengthMenu": [
                        [10, 25, 50, 100 /* , -1 */ ],
                        [10, 25, 50, 100 /* , "All" */ ]
                    ],
                    buttons: [/* {
                        extend: 'csv',
                        text: '<i class="fa fa-download"></i> CSV',
                        exportOptions: {
                            columns: 'th:not(:last-child)'
                        }
                    } */],
                    ajax: {
                        url: 'teachers/getdata',
                        data: function(d) {
                            d.name = $('input[name=name]').val();
                            d.position = $('select[name=position]').val();
                            d.campus = $('select[name=campus]').val();
                            d.active_inactive = $('select[name=active_inactive]').val();
                            return d;
                        }
                    },
                    columns: [{
                            data: 'firstname',
                            name: 'firstname'
                        },
                        {
                            data: 'lastname',
                            name: 'lastname'
                        },
                        {
                            data: 'email',
                        },
                        {
                            data: 'access_level',
                        },
                        {
                            // data: 'state.country.code',
                            data: 'country_code'
                        },
                        {
                            data: 'state.code',
                            render: function(data, type, row) {
                                if (row.state_id) {
                                    return data || ''; 
                                } else {
                                    return row.school && row.school.state && row.school.state.code
                                        ? row.school.state.code 
                                        : '';
                                }
                            }
                        },
                        @if (Auth::user()->school->campuses()->exists())
                            {
                                data: 'campuses',
                            },
                        @endif {
                            data: 'registration_date',
                        },
                        {
                            data: 'action',
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    "drawCallback": function(settings) {

                        // Export Teachers With Filters Value
                        name = $('input[name=name]').val();
                        @if (Auth::user()->school->campuses()->count() > 1)
                            campus = $('select[name=campus]').val();
                        @else
                            campus = '';
                        @endif

                        jQuery("#exportTeachers").attr("href", "/teacher/export?fullname=" + name + "&campus=" + campus + "");

                        laravel.initialize();
                    }
                });
                $('#search-form').on('submit', function(e) {
                    oTable.draw();
                    e.preventDefault();
                });
            });
        </script>
    @endpush
@endsection
