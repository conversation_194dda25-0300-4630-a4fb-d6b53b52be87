<template>
    <div class="card">
        <div class="card-header border-0 px-xl-0">
            <p class="card-title fs-1">{{ page?.title }}</p>
        </div>
        <div class="card-body px-xl-0">
            <div v-html="page?.content"></div>
            <div class="mt-5">
                <a v-for="button in validButtons" :key="button.name" class="btn btn-lg btn-primary mb-2 me-2"
                    :href="button?.url" target="_blank">
                    {{ button?.name }}
                </a>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from 'vue';

export default defineComponent({
 props: {
        page: {
            type: Object as () => { title: string; content: string; buttons: { name: string; url: string }[] },
            required: true,
        },
    },


    setup(props) {
        const validButtons = computed(() =>
            (props.page?.buttons || []).filter(b => b.name && b.url)
        );

        return {
            validButtons
        }
    }
})
</script>
