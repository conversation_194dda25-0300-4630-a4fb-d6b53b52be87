@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('teachers'))
@section('content')
    <style>
        .tooltip-inner {
            text-align: left;
        }

        .form-group-checkbox {
            background-color: #fff;
            position: relative;
            border: 1px solid rgba(0,0,0,.07);
            border-radius: 2px;
            padding-top: 7px;
            padding-left: 12px;
            padding-right: 12px;
            padding-bottom: 4px;
        }

        .form-group-checkbox.required:after {
            color: #f35958;
            content: "*";
            font-family: arial;
            font-size: 20px;
            position: absolute;
            right: 12px;
            top: 6px;
        }

        .form-group-checkbox>.error-help-block {
           display: block;
        }

    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Search
                    </div>
                </div>
                <div class="card-block">
                    <form class="" action="" method="post" id="search-form">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-3">
                                <div class="form-group form-group-default">
                                    <label>Teacher's name</label>
                                    <input type="text" class="form-control" name="name" />
                                </div>
                            </div>
                            {{-- <div class="col-md-3">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Position</label>
                                <select class="full-width" name="position" data-init-plugin="select2">
                                    <option value="" selected>Any</option>
                                    @foreach ($positions as $key => $position)
                                    <option value="{{$position->id}}">{{$position->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div> --}}
                            @if (Auth::user()->role->name == 'Admin')
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>School</label>
                                        <select class="full-width" name="school" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($schools as $key => $school)
                                                <option value="{{ $school->id }}">{{ $school->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>School State</label>
                                        <select class="full-width" name="state" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($states as $state)
                                                <option value="{{ $state->id }}"> {{ $state->name }} </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Active/Inactive</label>
                                    <select class="full-width" name="active_inactive" data-init-plugin="select2">
                                        <option value="" selected>Any</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            @if (Auth::user()->role->name == 'Admin')
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>School Type</label>
                                        <select class="full-width" name="type" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            <option value="Independent"> Independent </option>
                                            <option value="Catholic"> Catholic </option>
                                            <option value="Government"> Government </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>School Gender</label>
                                        <select class="full-width" name="gender" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            <option value="Boy">Boy</option>
                                            <option value="Girl">Girl</option>
                                            <option value="Coed">Coed</option>
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-6 text-right">
                                <br>
                                <button class="btn btn-primary" type="submit">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Teachers
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                <!-- <a data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="" href="#">
                                    <i class="fs-14 pg-plus"></i>
                                </a> -->
                                <button data-target="#modalTeachersImport" data-toggle="modal" id="btnTeachersImport" class="btn btn-primary btn-cons">
                                    <i class="fa fa-upload"></i> Bulk Import
                                </button>
                                <a class="btn btn-primary" id="exportTeachers" href="#">Export All <i class="fa fa-download"></i></a>
                            </li>
                            <li>
                                <!-- <a data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="" href="#">
            <i class="fs-14 pg-plus"></i>
           </a> -->
                                <button data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                                    <i class="fa fa-plus"></i> Add New
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-block">
                    <table class="table table-hover custom-datatable no-footer" id="teachers-table">
                        <thead>
                            <tr>
                                {{-- <th>Name</th> --}}
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Access</th>
                                <th>Country</th>
                                <th>State</th>
                                <th>Position</th>
                                <th>School</th>
                                <th>School State</th>
                                <th>School Type</th>
                                <th>School Gender</th>
                                <th>Last seen</th>
                                <th>Login count</th>
                                <th>Avg login duration</th>
                                <th>Registration date</th>
                                <th>Subscription Ending On</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @include ('teachers.modals.addteacher')
        @include ('teachers.modals.editteacher')
        @include ('teachers.modals.import')
    @endpush
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                $.fn.dataTable.ext.errMode = 'none';
                var oTable = $('#teachers-table').DataTable({
                    "order": [
                        [0, "asc"]
                    ],
                    processing: true,
                    stateSave: true,
                    serverSide: true,
                    dom: "<'row'<'col-md-12'lB>>" + "<'table-responsive'rt><'row'<'col-md-12'pi>>",
                    "autoWidth": false,
                    "lengthMenu": [
                        [10, 25, 50, 100/* , -1 */],
                        [10, 25, 50, 100/* , "All" */]
                    ],
                    buttons: [/* {
                        extend: 'csv',
                        text: '<i class="fa fa-download"></i> CSV',
                        exportOptions: {
                            columns: 'th:not(:last-child)'
                        }
                    } */],
                    ajax: {
                        url: 'teachers/getdata',
                        data: function(d) {
                            d.name = $('input[name=name]').val();
                            d.position = $('select[name=position]').val();
                            d.school = $('select[name=school]').val();
                            d.state = $('select[name=state]').val();
                            d.type = $('select[name=type]').val();
                            d.gender = $('select[name=gender]').val();
                            d.active_inactive = $('select[name=active_inactive]').val();
                            return d;
                        }
                    },
                    columns: [
                        {
                            data: 'firstname',
                            name: 'firstname',
                            // orderable: false,
                        },
                        {
                            data: 'lastname',
                            name: 'lastname',
                            // orderable: false,
                        },
                        {
                            data: 'email',
                        },
                        {
                            data: 'access_level',
                        },
                        {
                            // data: 'state.country.code',
                            data: 'country_name'
                        },
                        {
                            data: 'state.code',
                            render: function(data, type, row) {
                                if (row.state_id) {
                                    return data || ''; 
                                } else {
                                    return row.school && row.school.state && row.school.state.code
                                        ? row.school.state.code 
                                        : '';
                                }
                            }
                        },
                        {
                            data: 'positions.0.name',
                        },
                        {
                            data: 'school.name',
                            orderable: false,
                        },
                        {
                            data: 'school.state.code',
                            orderable: false,
                        },
                        {
                            data: 'school.detail.type',
                            orderable: false,
                        },
                        {
                            data: 'school.detail.gender',
                            orderable: false,
                        },
                        {
                            data: 'lastseen',
                            name: 'lastseen',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'logincount',
                            name: 'logincount',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'avgduration',
                            name: 'avgduration',
                            searchable: false,
                            orderable: false,
                        },
                        {
                            data: 'registration_date',
                        },
                        {
                            data: 'subscription_end_date',
                            orderable: false,
                        },
                        {
                            data: 'action',
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    "drawCallback": function(settings) {

                        // Export Teachers With Filters Value
                        name = $('input[name=name]').val();
                        school = $('select[name=school]').val();
                        state = $('select[name=state]').val();
                        active_inactive = $('select[name=active_inactive]').val();
                        type = $('select[name=type]').val();
                        gender = $('select[name=gender]').val();

                        jQuery("#exportTeachers").attr("href", "/teacher/export?fullname=" + name + "&school=" + school + "&state=" + state + "&active_inactive=" + active_inactive + "&type=" + type + "&gender=" + gender + "");
                        laravel.initialize();
                    }
                });
                $('#search-form').on('submit', function(e) {
                    oTable.draw();
                    e.preventDefault();
                });
            });
        </script>
    @endpush
@endsection
