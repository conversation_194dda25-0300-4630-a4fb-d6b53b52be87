<?php

namespace App\Traits;

use App\ChildParent;
use App\IntercomCompany;
use App\IntercomUser;
use App\Jobs\DeleteCompanyFromIntercom;
use App\Jobs\DeleteUserFromIntercom;
use App\School;
use App\Services\Intercom;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait HasIntercomUserInfo
{

    /**
     * Generate UUID
     *
     * @return void
     */
    protected static function bootHasIntercomUserInfo()
    {
        static::created(function (self $model) {
            if (!$model->intercom) {
                $model->intercom()->create();
            }
            // School
            if ($model->role_id == 6 && !$model->intercomCompany) {
                $model->intercomCompany()->create();
            }
        });

        static::updated(function (self $model) {
            if (!$model->intercom) {
                $model->intercom()->create();
            }
            // School
            if ($model->role_id == 6 && !$model->intercomCompany) {
                $model->intercomCompany()->create();
            }
        });

        static::deleting(function ($model) {
            $intercomUserUuid = $model->intercom?->uuid;
            $intercomCompanyUuid = ($model->role_id == 6) ? $model->intercomCompany?->uuid : null;

            DeleteUserFromIntercom::dispatchIf(!empty($intercomUserUuid), $intercomUserUuid);
            DeleteCompanyFromIntercom::dispatchIf(!empty($intercomCompanyUuid), $intercomCompanyUuid);
        });

    }

    public function intercom(): HasOne
    {
        return $this->hasOne(IntercomUser::class, 'user_id');
    }

    public function intercomCompany(): HasOne
    {
        return $this->hasOne(IntercomCompany::class, 'model_id');
    }

    public function convertSecondsToTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%02d hours, %02d minutes, %02d seconds', $hours, $minutes, $remainingSeconds);
    }


    public function getIntercomUserDataAttribute()
    {
        $user = User::select('users.*')
            ->with([
                'role',
                'profile',
                'school',
                'state',
                'campuses',
                'cvs',
                'eportfolios',
                'sessionStats'
            ])
            ->where('id', $this->id)
            ->first();


        if (!$user) {
            return [];
        }

        $cvs = $user->cvs;
        $ePortfolio = $user->eportfolios;

        $gender = $user->profile?->gender;

        if ($gender) {
            if (in_array(strtolower($gender), ['m', 'male'])) {
                $gender = 'Male';
            } elseif (in_array(strtolower($gender), ['f', 'female'])) {
                $gender = 'Female';
            } else {
                $gender = 'Other';
            }
        }


        $customAttributes = [
            'User Role' => $user->role ? $user->role->name : null,
            'First Name' => $user->profile ? $user->profile->firstname : null,
            'Last Name' => $user->profile ? $user->profile->lastname : null,
            'Gender' => $gender,
            'School' =>  $user->school ? $user->school->name : null,
            'Campus' => $user->campuses && $user->campuses->first() ? $user->campuses->first()->name : null,
            'Login Count' => $user->sessionStats?->login_count,
            'Last Login' => $user->sessionStats?->last_login,
            'Avg Login Time' => $user->sessionStats?->avg_duration,
            'Account Created' => $user->created_at,
            'State' => $user->state ? $user->state->name : null,
            'Postcode' => $user->postcode,
            // 'Country' => $user->state && $user->state->country ? $user->state->country->name : null,
            'Country' => $user->state?->country?->name ?: $user->country?->name,
        ];

        $accountStatus = null;

        if (in_array($user->role_id, [3, 4])) { // Student/Individual Student
            $intercomGameplanData = $this->intercomGameplanData($user);

            $customAttributes = array_merge($customAttributes, [
                'Stage of life' => $user->profile && $user->profile->stage ? $user->profile->stage->title : null,
                'Year' => $user->profile && $user->profile->class ? $user->profile->class->title : null,
                'Graduated Year' => $user->profile && $user->profile->graduate_year ? $user->profile->graduate_year : null,
                'Profling' => $user->profile && $user->getHasCompletedProfilerAttribute() ? 'Yes' : 'No',
                'Resumes' => $cvs->count() >= 1 ? 'Yes' : 'No',
                'ePortfolio' => $ePortfolio->count() >= 1 ? 'Yes' : 'No',
                'Account Type' => null,
                'Subject Selections Tool Access' => $user->school && $user->school->hasSubjectsSelectionToolAccess() == true ? 'Yes' : 'No',
                'Subscription End Date' => $user->subscription_end_date,
                'Account Status' => $accountStatus,
                'Registered' => $user->profile && $user->profile->accountcreated ? 'Yes' : 'No',
                'Graduated' => ($user->profile && $user->profile->standard_id == 7 && $user->profile->removed == 0) ? 'Yes' : 'No',
                'School State' => $user->school?->state?->name,
            ] + $intercomGameplanData);
        } else if ($user->role_id == 2) { // Teacher
            $subscriptionEndDate = $user->school?->detail?->subscription_ending_on;

            $customAttributes = array_merge($customAttributes, [
                'Position' => $user->profile ? $user->profile->position : null,
                'Subscription End Date' =>  $subscriptionEndDate,
                'Account Type' => null,
                'Access Type' => $user->profile ? $user->profile->access : null,
                'Account Status' => $accountStatus,
                'Registered' => null
            ]);
        } else if ($user->role_id == 5) {  // Parent
            $parent = ChildParent::where('id', $user->id)->with('licencesPurchased', 'children:id,name', 'children.profile')->first();
            $subscriptionEndDate = $parent && $parent->licencesPurchased->isNotEmpty()
                ? $parent->licencesPurchased->max('valid_upto')
                : null;

            $formattedSubscriptionEndDate = $subscriptionEndDate ? Carbon::parse($subscriptionEndDate)->format('Y-m-d') : null;

            $childrenAccounts = [];
            $invitees = $parent->childInvitees()->whereProcessed('0')->get();

            if ($invitees) {
                foreach ($invitees as $invitee) {
                    if ($invitee->child) {
                        array_push($childrenAccounts, $invitee->child->name);
                    }
                }
            }

            if ($parent->children?->count()) {
                foreach ($parent->children as $child) {
                    if (@$child->profile?->accountcreated) {
                        array_push($childrenAccounts, $child->name);
                    }
                }
            }

            $customAttributes = array_merge($customAttributes, [
                'Subscription End Date' => $formattedSubscriptionEndDate, //added
                'Account Type' => $user->profile ? ($user->profile->premium_access === 1 ? 'Premium' : ($user->profile->premium_access === 0 ? 'Free' : null)) : null,
                'Account Status' => $accountStatus,
                'Registered' => 'Yes',
                'Children' => count($childrenAccounts) ? implode(',', $childrenAccounts) : null,
            ]);

        } else if ($user->role_id == 6) {  // School
            $school = School::whereId($user->id)->with('detail', 'plans')->first();
            $schoolDetail = $school->detail;
            $schoolAccountLimit = $school->account_limit;

            if (is_infinite($schoolAccountLimit)) {
                $schoolAccountLimit = -1;
            }

            $customAttributes = array_merge($customAttributes, [
                'Subscription End Date' => (($schoolDetail && $schoolDetail->subscription_ending_on) ? $schoolDetail->subscription_ending_on :  null),
                'School Code' => $school->password,
                'Gender' => $schoolDetail?->gender,
                'School Type' => $schoolDetail?->type,
                'Account Limit' => $schoolAccountLimit ? (int)$schoolAccountLimit : null,
            ]);
        }

        return [
            'name' => $user->name,
            'email' => $user->email,
            'custom_attributes' => $customAttributes
        ];
    }

    public function getIntercomGameplanDataAttribute()
    {
        return $this->intercomGameplanData();
    }

    public function intercomGameplanData($user = null)
    {
        try {
            if (!$user) {
                $user = User::select('users.*')
                        ->where('id', $this->id)
                        ->first();

                if (!$user) return [];
            }

            $latestPlan = $user->lastPlan();

            if (!$latestPlan) return [];

            $intercom = (new Intercom);

            // $intensionMapping = [
            //     'notsure' => "I'm not sure.",
            //     'gettingajob' => 'Getting a full time job',
            //     'gettingtrade' => 'Doing an apprenticeship or traineeship',
            //     'gapyear' => 'Taking a gap year',
            //     'continuestudy' => 'Studying a university course',
            //     'enteringdefence' => 'Entering the Defence Force or going through ADFA',
            // ];


            // $intension = $latestPlan && isset($latestPlan->interestedin) ?
            //     ($intensionMapping[$latestPlan->interestedin] ?? 'Unknown') : 'Unknown';

            $intensions = '';
            $industries = '';
            $institutes = '';

            $planIntension= $latestPlan->interestedIn()->pluck('gameplan_question_options.id') ?? collect();

            if(count($planIntension)) {
                $intensions = $intercom->truncateIdsForIntercomCDA($planIntension->implode('|'));
            }

            if ($latestPlan) {
                // INDUSTRIES
                $planIndustries = $latestPlan->industries()->pluck('industry_categories.id') ?? collect();

                if(count($planIndustries)) {
                    $industries = $intercom->truncateIdsForIntercomCDA($planIndustries->implode('|'));
                }

                // INSTITUTES
                $instituteIds = [];
                $institutesArr = $latestPlan->institutes()->get(['id', 'instituteable_type', 'instituteable_id', 'other_institute'])->toArray();

                array_walk($institutesArr, function ($val) use (&$instituteIds) {
                    if ($val['instituteable_type'] == 'App\Institute') {
                        $instituteIds[] = 'T' . $val['instituteable_id'];
                    } elseif ($val['instituteable_type'] == 'App\College') {
                        $instituteIds[] = 'C' . $val['instituteable_id'];
                    } elseif ($val['instituteable_type'] == 'App\University') {
                        $instituteIds[] = 'U' . $val['instituteable_id'];
                    } else {
                        $instituteIds[] = 'I' . $val['id'];
                    }
                });

                // $planTafes = $latestPlan->tafes()->pluck('institutes.id')->toArray();
                // dd($planTafes);
                // $instituteIds = array_merge($instituteIds, array_map(function($val) { return 'T'.$val; }, $planTafes));

                // $planColleges = $latestPlan->colleges()->pluck('colleges.id')->toArray();
                // $instituteIds = array_merge($instituteIds, array_map(function($val) { return 'C'.$val; }, $planColleges));

                // $planUniversities = $latestPlan->universities()->pluck('universities.id')->toArray();
                // $instituteIds = array_merge($instituteIds, array_map(function($val) { return 'U'.$val; }, $planUniversities));

                // $planOtherInstitutes = $latestPlan->institituteOthers()->pluck('institute_plan.id')->toArray();
                // $instituteIds = array_merge($instituteIds, array_map(function($val) { return 'I'.$val; }, $planOtherInstitutes));

                $instituteIds = array_filter($instituteIds, function($val) {
                    return $val != null && $val != "";
                });

                $institutes = $intercom->truncateIdsForIntercomCDA( implode('|',$instituteIds) );
            }


            // $institutes = collect()
            //     ->merge($latestPlan && isset($latestPlan->institituteOthers) ? $latestPlan->institituteOthers : collect())
            //     ->merge($latestPlan && isset($latestPlan->tafes) ? $latestPlan->tafes : collect())
            //     ->merge($latestPlan && isset($latestPlan->colleges) ? $latestPlan->colleges : collect())
            //     ->merge($latestPlan && isset($latestPlan->universities) ? $latestPlan->universities : collect())
            //     ->pluck('name')
            //     ->implode('|');

            // old game plan ends

            $dynamicQuestionLabels = $intercom->getDynamicGameplanQuestionLabels();

            $dynamicQuestionAnswers = $latestPlan->questionAnswers()
                            ->with('option', 'question')
                            ->whereHas('question', function ($query) use($dynamicQuestionLabels) {
                                return $query->whereIn('label', $dynamicQuestionLabels);
                            })
                            ->get()
                            ->groupBy('question.label')
                            ->map(function ($group) use($intercom) {
                                $psv = $group->pluck('option.value')->filter()->implode('|');
                                
                                return empty($psv) ? '' : $intercom->truncateIdsForIntercomCDA( $psv );
                            })
                            ->only($dynamicQuestionLabels)
                            ->toArray();
                            
            return [
                'Intentions' => $intensions,
                'Industries' => $industries,
                'Institutes' => $institutes,
            ] + $dynamicQuestionAnswers;
            
        } catch (Exception $e) {
            Log::error($e->getMessage(), [
                'stack' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }

    public function getIntercomCompanyDataAttribute()
    {
        $school = School::whereId($this->id)
        ->with('detail', 'plans')
        ->first();

        $schoolUser = User::find($this->id);

        $schoolDetail = $school->detail;

        if (!$school || !$schoolDetail || !$schoolUser) return [];

        $schoolLevel = null;

        // if ($schoolDetail->primary_section && $schoolDetail->secondary_section) {
        //     $schoolLevel = 'Combined';
        // } else if ($schoolDetail->primary_section && !$schoolDetail->secondary_section) {
        //     $schoolLevel = 'Primary';
        // } else if (!$schoolDetail->primary_section && $schoolDetail->secondary_section) {
        //     $schoolLevel = 'Secondary';
        // }

        $schoolAccountLimit = $school->account_limit;

        if (is_infinite($schoolAccountLimit)) {
            $schoolAccountLimit = -1;
        }

        $accountsUsedPercentage = null;

        if ($schoolAccountLimit > 0) {
            // $accountsUsed = $school->students()->whereHas('profile', function ($query) {
            //     $query->where('accountcreated', true)->where('standard_id', '<>', 7);
            // })->count();

            $accountsUsed = $schoolDetail->total_students;
            $accountsUsedPercentage = (float)(( $accountsUsed / $schoolAccountLimit ) * 100);
        }

        $customAttributes = [
            'School_Type' => $schoolDetail->type,
            'Logo' => !empty($schoolDetail->logo) ? Storage::url($schoolDetail->logo) : null,
            'Campuses' => substr($school->campuses()->pluck('name')->implode(','), 0, 255),
            'School Gender' => $schoolDetail->gender,
            'School_Code' => $school->password,
            'country' => $school->state?->country?->name ?: $school->country?->name,
            'state' => $school->state?->name,
            // 'Region' => $schoolDetail->region,
            'Suburb' => $schoolDetail->suburb,
            'postcode' => $school->postcode,
            'School Contact Person' => $schoolDetail->contact_person,
            'Email' => $schoolDetail->email,
            'Phone' => $schoolDetail->phone,
            'Subscription Start Date' => $schoolDetail->subscription_start_date,
            'Subscription_End_Date' => $schoolDetail->subscription_ending_on,
            'Account_Limit' => $schoolAccountLimit,
            'Percentage of Accounts Used' => $accountsUsedPercentage,
            'Active Accounts' => $schoolDetail->total_students,
            'Inactive Accounts' => $schoolDetail->total_inactive_students,
            'Graduated Accounts' => $schoolDetail->total_graduated_students,
            'Teacher Accounts' => $school->teachers()->count(),
            'Parent Accounts' => $school->parents()->count(),
            // 'ICSEA',
            // 'Location',
            // 'Indigenous Percentage',
            'School Level' => $schoolLevel,
            'Product Access' => substr(implode("|", $this->productAccessData($schoolUser)), 0, 255),
        ];

        return [
            'name' => $schoolDetail->name,
            'custom_attributes' => $customAttributes,
        ];
    }

    public function productAccessData($user, $wantsAssociativeArray = false)
    {
        try {
            $productAccess = [
                'Industries' => $user->hasIndustriesAccess(),
                'eMagazine' => $user->hasEMagazineAccess(),
                'Career Profiling' => $user->hasCareerProfilingAccess(),
                'Video Profiling' => $user->hasVideoProfilingAccess(),
                'My Path' => $user->hasMyPathAccess(),
                'GamePlan' => $user->hasGamePlanAccess(),
                'Profiling' => $user->hasProfilingAccess(),
                'Lessons' => $user->hasLessonsAccess(),
                'Virtual Work Experience' => $user->hasVweAccess(),
                'Skills Training' => $user->hasSkillsTrainingAccess(),
                'Work, Health & Safety' => $user->hasWhsAccess(),
                'JobFinder' => $user->hasJobFinderAccess(),
                'Scholarship Finder' => $user->hasScholarshipFinderAccess(),
                'Resume Builder' => $user->hasResumeBuilderAccess(),
                'Course Finder' => $user->hasCourseFinderAccess(),
                'ePortfolio' => $user->hasEPortfolioAccess(),
                'Subject Selections' => $user->hasSubjectSelectionsAccess(),
                'Noticeboard' => $user->hasNoticeboardAccess(),
                'Subjects Selection' => $user->hasSubjectsSelectionAccess($this),
                'Full' => $user->hasFullAccess(),
                'Primary' => $user->hasPrimarySchoolAccess(),
                'Secondary' => $user->hasSecondarySchoolAccess(),
            ];

            if ($wantsAssociativeArray) {
                return $productAccess;
            }

            $productAccess = array_keys(array_filter($productAccess, function($val) {
                return $val == true;
            }));

            return $productAccess;
        } catch (Exception $e) {
            Log::error($e->getMessage(), [
                'stack' => $e->getTraceAsString(),
            ]);
            return [];
        }
    }


}
