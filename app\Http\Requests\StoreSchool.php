<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSchool extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return $this->user()->role_id == 1;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'school_name' => 'required|unique:school_details,name|max:255',
            // 'state_id' => 'required|numeric',
            'state_id' => 'required_if:country,!=,|numeric',
            'postcode' => 'required',
            'contactperson' => 'required',
            'position' => 'required',
            'phone' => 'required',
            'email' => 'required|email',
        ];
    }
    public function messages()
    {
        return [
            'name.unique' => "School with this name already exists",
        ];
    }
    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        // $validator->after(function ($validator) {
        //     if ($this->nameIsInvalid()) {
        //         $validator->errors()->add('name', 'Something is wrong with this field!');
        //     }
        // });
        if ($validator->fails()) {

            $failedRules = $validator->failed();
            dd($failedRules);
            die("jjj");
            if (isset($failedRules['name']['Unique'])) {
                $validator->errors()->add('name', 'Something is wrong with this field!');
            }
        }
    }
}
