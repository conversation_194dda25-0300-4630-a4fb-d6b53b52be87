<?php

namespace App\Http\Controllers;

use App\Country;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class CountryController extends Controller {
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request) 
	{
		if ($request->ajax()) {
			$query = Country::select('countries.*')->withCount('users');
			
			return DataTables::of($query)
				->addColumn('action', function ($country) {
					return view('countries.partials._actions', compact('country'));
				})
				->rawColumns(['action'])
				->make(true);
		}

		return view('countries.index');
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request) {
		$data = $request->validate([
			'name' => 'required|string|max:255',
			'code' => 'required|string|max:10|unique:countries,code',
		]);

		Country::create($data);

		return response()->json([
			'success' => true,
			'message' => 'Country created successfully.',
		]);
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Country  $country
	 * @return \Illuminate\Http\Response
	 */
	public function show(Country $country) {
		return $country;
	}


	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Country  $country
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Country $country) {
		$data = $request->validate([
			'name' => 'required|string|max:255',
			'code' => 'required|string|max:10|unique:countries,code,' . $country->id,
		]);

		$country->update($data);

		return response()->json([
			'success' => true,
			'message' => 'Country updated successfully.'
		]);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Country  $country
	 * @return \Illuminate\Http\Response
	 */
	public function destroy(Country $country) {
		$country->delete();

		return response()->json([
			'success' => true,
			'message' => 'Country deleted successfully.'
		]);
	}
}
