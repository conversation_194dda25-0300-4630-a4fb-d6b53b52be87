<?php

namespace App\Http\Controllers;

use DB;
use Excel;
use App\Role;
use App\User;
use App\State;
use App\Campus;
use App\School;
use App\Country;
use App\Standard;
use Carbon\Carbon;
use App\MenuAccess;
use App\SchoolPlan;
use App\SchoolVisit;
use App\SchoolDetail;
use App\Services\Intercom;
use Illuminate\Http\Request;
use App\Helpers\MenuHelper;
use App\Events\LicenseRenewed;
use App\Exports\SchoolsExport;
use App\Http\Requests\StoreSchool;
use App\Http\Requests\UpdateSchool;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use App\Jobs\SyncCustomCompanyDataToIntercom;
use App\Jobs\NotifyUserOfCompletedSchoolsExport;
use Illuminate\Support\Str;
use App\Enums\InstituteType;
use App\SchoolVerifiedDomain;

class SchoolsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $instituteTypes = InstituteType::labels();
        $schools = School::confirmed()->paginate(10);
        $states = Country::find(1)->states;
        return view('schools.index', compact('schools', 'states',  'instituteTypes'));
    }

    public function create()
    {
        $states = State::all();
        $countries = Country::All();
        $primaryYears = Standard::primarySchool()->get();
        $secondaryYears = Standard::secondarySchool()->get();
        $basicPlans = SchoolPlan::whereType('Standard')->get();
        $proPlans = SchoolPlan::whereType('Premium')->get();
        $instituteTypes = collect(InstituteType::labels());

        return view('schools.add', compact('countries', 'primaryYears', 'secondaryYears', 'basicPlans', 'proPlans', 'states', 'instituteTypes'));
    }

    public function serverSide(Request $request)
    {
        $schooldetails = SchoolDetail::whereHas('school')->with(['school', 'school.plans'])->whereOrderConfirmed('1');
        $datatable = Datatables::of($schooldetails)
            ->filter(function ($query) {
                if ($school = request('school')) {
                    $query->where('name', 'like', "%{$school}%");
                }
                if ($type = request('type')) {
                    $query->where('type', $type);
                }
                if ($institute_type = request('institute_type')) {
                    $query->where('institute_type', $institute_type);
                }
                if ($gender = request('gender')) {
                    $query->where('gender', $gender);
                }
                if ($state = request('state')) {
                    $query->whereHas('school', function ($q) use ($state) {
                        $q->whereStateId($state);
                    });
                }
                if (request('active_inactive') == 'Active') {
                    $query->whereDate('subscription_ending_on', '>=', Carbon::now());
                } elseif (request('active_inactive') == 'Inactive') {
                    $query->whereDate('subscription_ending_on', '<=', Carbon::now());
                }
            }, true);

        return $datatable->addColumn('showinfrontend', '<div class="checkbox check-primary no-margin pick"><input type="checkbox" value="{{$school_id}}" id="schooldetail{{$school_id}}"  @if ($showinfrontend) checked @endif><label for="schooldetail{{$school_id}}" class="no-margin"></label></div>')
            ->addColumn('password', function ($schooldetail) {
                return $schooldetail->school->password;
            })
            ->addColumn('logo', function ($schooldetail) {
                if ($schooldetail->logo) {
                    return '<img src="' . Storage::url($schooldetail->logo) . '" class="img-fluid" alt="">';
                }
                return '';
            })

            ->addColumn('state', function ($schooldetail) {
                // return $schooldetail->school->state->code;
                return $schooldetail?->school?->state?->code;
            })
            ->addColumn('instituteLink', function ($schooldetail) {
                if ($schooldetail->institute_type->value === 'tertiary') {
                    $label = $schooldetail->institute_type->label();
                    $link = url('/#/sign-up-institute/' . $schooldetail->slug);

                    return view('components.institute.copy-link-badge', compact('label', 'link'))->render();
                }

                return 'N/A';
            })
            ->addColumn('instituteType', function ($schooldetail) {
                return $schooldetail->institute_type->label();
            })
            ->addColumn('postcode', function ($schooldetail) {
                return $schooldetail->school->postcode;
            })
            // ->addColumn('accountcreated', function ($schooldetail) {
            //     return $schooldetail->school->accountCreated();
            // })
            ->addColumn('accountcreated', function ($schooldetail) {
                // $this->fixcount($schooldetail->school, $schooldetail->total_students);
                // return "In Progress";
                $account_limit = $schooldetail->school->account_limit;
                $total_students = $schooldetail->total_students;
                if (($total_students >= $account_limit)) {
                    return '<span class="label label-danger fs-14">' . $total_students . '/' . $account_limit . '</span>';
                } elseif ((($account_limit - $total_students) < 20)) {
                    return '<span class="label label-warning fs-14">' . $total_students . '/' . $account_limit . '</span>';
                } else {
                    $total = ($account_limit != INF) ? $account_limit : '&infin;';
                    return '<span class="label fs-14">' . $total_students . '/' . $total . '</span>';
                }
            })
            ->addColumn('graduated_students', function ($schooldetail) {
                return $schooldetail->total_graduated_students;
            })
            ->addColumn('inactive_students', function ($schooldetail) {
                return $schooldetail->total_inactive_students;
            })
            ->addColumn('plans', function ($schooldetail) {
                return $schooldetail->school->plans->implode('type', ' | ');
            })
            ->addColumn('subscription_size', function ($schooldetail) {
                return $schooldetail->school->plans->implode('name', ' | ');
            })
            ->addColumn('years', function ($schooldetail) {
                return implode(', ', $schooldetail->school->plan_years);
            })
            ->addColumn('subscription_end_date', function ($schooldetail) {
                return Carbon::parse($schooldetail->subscription_ending_on)->toFormattedDateString();
            })
            ->addColumn('action', '<a href="{{route("schools.edit", $school_id)}}" class="m-r-10"><i class="fa fa-edit"></i></a> <a  data-id="{{$school_id}}"  id="schoolImport" class="m-r-10" href="/studentimport/{{$school_id}}"><i class="fa fa-upload"></i></a> <a href="schools/{{$school_id}}" data-method="delete" data-token="{{csrf_token()}}" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i> </a>')
            ->rawColumns(['showinfrontend', 'logo',  'accountcreated',  'action', 'instituteType', 'instituteLink'])
            ->make(true);
    }

    public function export()
    {

        $filename = 'public/export/schools-' . time() . '.xlsx';
        (new SchoolsExport())->queue($filename)->chain([
            new NotifyUserOfCompletedSchoolsExport(request()->user(), $filename),
        ]);

        return redirect('/schools')->with('message', "Export in progress , you will get an email with download link once it is ready.");
        // return Excel::download(new SchoolsExport, 'schools.xlsx');
    }


    public function ordered()
    {
        $schools = School::ordered()->paginate(10);
        return view('schools.ordered', compact('schools'));
    }


    public function visitRequest()
    {
        $schools = SchoolVisit::with('state')->paginate(10);
        return view('schools.visits', compact('schools'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function fixcount($school, $count)
    {
        SchoolDetail::where("school_id", $school->id)->update(["student_count" => $count]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreSchool $request)
    {
        $school = new School();

        $role_id = Role::where('name', 'School')->value('id');
        $school->fill([
            'role_id' => $role_id,
            'name' => $request->school_name,
            'email' => Str::random(20),
            'password' => $request->password,
            'country_id' => $request->country,
            'state_id' => $request->state_id,
            'postcode' => $request->postcode,
        ]);
        $school->save();

        $detail = new SchoolDetail();

        $logo = null;

        if ($request->hasFile('new_logo') && $request->file('new_logo')->isValid()) {
            $logo = $request->file('new_logo')->store('attachments/schools', ['visibility' => 'public']);
        }

        if ($request->institute_type == InstituteType::Tertiary->value) {
            if (!empty($request->verified_domains) && is_array($request->verified_domains)) {
                foreach ($request->verified_domains as $domain) {
                    if ($domain) {
                        SchoolVerifiedDomain::create([
                            'school_id' => $school->id,
                            'domain' => $domain,
                        ]);
                    }
                }
            }
            $detail->privacy_link = $request->privacy_link;
        }

        $detail->type = $request->type;
        $detail->institute_type = $request->institute_type;
        $detail->gender = $request->gender;
        $detail->suburb = $request->suburb;
        $detail->name = $request->school_name;
        $detail->logo = $logo;
        $detail->contact_person = $request->contactperson;
        $detail->position = $request->position;
        $detail->email = $request->email;
        $detail->phone = $request->phone;
        $detail->order_confirmed = $request->confirm;
        $detail->confirmed_at = $request->confirm == '1' ? Carbon::now() : null;
        $detail->subscription_start_date = $request->subscription_start_date;
        $detail->subscription_ending_on = $request->subscription_ending_on;
        $detail->account_limit = $request->account_limit;
        $detail->secondary_section = $request->secondary_section ? true : false;
        $detail->primary_section = $request->primary_section ? true : false;
        $detail->livechat = $request->livechat;
        $detail->show_postcode = $request->show_postcode;

        $school->detail()->save($detail);

        $menuTypes = MenuHelper::MENU_TYPES;

        if ($request->institute_type == InstituteType::Tertiary->value) {
            foreach ($menuTypes as $key => $menuName) {
                if (($menuName != 'Profile Visibility' && empty($request->tertiary_menus[$key])) || ($menuName == 'Profile Visibility' && !empty($request->tertiary_menus[$key]))) {
                    MenuAccess::firstOrCreate([
                        'menu' => $menuName,
                        'school_id' => $school->id,
                    ]);
                }
            }
        } else {
            $campuses = $request->campuses;
            if (!empty($campuses)) {
                foreach ($campuses as $name) {
                    if ($name) {
                        $campus = new Campus();
                        $campus->name = $name;
                        $school->campuses()->save($campus);
                    }
                }
            }


            if ($request->primary_section) {
                if ($request->primary_basic_plan && $request->primary_basic_years) {
                    $basicYears = [];
                    foreach ($request->primary_basic_years as $year) {
                        $basicYears['year_' . $year] = '1';
                    }
                    $school->plans()->attach($request->primary_basic_plan, $basicYears);
                }

                if ($request->primary_pro_plan && $request->primary_pro_years) {
                    $proYears = [];
                    foreach ($request->primary_pro_years as $year) {
                        $proYears['year_' . $year] = '1';
                    }
                    $school->plans()->attach($request->primary_pro_plan, $proYears);
                }
            }

            $secondaryYears = Standard::secondarySchool()->pluck('id');

            foreach ($menuTypes as $key => $menuName) {
                if (@$request->menus[$key]) {
                    if ($menuName == 'Profile Visibility') {
                        $hiddenYears = $request->menus[$key];
                    } else {
                        $hiddenYears = $secondaryYears->diff($request->menus[$key]);
                    }
                    foreach ($hiddenYears as $yearId) {
                        MenuAccess::firstOrCreate([
                            'menu' => $menuName,
                            'school_id' => $school->id,
                            'year_id' => $yearId,
                        ]);
                    }
                }
            }

            if ($school->campuses()->exists()) {
                $teachers = $school->teachers()->get();
                if ($teachers) {
                    foreach ($teachers as $teacher) {
                        $teacher->campuses()->sync($school->campuses()->pluck('id'));
                    }
                }
            }
        }

        Cache::tags(['userhasProAccess', 'school.' . $school->id])->flush();
        Cache::forget('schoolPlans' . $school->id);
        Cache::forget('standards' . $school->id);
        foreach (Standard::all() as $year) {
            Cache::forget('wewmenuY' . $year->id . 'S' . $school->id);
        }
        Cache::tags(['schoolmenuAccess'])->forget("schoolMenuAccess-" . $school->id);

        SyncCustomCompanyDataToIntercom::dispatchIf((new Intercom)->isEnabled(), $school);

        return redirect()->route('schools.index')->with('message', 'School created successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function show(School $school)
    {
        // if (request()->wantsJson()) {
        //     $school->detail->state_id = $school->state_id;
        //     $school->detail->postcode = $school->postcode;
        //     return $school->detail;
        // }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $school = School::where('id', $id)->with('detail', 'plans')->first();
        $countries = Country::All();
        $primaryYears = Standard::primarySchool()->get();
        $secondaryYears = Standard::secondarySchool()->get();
        $basicPlans = SchoolPlan::whereType('Standard')->get();
        $proPlans = SchoolPlan::whereType('Premium')->get();
        $instituteTypes = collect(InstituteType::labels());

        return view('schools.edit', compact('school', 'countries', 'primaryYears', 'secondaryYears', 'basicPlans', 'proPlans',  'instituteTypes'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());
        $school = School::findOrFail($id);

        $school_had_campuses = $school->campuses()->pluck('id');

        $school->update([
            'name' => $request->school_name,
            'password' => $request->password,
            'country_id' => $request->country,
            'state_id' => $request->state,
            'postcode' => $request->postcode,
        ]);

        $detail = $school->detail;

        if ($request->current_logo == "" && !empty($detail->logo) && Storage::exists($detail->logo)) {
            Storage::delete($detail->logo);
        }
        if ($request->new_logo) {
            $logo = $request->new_logo->store('attachments/schools', ['visibility' => 'public']);
            if ($detail->logo && Storage::exists($detail->logo)) {
                Storage::delete($detail->logo);
            }
        } else {
            $logo = $request->current_logo;
        }

        if ($request->type) {
            $detail->type = $request->type;
        }
        if ($request->gender) {
            $detail->gender = $request->gender;
        }
        if ($request->suburb) {
            $detail->suburb = $request->suburb;
        }

        if ($request->institute_type == InstituteType::Tertiary->value) {
            $school->verifiedDomains()->delete();

            if (!empty($request->verified_domains) && is_array($request->verified_domains)) {
                foreach ($request->verified_domains as $domain) {
                    if ($domain) {
                        SchoolVerifiedDomain::create([
                            'school_id' => $school->id,
                            'domain' => $domain,
                        ]);
                    }
                }
            }
            $detail->privacy_link = $request->privacy_link;
        }

        $detail->institute_type = $request->institute_type;
        $detail->name = $request->school_name;
        $detail->logo = $logo;
        $detail->contact_person = $request->contactperson;
        $detail->position = $request->position;
        $detail->email = $request->email;
        $detail->phone = $request->phone;
        $detail->order_confirmed = $request->confirm;
        if ($request->confirm == '1' && !$detail->confirmed_at) {
            $detail->confirmed_at = Carbon::now();
        }
        if ($request->subscription_start_date) {
            $detail->subscription_start_date = $request->subscription_start_date;
        }
        $olddate = $detail->subscription_ending_on;
        $detail->subscription_ending_on = $request->subscription_ending_on;
        $detail->account_limit = $request->account_limit;
        $detail->secondary_section = $request->secondary_section ? true : false;
        $detail->primary_section = $request->primary_section ? true : false;
        $detail->livechat = $request->livechat;
        $detail->show_postcode = $request->show_postcode;



        $school->detail()->save($detail);

        $menuTypes = MenuHelper::MENU_TYPES;

        if ($request->institute_type == InstituteType::Tertiary->value) {
            $school->campuses()->delete();
            $school->plans()->detach();
            $school->menuAccess()->delete();

            foreach ($menuTypes as $key => $menuName) {
                if (($menuName != 'Profile Visibility' && empty($request->tertiary_menus[$key])) || ($menuName == 'Profile Visibility' && !empty($request->tertiary_menus[$key]))) {
                    MenuAccess::firstOrCreate([
                        'menu' => $menuName,
                        'school_id' => $school->id,
                    ]);
                }
            }
        } else {
            $campusIds = $request->campusids;
            $campuses = $request->campuses;
            $diff = $school->campuses()->pluck('id')->diff($campusIds);
            Campus::find($diff)->each->delete();

            if (!empty($campuses)) {
                foreach ($campuses as $key => $name) {
                    if ($name) {
                        if (isset($campusIds[$key])) {
                            $campus = Campus::find($campusIds[$key]);
                            $campus->name = $name;
                            $campus->save();
                        } else {
                            $campus = new Campus();
                            $campus->name = $name;
                            $school->campuses()->save($campus);
                        }
                    }
                }
            }

            if (!$school_had_campuses && $school->campuses()->exists()) {
                $teachers = $school->teachers()->get();
                if ($teachers) {
                    foreach ($teachers as $teacher) {
                        $teacher->campuses()->sync($school->campuses()->pluck('id'));
                    }
                }
            } elseif (($school_had_campuses && $school->campuses()->doesntExist()) || $diff) {
                $users = $school->users()->pluck('id');
                DB::table('campus_user')->whereNotIn('campus_id', $school->campuses()->pluck('id'))->whereIn('user_id', $users)->delete();
            }

            $school->plans()->detach();

            if ($request->primary_section) {
                if ($request->primary_basic_plan) {
                    foreach ($request->primary_basic_years as $year) {
                        $basicYears['year_' . $year] = '1';
                    }
                    $school->plans()->attach($request->primary_basic_plan, $basicYears);
                }
                if ($request->primary_pro_plan) {
                    foreach ($request->primary_pro_years as $year) {
                        $proYears['year_' . $year] = '1';
                    }
                    $school->plans()->attach($request->primary_pro_plan, $proYears);
                }
            }

            $secondaryYears = Standard::secondarySchool()->pluck('id');
            $industriesHiddenYears = $secondaryYears->diff(@$request->menus['industries']);
            $emagazineHiddenYears = $secondaryYears->diff(@$request->menus['emagazine']);
            $myPathHiddenYears = $secondaryYears->diff(@$request->menus['myPath']);
            $careerProfilingHiddenYears = $secondaryYears->diff(@$request->menus['careerProfiling']);
            $videoProfilingHiddenYears = $secondaryYears->diff(@$request->menus['videoProfiling']);
            $gameplanHiddenYears = $secondaryYears->diff(@$request->menus['gameplan']);
            $profilingHiddenYears = $secondaryYears->diff(@$request->menus['profiling']);
            $lessonsHiddenYears = $secondaryYears->diff(@$request->menus['lessons']);
            $vweHiddenYears = $secondaryYears->diff(@$request->menus['vwe']);
            $stHiddenYears = $secondaryYears->diff(@$request->menus['st']);
            $whsHiddenYears = $secondaryYears->diff(@$request->menus['whs']);
            $jobHiddenYears = $secondaryYears->diff(@$request->menus['job']);
            $scholarshipHiddenYears = $secondaryYears->diff(@$request->menus['scholarship']);
            $resumeHiddenYears = $secondaryYears->diff(@$request->menus['resume']);
            $courseHiddenYears = $secondaryYears->diff(@$request->menus['course']);
            $eportfolioHiddenYears = $secondaryYears->diff(@$request->menus['eportfolio']);
            $subjectHiddenYears = $secondaryYears->diff(@$request->menus['subject']);
            $noticeboardHiddenYears = $secondaryYears->diff(@$request->menus['noticeboard']);
            $helpCenterHiddenYears = $secondaryYears->diff(@$request->menus['help']);
            $visibilityHiddenYears = $secondaryYears->diff(@$request->menus['visibility']);

            if (@$request->menus['industries']) {
                $school->menuAccess()->whereMenu('Industries')->whereIn('year_id', $request->menus['industries'])->delete();
            }
            if ($industriesHiddenYears) {
                foreach ($industriesHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Industries',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['emagazine']) {
                $school->menuAccess()->whereMenu('eMagazine')->whereIn('year_id', $request->menus['emagazine'])->delete();
            }
            if ($emagazineHiddenYears) {
                foreach ($emagazineHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'eMagazine',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['careerProfiling']) {
                $school->menuAccess()->whereMenu('careerProfiling')->whereIn('year_id', $request->menus['careerProfiling'])->delete();
            }
            if ($careerProfilingHiddenYears) {
                foreach ($careerProfilingHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'careerProfiling',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['videoProfiling']) {
                $school->menuAccess()->whereMenu('videoProfiling')->whereIn('year_id', $request->menus['videoProfiling'])->delete();
            }
            if ($videoProfilingHiddenYears) {
                foreach ($videoProfilingHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'videoProfiling',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['myPath']) {
                $school->menuAccess()->whereMenu('MyPath')->whereIn('year_id', $request->menus['myPath'])->delete();
            }
            if ($myPathHiddenYears) {

                foreach ($myPathHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'MyPath',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['gameplan']) {
                $school->menuAccess()->whereMenu('Game Plan')->whereIn('year_id', $request->menus['gameplan'])->delete();
            }
            if ($gameplanHiddenYears) {
                foreach ($gameplanHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Game Plan',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['profiling']) {
                $school->menuAccess()->whereMenu('Profiling')->whereIn('year_id', $request->menus['profiling'])->delete();
            }

            if ($profilingHiddenYears) {
                foreach ($profilingHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Profiling',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['lessons']) {
                $school->menuAccess()->whereMenu('Lessons')->whereIn('year_id', $request->menus['lessons'])->delete();
            }
            if ($lessonsHiddenYears) {
                foreach ($lessonsHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Lessons',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['vwe']) {
                $school->menuAccess()->whereMenu('Virtual Work Experience')->whereIn('year_id', $request->menus['vwe'])->delete();
            }
            if ($vweHiddenYears) {
                foreach ($vweHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Virtual Work Experience',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['st']) {
                $school->menuAccess()->whereMenu('Skills Training')->whereIn('year_id', $request->menus['st'])->delete();
            }
            if ($stHiddenYears) {
                foreach ($stHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Skills Training',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['whs']) {
                $school->menuAccess()->whereMenu('Work, Health & Safety')->whereIn('year_id', $request->menus['whs'])->delete();
            }
            if ($whsHiddenYears) {
                foreach ($whsHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Work, Health & Safety',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['job']) {
                $school->menuAccess()->whereMenu('Job Finder')->whereIn('year_id', $request->menus['job'])->delete();
            }
            if ($jobHiddenYears) {
                foreach ($jobHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Job Finder',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['scholarship']) {
                $school->menuAccess()->whereMenu('Scholarship Finder')->whereIn('year_id', $request->menus['scholarship'])->delete();
            }
            if ($scholarshipHiddenYears) {
                foreach ($scholarshipHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Scholarship Finder',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['resume']) {
                $school->menuAccess()->whereMenu('Resume Builder')->whereIn('year_id', $request->menus['resume'])->delete();
            }
            if ($resumeHiddenYears) {
                foreach ($resumeHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Resume Builder',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['course']) {
                $school->menuAccess()->whereMenu('Course Finder')->whereIn('year_id', $request->menus['course'])->delete();
            }
            if ($courseHiddenYears) {
                foreach ($courseHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Course Finder',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['eportfolio']) {
                $school->menuAccess()->whereMenu('ePortfolio')->whereIn('year_id', $request->menus['eportfolio'])->delete();
            }
            if ($eportfolioHiddenYears) {
                foreach ($eportfolioHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'ePortfolio',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['subject']) {
                $school->menuAccess()->whereMenu('Subject Selections')->whereIn('year_id', $request->menus['subject'])->delete();
            }
            if ($subjectHiddenYears) {
                foreach ($subjectHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Subject Selections',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['noticeboard']) {
                $school->menuAccess()->whereMenu('Noticeboard')->whereIn('year_id', $request->menus['noticeboard'])->delete();
            }
            if ($noticeboardHiddenYears) {
                foreach ($noticeboardHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Noticeboard',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if (@$request->menus['help']) {
                $school->menuAccess()->whereMenu('Help Centre')->whereIn('year_id', $request->menus['help'])->delete();
            }
            if ($helpCenterHiddenYears) {
                foreach ($helpCenterHiddenYears as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Help Centre',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            if ($visibilityHiddenYears) {
                $school->menuAccess()->whereMenu('Profile Visibility')->whereIn('year_id', $visibilityHiddenYears)->delete();
            }
            if (@$request->menus['visibility']) {
                foreach ($request->menus['visibility'] as $yearId) {
                    MenuAccess::firstOrCreate([
                        'menu' => 'Profile Visibility',
                        'school_id' => $school->id,
                        'year_id' => $yearId,
                    ]);
                }
            }

            // Update is_public status for students whose profile visibility is being hidden
            if ($visibilityHiddenYears->isNotEmpty()) {
                DB::table('profiles')
                    ->join('users', 'profiles.user_id', '=', 'users.id')
                    ->whereIn('profiles.standard_id', $visibilityHiddenYears)
                    ->where('users.school_id', $school->id)
                    ->where('profiles.standard_id', '<>', Standard::nonStudentId()) // Exclude graduated students
                    ->where('profiles.removed', false) // Exclude removed students
                    ->where('profiles.is_public', true) // Only update currently public profiles
                    ->whereNotExists(function ($query) {
                        $query->select(DB::raw(1))
                              ->from('licences')
                              ->whereColumn('licences.assigned_to', 'users.id')
                            //   ->whereIn('licences.type', ['Child', 'Individual'])
                              ->where('licences.valid_upto', '>=', now()->toDateString());
                    }) // Exclude students with valid Child or Individual licenses
                    ->update(['profiles.is_public' => false]);
            }
        }




        // if ($request->secondary_section) {
        //     if ($request->basic_plan) {
        //         foreach ($request->basic_years as $year) {
        //             $basicYears['year_' . $year] = '1';
        //         }
        //         $school->plans()->attach($request->basic_plan, $basicYears);
        //     }
        //     if ($request->pro_plan) {
        //         foreach ($request->pro_years as $year) {
        //             $proYears['year_' . $year] = '1';
        //         }
        //         $school->plans()->attach($request->pro_plan, $proYears);
        //     }
        // }

        if ($olddate != $detail->subscription_ending_on) {
            $students = $school->students()->with('parents')->get();
            foreach ($students as $student) {
                \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));
                if ($student->parents) {
                    foreach ($student->parents as $parent) {
                        \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
                    }
                }
            }

            foreach ($school->teachers as $teacher) {
                \Event::dispatch(new LicenseRenewed(User::where("id", $teacher->id)->first()));
            }
        }

        SyncCustomCompanyDataToIntercom::dispatchIf((new Intercom)->isEnabled(), $school);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Order updated successfully!');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function destroy(School $school)
    {
        $this->authorize('update', $school);
        // dd($school);
        (new Intercom)->deleteCompanyById($school->id);

        $school->delete();

        if (request()->wantsJson()) {
            return response([], 204);
        }

        // return redirect('/schools');
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'School has been deleted successfully!');
    }

    public function toggleVisit(Request $request)
    {
        $visit = SchoolVisit::where('id', $request->id)->update([
            'processed' => $request->visited,
        ]);

        return $visit;
    }
    public function showInFrontEnd($id)
    {
        $school = SchoolDetail::where('school_id', $id)->first();
        $school->showinfrontend = !$school->showinfrontend;
        $school->save();
        if (request()->wantsJson()) {
            return response()->json('success');
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)/* ->with('message', 'Your application has been saved!') */;
    }
    public function schoolToOrg($id)
    {
        $school = School::find($id);

        $teachers = $school->teachers()->get();
        foreach ($teachers as $key => $teacher) {
            $teacher->school_id = NULL;
            $teacher->role_id = Role::whereName('Staff')->value('id');
            $teacher->organisation_id = $id;
            $teacher->save();
        }

        $students = $school->students()->get();
        foreach ($students as $key => $student) {
            $student->school_id = NULL;
            $student->organisation_id = $id;
            $student->save();
        }

        $school->role_id = Role::whereName('Organisation')->value('id');
        $school->save();

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'School changed to an Oraganisation!');
    }

    public function newMenuAccess()
    {
        $secondaryYears = Standard::secondarySchool()->pluck('id');
        School::chunk(50, function ($schools) use ($secondaryYears) {
            foreach ($schools as $school) {
                $menus = [];
                foreach ($secondaryYears as $yearId) {
                    $menus[] = new MenuAccess([
                        'menu' => 'Subject Selections',
                        'year_id' => $yearId,
                    ]);
                }
                $school->menuAccess()->saveMany($menus);

                if ($school->detail && $school->account_limit != INF && !$school->detail->account_limit) {
                    $detail = $school->detail;
                    $detail->account_limit = $school->account_limit;

                    $school->detail()->save($detail);
                }
            }
        });
    }
}
