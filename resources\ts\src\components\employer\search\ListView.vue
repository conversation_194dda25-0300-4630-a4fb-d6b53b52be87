<template>
    <div class="tab-pane fade show active" id="kt_project_users_table_pane">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table id="kt_company_table"
                        class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bold">
                        <thead class="fs-7 text-gray-400 text-uppercase">
                            <tr>
                                <th class="min-w-200px">Company</th>
                                <th class="min-w-120px">Location</th>
                                <th class="min-w-300px">Description</th>
                                <th class="min-w-100px">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fs-6">
                            <tr v-for="company in companies" :key="company.id">
                                <!-- Company Logo + Name -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-5 position-relative">
                                            <img :src="company.logo" alt="Company Logo" class="img-thumbnail rounded"
                                                style="width: 50px; height: 50px; object-fit: cover; background-color: #e0e0e0;" />
                                        </div>
                                        <div class="d-flex flex-column justify-content-center">
                                            <div class="mb-1 text-gray-800 text-hover-primary">
                                                {{ company.name }}
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Location -->
                                <td>
                                    <span>
                                        <p class="text-black mb-5 mt-3">
                                            <span v-for="(state, index) in company.location" :key="index">
                                                {{ state }}
                                                <span v-if="index < company.location.length - 1">· </span>
                                            </span>
                                        </p>
                                    </span>
                                </td>

                                <!-- Description -->
                                <td>
                                    <span>{{ company.description }}</span>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <router-link
                                        :to="{ name: 'employer-company', params: { id: company.id } }">
                                        View Company Profile
                                    </router-link>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

interface Company {
    id: number;
    name: string;
    logo: string;
    location: string;
    description: string;
    profileUrl: string;
}

export default defineComponent({
    name: 'EmployerCourseListView',
    props: {
        companies: {
            type: Array as () => Company[],
            required: true,
            default: () => [],
        },
        loading: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
});
</script>