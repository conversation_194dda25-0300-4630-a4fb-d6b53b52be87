@extends('layouts.auth')
@section('content')
    <style>
        .license-feature-list>li {
            line-height: normal;
            display: inline-block;
            width: 100%;
        }

        .license-feature-list>li>img {
            width: 20px;
            float: left;
            margin-right: 5px;
        }

        .license-feature-list>li>span {
            width: calc(100% - 25px);
            float: left;
        }

        /* custom stripe pop-up*/
        .stripe_checkout_app.Checkout.is-desktop .Modal {
            border-radius: none !important;
            background-color: #fff !important;
        }

        .Checkout.is-desktop .Header-logoBorder {
            border: none !important;
            box-shadow: none !important;
        }

        .Checkout.is-desktop .Modal .Header {
            border-radius: none !important;
            background-color: #001ef3 !important;
        }

        .Checkout.is-desktop .Header-companyName,
        .Checkout.is-desktop .Header-purchaseDescription {
            color: #fff !important;
            text-shadow: none !important;
        }

        .Checkout.is-desktop .PaymentMethodSelector-wrapper {
            position: unset !important;
            background-color: #fff !important;
        }

        .Checkout. .Fieldset-input {
            border-radius: none !important;
            color: #2c2c2c !important;
        }

        .Checkout.is-desktop .Button {
            padding-bottom: 8px !important;
            padding-top: 8px !important;
            text-decoration: none !important;
            text-transform: uppercase !important;
            letter-spacing: 4px !important;
            font-family: 'Oswald', sans-serif !important;
            border-radius: 1px !important;
            font-weight: 700 !important;
            font-size: 12px !important;
            border: 2px solid #001ef3 !important;
            color: #001ef3 !important;
            text-align: center !important;
            cursor: pointer !important;
            background-color: transparent !important;
            -webkit-transition: all 300ms linear !important;
            -moz-transition: all 300ms linear !important;
            -o-transition: all 300ms linear !important;
            -ms-transition: all 300ms linear !important;
            transition: all 300ms linear !important;
            min-width: 120px !important;
        }

    </style>
    <!-- Preloader -->
    <div class="loader-mask" style="display:none;">
        <div class="loader">
            "Loading..."
        </div>
        <h4 style=" text-align: center; width: 100%; top: 55%; position: absolute; ">Payment Processing</h4>
    </div>
    <div class="register-bg d-flex m-t-50">
        <div class="register-container full-height justify-content-center d-flex flex-column">
            <div class="card no-margin no-border">
                <div class="card-header p-b-0">

                    <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-logo">
                    <h4 class="uppercase oswald ls-3 bold mb-1">Your parent/child licence</h4>
                    <p class="m-b-0 mb-1"><span class="bold">If your child already has an active account with us, there is no cost to create your parent account.</span> If your child does not already have an active account, the program costs $45 per year for each child.</p>
                </div>
                <div class="card-block pt-0">
                    @if (session()->has('message'))
                        <div class="alert alert-success text-center">
                            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
                            {{ session()->get('message') }}
                        </div>
                    @endif
                    <form class="m-t-10 m-b-20" id="parentLicenses" role="form" method="POST" action="{{ route('parent-license') }}" onkeydown="return event.key != 'Enter';">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label> First name </label>
                                    <input type="text" class="form-control" name="fname" value="" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label> Last name </label>
                                    <input type="text" class="form-control" name="lname" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-12">
                                <div class="form-group form-group-default required">
                                    <label> Email </label>
                                    <input type="email" class="form-control" name="email" @if ($invitation) value="{{ $invitation->email }}" @endif />
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label> Password </label>
                                    <input type="password" class="form-control" name="password" id="password" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label> Confirm Password </label>
                                    <input type="password" class="form-control" name="password_confirmation" />
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-12">
                                <div class="form-group form-group-default required form-group-default-select2 required">
                                    <label>Country</label>
                                    <select class="full-width" name="country" data-init-plugin="select2" data-placeholder="Select.." required>
                                        <option value=""></option>
                                        @foreach ($countries as $country)
                                            <option value="{{ $country->id }}"> {{ $country->name }} </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6" id="state-container" style="display: none;">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>State</label>
                                    <select id="state" class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Postcode</label>
                                    <input id="postcode" type="number" placeholder="Enter postcode" class="form-control" name="postcode" required>
                                </div>
                            </div>
                        </div>

                        <p class="bold m-t-20">Enter your child's details.</p>
                        @if ($invitation)
                            <input type="hidden" name="invitationtoken" value="{{ $token }}">
                            <div class="card">
                                <div class="card-header separator">
                                    <div class="card-title">Child <span class="number">1</span></div>
                                </div>
                                <div class="card-block">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default disabled required">
                                                <label> First name </label>
                                                <input type="text" name="child_fnames[1]" class="form-control" value="{{ $invitation->child->profile->firstname }}" readonly />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default disabled required">
                                                <label> Last name </label>
                                                <input type="text" name="child_lnames[1]" class="form-control" value="{{ $invitation->child->profile->lastname }}" readonly />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default disabled required">
                                                <label> Email </label>
                                                <input type="email" name="child_emails[1]" class="form-control" value="{{ $invitation->child->email }}" readonly />
                                                <input type="hidden" name="child_invitation[1]" class="form-control" value="{{ $invitation->token }}" />
                                                <input type="hidden" name="amount" @if ($invitation->child->activeLicense()) value="0" @else value="4500" @endif>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="card">
                                <div class="card-header separator">
                                    <div class="card-title">Child <span class="number">1</span></div>
                                </div>
                                <div class="card-block">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label> First name </label>
                                                <input type="text" class="form-control" name="child_fnames[1]" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label> Last name </label>
                                                <input type="text" class="form-control" name="child_lnames[1]" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default required">
                                                <label> Email </label>
                                                <input type="email" class="form-control" name="child_emails[1]" />
                                                <input type="hidden" name="amount" value="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        <div class="m-b-10 clearfix text-right">
                            <button class="btn btn-primary btn-custom-compact btn-custom-sm" type="button" id="btnAddChild"> <i class="fa fa-plus" aria-hidden="true"></i> Add Another Child </button>
                        </div>
                        {{-- <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default">
                                <label> ENTER COUPON </label>
                                <input type="text" class="form-control coupunData" name="coupon" />
                            </div>
                        </div>
                        <div class="col-md-6">
                         <button id="checkCoupon" class="btn btn-primary btn-custom-sm">CHECK</button>
                     </div>
                 </div> --}}
                        <div class="row">
                            <div class="col-md-12">
                                <p>Your total billing is: <span id="total-amount" class="bold">-</span></p>
                                <p class="fs-14 lh-normal mt-2 mb-3"><i>The digital licence(s) you are purchasing are valid for a year from the date of purchase</i></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button id="stripePayBtn" class="btn btn-primary">Start</button>
                            </div>
                        </div>
                        <script src="https://js.stripe.com/v3"></script>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('styles')
        <style>
            .select2-container{
                width: 100% !important;
            }
        </style>
    @endpush
    @push('scripts')
        <script>
            var stripe = Stripe("{{ config('services.stripe.key') }}");
            var checkoutButton = document.getElementById('stripePayBtn');

            // Close Checkout on page navigation:
            window.addEventListener('popstate', function() {
                handler.close();
            });

            function resetNumbering() {
                jQuery('.number').each(function(i) {
                    i++;
                    jQuery(this).text(i);
                });
            }

            resetNumbering();


            function totalAmount() {
                var sum = 0;
                $('input[name=amount]').each(function() {
                    sum += Number($(this).val());
                });
                if (sum) {
                    jQuery('#stripePayBtn').prop('disabled', false).text('Pay With Card');
                } else {
                    jQuery('#stripePayBtn').prop('disabled', false).text('Start');
                }
                return sum;
            }

            function formIsValid() {
                return $("#parentLicenses").valid();
            }

            jQuery(document).ready(function() {

                jQuery("select[name=country]").change(function() {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getStates') }}",
                        data: {
                            id: jQuery(this).val()
                        },
                        success: function(response) {
                            jQuery("select[name=state]").html('<option value="" hidden></option>');
                            $('select[name=state]').val('');

                            if (!response || response.length == 0) {
                                jQuery("#state-container").hide();
                                return;
                            }
                            jQuery("#state-container").show();

                            jQuery.each(response, function(i, v) {
                                jQuery("select[name=state]").append('<option value="' + v.id + '">' + v.name + '</option>')
                            });

                        },
                        fail: function() {
                            jQuery("select[name=state]").html('<option value="" hidden></option>');
                        }
                    });
                });
                jQuery("select[name=country]").trigger("change.select2");

                
                @if (isset($invitation))
                    amount = totalAmount()/100;
                    jQuery('#total-amount').text('$'+amount);
                    jQuery('input[name=email]').focus();
                @else
                    jQuery('input[type=email]').val('');
                @endif
                var num = 1;
                jQuery(document).on('click', '.remove', function() {
                    jQuery(this).closest('.card').remove();
                    amount = totalAmount() / 100;
                    jQuery('#total-amount').text('$' + amount);
                    resetNumbering();
                });
                jQuery(document).on('click', '#btnAddChild', function() {
                    num++;
                    jQuery(this).parent().before(
                        '<div class="card"> <div class="card-header separator"><div class="card-title">Child <span class="number"></span></div><div class="card-controls"><i role="button" class="fa fa-times text-danger remove"></i></div></div> <div class="card-block"> <div class="row clearfix"> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> First name </label> <input type="text" class="form-control" name="child_fnames[' + num + ']" /> </div> </div> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> Last name </label> <input type="text" class="form-control" name="child_lnames[' + num + ']" /> </div> </div> </div> <div class="row clearfix"> <div class="col-md-12"> <div class="form-group form-group-default required"> <label> Email </label> <input type="email" class="form-control" name="child_emails[' + num + ']" /> <input type="hidden" name="amount" value="0"> </div> </div> </div> </div> </div>'
                    );

                    $('input[name="child_lnames[' + num + ']"]').rules("add", {
                        required: true,
                    });

                    $('input[name="child_fnames[' + num + ']"]').rules("add", {
                        required: true,
                    });

                    $('input[name="child_emails[' + num + ']"]').rules("add", {
                        required: true,
                        email: true,
                        notEqualTo: ['input[type=email]'],
                        remote: {
                            url: "/checkEmail",
                            type: "get",
                            data: {
                                email: function() {
                                    return $('input[name="child_emails[' + num + ']"]').val();
                                }
                            },
                        },
                        messages: {
                            remote: "Email already in use!",
                            notEqualTo: "Email already entered!",
                        }
                    });
                    resetNumbering();
                });


                jQuery.validator.addMethod("notEqualTo", function(value, element, options) {
                    // get all the elements passed here with the same class
                    var elems = $(element).parents('form').find(options[0]);
                    // the value of the current element
                    var valueToCompare = value;
                    // count
                    var matchesFound = 0;
                    // loop each element and compare its value with the current value
                    // and increase the count every time we find one
                    jQuery.each(elems, function() {
                        thisVal = $(this).val();
                        if (thisVal == valueToCompare) {
                            matchesFound++;
                        }
                    });
                    // count should be either 0 or 1 max
                    if (this.optional(element) || matchesFound <= 1) {
                        //elems.removeClass('error');
                        return true;
                    } else {
                        //elems.addClass('error');
                    }
                }, "")

                $("#parentLicenses").validate({
                    invalidHandler: function(form, validator) {
                        if (!validator.numberOfInvalids()) {
                            return;
                        }

                        $('html, body').animate({
                            scrollTop: $(validator.errorList[0].element).offset().top - 100
                        }, 500);
                        $(validator.errorList[0].element).focus();

                    },
                    rules: {
                        fname: {
                            required: true
                        },
                        lname: {
                            required: true
                        },
                        email: {
                            required: true,
                            email: true,
                            notEqualTo: ['input[type=email]'],
                            remote: {
                                url: "/checkUserName",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $("input[name='email']").val();
                                    }
                                },
                            }
                        },

                        password: {
                            required: true,
                            minlength: 5
                        },
                        password_confirmation: {
                            equalTo: "#password"
                        },
                        country: 'required',
                        state: {
                            required: function() {
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                        },
                        postcode: 'required',
                        'child_fnames[1]': {
                            required: true
                        },
                        'child_lnames[1]': {
                            required: true
                        },
                        'child_emails[1]': {
                            required: true,
                            email: true,
                            notEqualTo: ['input[type=email]'],
                            remote: {
                                url: "/checkEmail",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $('input[name="child_emails[1]"]').val();
                                    }
                                },
                            },
                        }
                    },
                    messages: {
                        email: {
                            remote: "Email already in use!",
                            notEqualTo: "Email already entered!",
                        },
                        'child_emails[1]': {
                            remote: "Email already in use!",
                            notEqualTo: "Email already entered!",
                        },
                    },
                    submitHandler: function(form, event) {
                        if (totalAmount()) {
                            jQuery('#stripePayBtn').prop('disabled', true).text("Processing...")
                            event.preventDefault();
                            var formdata = jQuery("#parentLicenses").serialize();
                            var childcount = jQuery('input[name="amount"][value="4500"]').length;
                            var data = formdata + '&licensecount=' + childcount;
                            axios.post("{{ route('stripe.checkoutsession') }}", data)
                                .then(function(response) {
                                    if (typeof response.data.id != 'undefined') {
                                        stripe.redirectToCheckout({
                                            // Make the id field from the Checkout Session creation API response
                                            // available to this file, so you can provide it as argument here
                                            // instead of the {CHECKOUT_SESSION_ID} placeholder.
                                            sessionId: response.data.id
                                        }).then(function(result) {
                                            console.log("error")
                                            // jQuery(form).find(":submit").attr('disabled', 'disabled');
                                            //             form.submit();
                                            // If `redirectToCheckout` fails due to a browser or network
                                            // error, display the localized error message to your customer
                                            // using `result.error.message`.
                                        });
                                    }
                                })
                                .catch(function(error) {
                                    console.log(error);
                                });

                            return '';

                            //this runs when the form validated successfully
                        }
                        form.submit();

                    }
                });
                var timer;
                jQuery(document).on('keyup paste change', 'input[name^=child_emails]', function() {
                    var $element = jQuery(this);

                    $element.parent().next('div').remove();
                    $element.removeData("previousValue");
                    jQuery("#parentLicenses").validate().element(this);
                    clearTimeout(timer);
                    timer = setTimeout(function() {
                        if ($element.valid() == true) {
                            jQuery.ajax({
                                type: "GET",
                                url: '/checkStudentExist',
                                data: {
                                    email: $element.val(),
                                },
                                success: function(response) {
                                    if (response.result == "true" && response.license) {
                                        $element.next('input').val("0");
                                        $element.parent().after('<div><p class="px-2 py-1 bold">Looks like your child already has an active account with The Careers Department. <span class="text-blue">Great news, you save $45 on check out</span>!</p><div>You will need a parent licence to receive access to:</div><ul class="license-feature-list pl-0 no-list"><li><img src="{{ asset('images/favicon.png') }}"><span>Weekly parent newsletters</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>Your child&#39;s profile and their &#39;game plan&#39;</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>Access to over 500 careers explanation modules</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>40 specialised parent &#39;lessons&#39; including how scaling works, how to use this product with your child and biggest course and industry misconceptions explained.</span></li></ul><p class="bold">Why buy a parent licence if the school has bought their licence? Simple: 90% of students tell us that their parents are the number one influencer. It pays for you to be informed.</p><p>Your child will receive a notification that you&#39;ve created an account.</p></div>')
                                    } else {
                                        $element.next('input').val("4500");
                                    }
                                    amount = totalAmount() / 100;
                                    jQuery('#total-amount').text('$' + amount);
                                }
                            });
                        }
                    }, 700);
                });

                jQuery(document).on('click', '#checkCoupon', function(e) {
                    e.preventDefault();
                    var $coupon = jQuery('.coupunData').val();
                    // console.log($coupon);
                    jQuery.ajax({
                        context: this,
                        type: "get",
                        url: "/validate-coupon/" + $coupon,
                        success: function(response) {
                            console.log(response);
                        }
                    });
                });
            });
        </script>
    @endpush
@endsection
