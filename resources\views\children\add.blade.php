@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('edit-profile'))
@section('content')
@push('styles')
<style>
    .license-feature-list>li {
        line-height: normal;
        display: inline-block;
        width: 100%;
    }

    .license-feature-list>li>img {
        width: 20px;
        float: left;
        margin-right: 5px;
    }

    .license-feature-list>li>span {
        width: calc(100% - 25px);
        float: left;
    }
</style>
@endpush
@if(session()->has('message'))
<div class="alert alert-success text-center">
    <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a>
    {{ session()->get('message') }}
</div>
@endif
<!-- Preloader -->
<div class="loader-mask" style="display:none;">
    <div class="loader">
        "Loading..."
    </div>
    <h4 style=" text-align: center; width: 100%; top: 55%; position: absolute; ">Payment Processing</h4>
</div>
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card card-transparent">
            <div class="card-header">
                <img src="{{asset('images/favicon.png')}}" alt="*" class="card-title-X">
                <div class="card-title custom-card-title">Update Profile</div>
            </div>
            <div class="card-block">
                <form id="form-personal" action={{url('profiles/edit')}} method="POST" role="form" autocomplete="off"
                    enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <input type="hidden" name="_method" value="PUT">
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>First Name</label>
                                <input type="text" class="form-control" name="firstname" required=""
                                    value="{{ $user->profile->firstname }}" aria-required="true">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Last Name</label>
                                <input type="text" class="form-control" name="lastname" required=""
                                    value="{{ $user->profile->lastname }}" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Email</label>
                                <input type="email" class="form-control" name="email" value="{{ $user->email }}"
                                    required="" aria-required="true">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default" aria-required="true">
                                <label>Password</label>
                                <input type="password" class="form-control" name="password"
                                    placeholder="Enter only if you want to change" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                <label>Country</label>
                                <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                    @foreach($countries as $country)
                                     <option @if($country->id == $user->country_id) selected="selected" @endif value="{{$country->id}}">{{$country->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>State</label>
                                <select name="state" class="form-control">
                                    @foreach($user->state->country->states as $state)
                                    <option @if($state->id == $user->state_id) selected="selected" @endif
                                        value="{{$state->id}}">{{$state->name}}</option>>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Postcode</label>
                                <input type="text" class="form-control" name="postcode" required="" value="{{ $user->postcode }}" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        @if ($user->avatar_path)
                        <div class="col-md-8" id="inputCol">
                            <div class="form-group form-group-default">
                                <label>Avatar</label>
                                <input type="hidden" name="current_avatar" id="current-avatar"
                                    value="{{ $user->avatar_path }}">
                                <input type="file" class="form-control" id="new_avatar" name="new_avatar">
                            </div>
                        </div>
                        <div class="col-md-4" id="imgCol">
                            <div class="row">
                                <div class="col-md-12">
                                    <img src="{{ Storage::url($user->avatar_path) }}" alt="" class="img-fluid">
                                    {{-- <img src="{{url(Storage::cloud()->url($user->avatar_path))}}" alt=""
                                    class="img-fluid"> --}}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 text-danger text-right">
                                    <span role="button" id="removeImg" data-toggle="tooltip" data-placement="right"
                                        title="Remove Image"> <i class="fa fa-trash-o"></i></span>
                                </div>
                            </div>
                        </div>
                        @else
                        <div class="col-md-12" id="inputCol">
                            <div class="form-group form-group-default">
                                <label>Avatar</label>
                                <input type="hidden" name="current_avatar" id="current-avatar" value="">
                                <input type="file" class="form-control" id="new_avatar" name="new_avatar" />
                            </div>
                        </div>
                        @endif
                    </div>
                    <div class="clearfix"></div>
                    <button class="btn btn-black" type="submit">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card card-transparent">
            <div class="card-header">
                <img src="{{asset('images/favicon.png')}}" alt="*" class="card-title-X">
                <div class="card-title custom-card-title">Children</div>
            </div>
            <div class="card-block">
                {{-- <p>Invite your children to have their own account with The Careers Department. Once you purchase their account, they will be emailed with the details to get started. Once their account is created, you will be able to see the activities, profiling and what they are interested in.</p>
                    <p class="bold">If your child already has an account with their school - make sure you use the same email address they used for their school account set up.</p> --}}
                <form id="parentLicenses" action={{route('children.store')}} method="POST" role="form"
                    autocomplete="off" enctype="multipart/form-data">
                    {{ csrf_field() }}
                    @forelse($invitees as $invite)
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">Child {{$loop->iteration}}</div>
                            <div class="card-controls">
                                @if($invite->processed)
                                <h4 class="text-blue uppercase oswald no-margin lh-normal">Added <i
                                        class="fa fa-check"></i></h4>
                                @else
                                <h4 class="text-blue uppercase oswald no-margin lh-normal">Waiting <i
                                        class="fa fa-circle-o"></i></h4>
                                @endif
                            </div>
                        </div>
                        <div class="card-block">
                            <div class="row clearfix">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default disabled">
                                        <label>First Name</label>
                                        <input type="text" class="form-control"
                                            value="{{$invite->child->profile->firstname}}" disabled>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group form-group-default disabled">
                                        <label>Last Name</label>
                                        <input type="text" class="form-control"
                                            value="{{$invite->child->profile->lastname}}" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="row clearfix">
                                <div class="col-md-12">
                                    <div class="form-group form-group-default disabled">
                                        <label>Email</label>
                                        <input type="email" class="form-control" value="{{$invite->child->email}}"
                                            disabled>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    @endforelse
                    @if (config('app.env') == 'local')
                        <div class="m-b-10 clearfix text-right">
                            <button class="btn btn-primary btn-custom-sm" type="button" id="btnAddChild">
                                <i class="fa fa-plus" aria-hidden="true"></i> Add Another Child
                            </button>
                        </div>
                        <div class="row d-none" id="rowAmount">
                            <div class="col-md-12">
                                <p>Your total billing is: <span id="total-amount" class="bold">-</span></p>
                                <p>The digital licence(s) you are purchasing are valid for a year from the date of purchase
                                </p>
                            </div>
                            <div class="col-md-12" id="btn-submit">
                            </div>
                        </div>
                        <input type="hidden" name="stripeToken" value="" id="stripeToken">
                    @endif
                </form>
            </div>
        </div>
    </div>
</div>
<div class="row mb-4">
    <div class="col-md-8 mx-auto">
        <div id="accordion" class="custom-accordion small-accordion" role="tablist">
            <div class="card card-transparent">
                <div class="card-header bg-transparent no-border" role="tab" id="heading-nominees">
                    <h5 class="mb-0">
                        <img src="{{asset('images/favicon.png')}}" alt="*" class="card-title-X">
                        <div class="card-title custom-card-title">
                            <a class="oswald uppercase link" data-toggle="collapse" href="#collapse-nominees"
                                aria-expanded="false" aria-controls="collapse-nominees"
                                style="padding: 0; height: auto; background-color: blue; background-color: transparent !important; color: white !important;">
                                INDUSTRY PREFERENCES
                            </a>
                        </div>
                    </h5>
                </div>
            </div>
            <form action={{route('parent-industries.store', $user->id)}} method="POST" role="form" autocomplete="off"
                enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div id="collapse-nominees" class="collapse" role="tabpanel" aria-labelledby="heading-nominees"
                    aria-expanded="false" data-parent="#accordion">
                    <div class="bold text-black">
                        Tick the industries that you want to hear about.
                    </div>
                    <div class="row m-b-20">
                        <div class="col-md-8 ">
                            @foreach ($industrycategories->sortBy('name') as $key => $industrycategory)
                            <div class="checkbox check-primary">
                                <input type="checkbox" name="industries[]" value="{{$industrycategory->id}}" id="industry{{$industrycategory->id}}" @if ($selectedIndustries->contains($industrycategory->id)) checked @endif>
                                <label for="industry{{$industrycategory->id}}">{{$industrycategory->name}}</label>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    <button class="btn btn-primary" type="submit">SAVE</button>
                </div>
            </form>
        </div>
    </div>
</div>
@push('scripts')
<script>
    jQuery(document).ready(function () {
        @if($invitees->count())
        var num = {{$invitees->count()}};
        @else
        var num = 0;
        @endif

        jQuery(document).on('click', '.remove' ,function () {
            jQuery(this).closest('.card').remove();
            num--;
            amount = totalAmount()/100;
            jQuery('#total-amount').text('$'+amount);
            if(num == 1) {
                jQuery('#rowAmount').addClass('d-none');
            }

        });
        jQuery(document).on('click', '#btnAddChild' ,function () {
            num++;
            jQuery(this).parent().before(
                '<div class="card"> <div class="card-header"><div class="card-title">Child ' + num +'</div><div class="card-controls"><i role="button" class="fa fa-times text-danger remove"></i></div></div> <div class="card-block"> <div class="row clearfix"> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> First name </label> <input type="text" class="form-control" name="child_fnames[' + num +']" /> </div> </div> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> Last name </label> <input type="text" class="form-control" name="child_lnames[' + num +']" /> </div> </div> </div> <div class="row clearfix"> <div class="col-md-12"> <div class="form-group form-group-default required"> <label> Email </label> <input type="email" class="form-control" name="child_emails[' + num +']" /> <input type="hidden" name="amount" value="0"> </div> </div> </div> </div> </div>'
                );
            // cartAmount = cartAmount + 2000;
            $('input[name="child_lnames[' + num +']"]').rules("add", { required : true, });

            $('input[name="child_fnames[' + num +']"]').rules("add", { required : true, });

            $('input[name="child_emails[' + num +']"]').rules("add", {
                required : true,
                email: true,
                notEqualTo: ['input[type=email]'],
                remote: {
                    url: "/checkEmail",
                    type: "get",
                    data: {
                        email: function () {
                            return $('input[name="child_emails[' + num +']"]').val();
                        }
                    },
                },
                messages : {
                    remote: "Email already in use!",
                    notEqualTo: "Email already entered!",
                }
            });
        });
    });
</script>
<script src="https://checkout.stripe.com/checkout.js"></script>
<script>
    var handler = StripeCheckout.configure({
        key: "{{ config('services.stripe.key') }}",
        image: '../../images/favicon.png',
        locale: 'auto',
        token: function (token) {
            jQuery("#stripeToken").val(token.id);
            jQuery("#parentLicenses").submit();
            // loader
            jQuery('.loader-mask').show();
        }
    });


    // Close Checkout on page navigation:
    window.addEventListener('popstate', function () {
        handler.close();
    });


    function totalAmount() {
        var sum = 0;
        $('input[name=amount]').each(function() {
            sum += Number($(this).val());
        });
        return  sum;
    }

    function formIsValid() {
        return $("#parentLicenses").valid();
    }
    $(document).ready(function () {

        jQuery(document).on('click', '#removeImg', function () {
            var answer = confirm('Are you sure you want to remove this?');
            if (answer) {
                jQuery('#imgCol').remove();
                jQuery('#inputCol').removeClass('col-md-8').addClass('col-md-12')
                jQuery('#current-avatar').val('');
            } else {
                //do nothing
            }
        });

        jQuery.validator.addMethod("notEqualTo", function (value, element, options) {
            // get all the elements passed here with the same class
            var elems = $(element).parents('form').find(options[0]);
            // the value of the current element
            var valueToCompare = value;
            // count
            var matchesFound = 0;
            // loop each element and compare its value with the current value
            // and increase the count every time we find one
            jQuery.each(elems, function () {
                thisVal = $(this).val();
                if (thisVal == valueToCompare) {
                    matchesFound++;
                }
            });
            // count should be either 0 or 1 max
            if (this.optional(element) || matchesFound <= 1) {
                //elems.removeClass('error');
                return true;
            } else {
                //elems.addClass('error');
            }
        }, "")

        $("#parentLicenses").validate({
            rules: {
                'child_fnames[1]': {
                    required: true
                },
                'child_lnames[1]': {
                    required: true
                },
                'child_emails[1]': {
                    required: true,
                    email: true,
                    notEqualTo: ['input[type=email]'],
                    remote: {
                        url: "/checkEmail",
                        type: "get",
                        data: {
                            email: function () {
                                return $('input[name="child_emails[1]"]').val();
                            }
                        },
                    },
                }
            },
            messages: {
                'child_emails[1]': {
                    remote: "Email already in use!",
                    notEqualTo: "Email already entered!",
                },

            }
        });
        var timer;
        jQuery(document).on('keyup paste', 'input[name^=child_emails]', function() {
            var $element = jQuery(this);

            $element.parent().next('div').remove();
            $element.removeData("previousValue");
            jQuery("#parentLicenses").validate().element(this);
            clearTimeout(timer);
            timer = setTimeout(function(){
                jQuery('#btn-submit').empty();
                if ($element.valid() == true) {
                    jQuery.ajax({
                        type: "GET",
                        url: '/checkStudentExist',
                        data: {
                            email: $element.val(),
                        },
                        success: function (response) {
                            if (response.result == "true") {
                                $element.next('input').val(0);
                                $element.parent().after('<div>&#39;Email already in use with a school!&#39; Do you want to connect this account to yours? As they already have an account there is no cost to you. They will receive an email to confirm and will then appear in your account.</div>')
                            } else {
                                $element.next('input').val(2000);
                            }
                            amount = totalAmount()/100;
                            if(amount <= 0) {
                                jQuery('#rowAmount').removeClass('d-none');
                                jQuery('#btn-submit').html('<button type="submit" class="btn btn-primary">Add</button>');
                            } else {
                                jQuery('#rowAmount').removeClass('d-none');
                                jQuery('#btn-submit').html('<button id="stripePayBtn" class="btn btn-primary">Pay With Card</button>');
                                document.getElementById('stripePayBtn').addEventListener('click', function (e) {
                                    if (formIsValid()) {

                                        // totalAmount = totalAmount();
                                        // console.log(totalAmount);
                                        handler.open({
                                            name: 'The Careers Department',
                                            description: 'Child Licence Purchase',
                                            zipCode: true,
                                            currency: 'AUD',
                                            email: '{{Auth::user()->email}}',
                                            amount: totalAmount()
                                        });
                                    }
                                    e.preventDefault();
                                });
                            }
                            jQuery('#total-amount').text('$'+amount);
                        }
                    });
                }
            }, 800);
        });
    });
</script>
@endpush
@endsection
