<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Jobs\UpdateMailchimpAudienceJob;
use Illuminate\Http\Request;
use App\User;
use App\Role;
use App\ChildParent;
use App\ChildInvitee;
use App\ParentInvitee;
use Illuminate\Support\Str;
use Auth;
use Cookie;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Auth\RegistersUsers;
use App\Jobs\ProcessChildInvitation;
use App\Jobs\ProcessLimitedChildInvitation;
use App\Jobs\ProcessChildRegistration;
use DB;
use App\Events\LicenseRenewed;
use Mail;
use App\Student;
use App\School;
use App\Profile;
use App\Events\StudentRegistered;
use App\Mail\SchoolAccountLimitNotification;
use App\Jobs\ProcessParentInvitation;
use App\IndividualStudent;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use App\Standard;
use Illuminate\Support\Facades\Validator;

class RegisterationController extends Controller
{
    // use RegistersUsers; // Removed in Laravel 12 - using custom logic

    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    // use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except(['logout', 'sessionExpire']);
    }
    public function create()
    {
        return view('app');
    }

    public function store(Request $request)
    {
        // dd($request->all());

        $request->validate([
            'email' => [
                'required',
                // 'email:dns',
                function ($attribute, $value, $fail) {  // users with account created
                    $user = User::where('email', $value)
                        ->whereHas('profile', function ($q) {
                            $q->where('accountcreated', true);
                        })
                        ->first();
                    if ($user) {
                        $fail('The email has already been taken.');
                    }
                },
            ],
        ]);

        $school_id = $request->studentDetail['school']['id'];

        if (!$school_id) {
            session()->forget('data');
            return response()->json([
                'messages' => 'Your session has expired due to inactivity. Please try again.',
                'errors' => [['Your session has expired due to inactivity. Please try again.']]
            ], 400);
            return redirect('/school/select-school')->with('message', 'Your session has expired due to inactivity. Please try again.');
        }

        if (!School::find($school_id)->student_limit_reached) {

            $profile = new Profile();

            if (isset($request->underUniversity) && $request->underUniversity) {
                if ($request->firstName && $request->lastName) {
                    $profile->firstname = $request->firstName;
                    $profile->lastname = $request->lastName;
                }
                if ($request->genderOther) {
                    $gender = $request->genderOther;
                } else {
                    $gender = $request->gender;
                }

                if ($gender) {
                    $profile->gender = $gender;
                }

                $profile->accountcreated = true;
                $profile->standard_id = Standard::nonStudentId();

                $user = User::where('email', $request->email)->first();
                if (($user && !@$user->profile->accountcreated) || ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists())) {
                    $student = $user;
                } else {
                    $student = new Student;
                    $student->email = $request->email;
                }

                $student->role_id = 3;
                $student->name = $request->firstName . " " . $request->lastName;
                $student->school_id = $school_id;
                $student->password = bcrypt($request->password);
                $student->country_id = $request->studentDetail['country'] ?? null;
                $student->state_id = $request->studentDetail['state'] ?? null;
                $student->postcode = $request->postcode;
                $student->save();

                $password = $request->password;
            } else {

                if ($request->studentDetail['firstName'] && $request->studentDetail['lastName']) {
                    $profile->firstname = $request->studentDetail['firstName'];
                    $profile->lastname = $request->studentDetail['lastName'];
                }
                if ($request->studentDetail['genderOther']) {
                    $gender = $request->studentDetail['genderOther'];
                } else {
                    $gender = $request->studentDetail['gender'];
                }

                if ($gender) {
                    $profile->gender = $gender;
                }
                // if ($request->studentDetail['year']) {
                $profile->standard_id = $request->studentDetail['year'] ?? 2;
                // }
                $profile->accountcreated = true;

                $user = User::where('email', $request->email)->first();
                if (($user && !@$user->profile->accountcreated) || ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists())) {
                    $student = $user;
                } else {
                    $student = new Student;
                    $student->email = $request->email;
                }
                $student->role_id = 3;
                $student->name = $request->studentDetail['firstName'] . " " . $request->studentDetail['lastName'];
                $student->school_id = $school_id;
                $student->password = bcrypt($request->studentDetail['password']);
                $student->state_id = $request->studentDetail['state'] ?? null;
                $student->country_id = $request->studentDetail['country'] ?? null;
                $student->postcode = $request->studentDetail['postcode'];
                $student->save();

                if ($request->studentDetail['schoolCampus']) {
                    $student->campuses()->sync($request->studentDetail['schoolCampus']);
                }
                $password = $request->studentDetail['password'];
            }

            if (($user && !@$user->profile->accountcreated) || ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists())) {
                $student->profile()->update($profile->toArray());
                ChildInvitee::where('child_id', $student->id)->whereIn(
                    'parent_id',
                    $student->parents()->pluck('parent_id')
                )->update(['processed' => '1']);
                if ($school_id) {
                    event(new StudentRegistered($student));
                }
            } else {
                $student->profile()->save($profile);
                event(new StudentRegistered($student));
            }

            $school = $student->school;
            if ($student->school->detail->total_students == $student->school->account_limit - 20) {
                $response = "alert";
                Mail::send(new SchoolAccountLimitNotification($response, $school));
            } else if ($student->school->detail->total_students == $student->school->account_limit) {
                $response = "complete";
                Mail::send(new SchoolAccountLimitNotification($response, $school));
            }

            \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));

            // User::addHelpcrunchAccount($student->id);
            if ($student->is_child) {
                // User::updateAssociatedUsersMailchimpDetail($student->id);
            }
            $user = User::find($student->id);
            // UpdateMailchimpAudienceJob::dispatch($user);

            /*TODO Store Parent if submitted  , check if parent already created , if so just send invite , make sure email gets sent for welcome */

            if (Auth::attempt(['email' => $student->email, 'password' => $password])) {
                // return redirect('/#/gameplan')->with('message', "Your account has been successfully created.");
                $request->session()->regenerate();
                if (!empty($request->studentDetail['parent']) && !empty($request->studentDetail['parent']['email'])) {
                    $invitation = ParentInvitee::create([
                        'child_id' => Auth::id(),
                        'relation' => "parent",
                        'email' => $request->studentDetail['parent']['email'],
                        'token' => uniqid(Str::random(27)),
                        'processed' => '0',
                    ]);

                    try {
                        dispatch(new ProcessParentInvitation($invitation))->afterCommit();
                    } catch (\Exception $e) {
                        // Handle any exceptions that occur during dispatching
                        \Log::error('Failed to dispatch Parent Inviatation job', ['exception' => $e]);
                    }
                }
                return new UserResource(Auth::user());
            }
        } else {
            return response()->json([
                'messages' => 'Sorry! Your school has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.',
                'errors' => [['Sorry! Your school has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.']]
            ], 400);
        }
    }
    public function stripepurchasechildlicensesession(Request $request)
    {

        $parentdata = serialize($request->all());
        if ($request->plan == 'premium') {
            dd($request->all());
            $success_url = Auth::check() ? route('stripe.purchasechildsuccess') : route('stripe.parentchildsuccess');
            $parent_email = Auth::check() ? Auth::user()->email : $request->email;

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'subscription_data' => [
                    // 'trial_period_days' => 30,
                ],
                'customer_email' => $parent_email,
                'line_items' => [
                    [
                        'price' => config('services.stripe.child_key'),
                        'quantity' => count($request->children),
                    ]
                ],
                'mode' => 'subscription',
                'success_url' => $success_url,
                'allow_promotion_codes' => true,
                'cancel_url' => route('stripe.childfailed'),
                'metadata' => [
                    'child_id' => $request->child_id,
                ]
            ]);
            $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
            return $session;
        } else {
            $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => false]);
            return $this->stripelimitedparentchildsuccess($request);
            // $this->
        }
    }
    public function stripelimitedparentchildsuccess(Request $request) //When purchasing new parent license
    {
        $cart = $request->session()->get('cart.parentlicense');
        // $request->session()->forget('cart.parentlicense');


        try {

            $stripe_id = '';
            $form = unserialize($cart['parentlicense']);
            /*Payment successful*/
            $subscriptionid = '';
            DB::beginTransaction();


            $roleId = Role::where('name', "Individual Student")->value('id');

            $parentRoleId = Role::where('name', "Parent")->value('id');

            // if (array_filter($form['children'])) {
            $parent = ChildParent::create([
                'name' => $form['firstname'] . " " . $form['lastname'],
                'email' => $form['email'],
                'password' => bcrypt($form['password']),
                'state_id' => $form['state'] ?? null,
                'country_id' => $form['country'] ?? null,
                'postcode' => $form['postcode'],
                'stripe_id' => $stripe_id,
                'role_id' => $parentRoleId,
            ]);
            if ($parent) {
                // $user = User::find($parent->id);
                // $pm = $user->paymentMethods();
                // if ($pm) {
                //     $user->addPaymentMethod($pm->first()->id);
                //     $card = $user->paymentMethods()->first()->card;
                //     $user->card_last_four = $card->last4;
                //     $user->card_brand = $card->brand;
                //     $user->save();
                // }

                $parent->profile()->create([
                    'firstname' => $form['firstname'],
                    'lastname' => $form['lastname'],
                    'accountcreated' => true,
                    'premium_access' => false
                ]);



                // User::addHelpcrunchAccount($parent->id);
                // User::updateUserMailchimpDetail($parent->id);
                // if ($request->child_id) {
                //     ParentInvitee::where('child_id', $request->child_id)->email()->update([
                //         'processed' => '1',
                //     ]);

                //     $parent->children()->attach($request->child_id);
                // }
                // $fnames = $form['child_fnames'];
                // $lnames = $form['child_lnames'];
                // $emails = $form['child_emails'];
                $child_invitation = [];
                $quantity = 1;
                $children = [];
                if (!empty($form['children'])) {
                    foreach ($form['children'] as $key => $childdata) {
                        if ($childdata['email']) {
                            if (User::where('email', $childdata['email'])->exists()) {
                                $child = User::where('email', $childdata['email'])->first();

                                $children[] = $child->name;
                                if ($child->activeLicense()) {
                                    $parentprofile = $parent->profile;
                                    $parentprofile->premium_access = true;
                                    $parentprofile->save();
                                }
                                // $invitation = ChildInvitee::where('child_id', $child->id)->where('parent_id', Auth::id())->where('processed', 0)->first();
                                // $invitation = ChildInvitee::create([
                                //     'parent_id' => $parent->id,
                                //     'child_id' => User::where('email', $email)->value('id'),
                                //     'token' => uniqid(Str::random(27)),
                                //     'processed' => '0',
                                // ]);
                                if (isset($child_invitation[$key])) {
                                    ParentInvitee::whereToken($child_invitation[$key])->update([
                                        'processed' => '1',
                                    ]);


                                    $invitation = ChildInvitee::firstOrCreate(
                                        [
                                            'parent_id' => $parent->id,
                                            'child_id' => $child->id,
                                        ],
                                        [
                                            'token' => uniqid(Str::random(27)),
                                            'processed' => '1',
                                        ]
                                    );

                                    $parent->children()->syncWithoutDetaching($child->id);
                                } elseif ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child->id)->first()) {
                                    $parentInvitation->processed = '1';
                                    $parentInvitation->save();

                                    $invitation = ChildInvitee::firstOrCreate(
                                        [
                                            'parent_id' => $parent->id,
                                            'child_id' => $child->id,
                                        ],
                                        [
                                            'token' => uniqid(Str::random(27)),
                                            'processed' => '0',
                                        ]
                                    );

                                    $parent->children()->syncWithoutDetaching($child->id);
                                } else {
                                    $invitation = ChildInvitee::create(
                                        [
                                            'parent_id' => $parent->id,
                                            'child_id' => $child->id,
                                            'token' => uniqid(Str::random(27)),
                                            'processed' => '0',
                                        ]
                                    );

                                    dispatch(new ProcessChildInvitation($invitation));
                                }

                                // if (!User::where('email', $childdata['email'])->first()->activeLicense()) {

                                //     $licence = Licence::firstOrCreate(
                                //         ['assigned_to' => $child->id, 'type' => 'Child'],
                                //         [
                                //             'stripe_id' => $subscriptionid,
                                //             'purchased_by' => $parent->id,
                                //             'number' => strtoupper(sha1(microtime(true))),
                                //             'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                //         ]
                                //     );
                                // }
                            } else {
                                $child = '';
                                $child = IndividualStudent::create([
                                    'name' => "",
                                    'email' => ($childdata['email']) ? $childdata['email'] : uniqid(Str::random(27)),
                                    'password' => bcrypt(Str::random(10)),
                                    'role_id' => $roleId,
                                    'state_id' => $form['state'] ?? null,
                                    'country_id' => $form['country'] ?? null,
                                    'postcode' => $form['postcode'],
                                ]);
                                $children[] = $childdata['email'];
                                $child->profile()->create([
                                    'firstname' => "",
                                    'lastname' => "",

                                ]);

                                if ($child) {
                                    $parent->children()->syncWithoutDetaching($child->id);

                                    // $licence = new Licence();
                                    // $licence->stripe_id = $subscriptionid;
                                    // $licence->purchased_by = $parent->id;
                                    // $licence->assigned_to = $child->id;
                                    // $licence->number = strtoupper(sha1(microtime(true)));
                                    // $licence->type = 'Child';
                                    // $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                                    // $licence->save();


                                    $invitation = ChildInvitee::create([
                                        'parent_id' => $parent->id,
                                        'child_id' => $child->id,
                                        'token' => uniqid(Str::random(27)),
                                        'processed' => '0',
                                    ]);
                                    dispatch(new ProcessLimitedChildInvitation($invitation));
                                }
                                $quantity++;
                            }
                        }
                    }

                    // $user = User::find($parent->id);
                    // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                }
            }

            // \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));

            DB::commit();

            $data = [];

            if (count($children) == 1) {
                $data = ['child' => $children[0]];
            }

            Mail::send('emails.parentaccountcreated', $data, function ($message) use ($parent) {
                $message->to($parent->email)->subject('Account created successfully.');
            });

            if (Auth::attempt(['email' => $parent->email, 'password' => $form['password']])) {
                return new UserResource(Auth::user());
            }
            // }


            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect(route('landing') . '#/sign-in')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }
    public function storeParentLicense(Request $request)
    {
        // $relation = $request->relation;
        // foreach ($request->email as $key => $email) {
        //     $invitation = ParentInvitee::create([
        //         'child_id' => Auth::id(),
        //         'relation' => $relation[$key],
        //         'email' => $email,
        //         'token' => uniqid(Str::random(27)),
        //         'processed' => '0',
        //     ]);

        //     dispatch(new ProcessParentInvitation($invitation));
        // }
        DB::beginTransaction();
        try {
            $parentRoleId = Role::where('name', "Parent")->value('id');
            $emails = [$request->parentDetail['childEmail']];
            $child_emails = array_filter($emails);

            if ($child_emails) {
                if (User::whereIn('email', $emails)->exists()) {
                    $parent = ChildParent::create([
                        'name' => $request->parentDetail['firstname'] . " " . $request->parentDetail['lastname'],
                        'email' => $request->parentDetail['parentEmail'],
                        'password' => bcrypt($request->parentDetail['password']),
                        'state_id' => null,
                        'country_id' => null,
                        'postcode' => $request->parentDetail['postcode'],
                        'role_id' => $parentRoleId,
                    ]);
                    if ($parent) {
                        $parent->profile()->create([
                            'firstname' => $request->parentDetail['firstname'],
                            'lastname' => $request->parentDetail['lastname'],
                            'accountcreated' => true,
                        ]);

                        // User::addHelpcrunchAccount($parent->id);
                        // User::updateUserMailchimpDetail($parent->id);

                        $children = [];
                        if ($emails) {
                            foreach ($emails as $key => $email) {
                                if ($email) {
                                    $child = User::where('email', $email)->first();
                                    if ($child && $child->activeLicense()) {
                                        $children[] = $child->name;
                                        $child_id = $child->id;

                                        $processed = false;
                                        // if (isset($request->child_invitation[$key])) {
                                        //     ParentInvitee::whereToken($request->child_invitation[$key])->update([
                                        //         'processed' => '1',
                                        //     ]);
                                        //     $processed = true;
                                        //     $parent->children()->syncWithoutDetaching($child_id);
                                        // } else
                                        if ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child->id)->first()) {
                                            $parentInvitation->processed = '1';
                                            $parentInvitation->save();

                                            $processed = true;
                                            $parent->children()->syncWithoutDetaching($child->id);
                                        }
                                        $invitation = ChildInvitee::firstOrCreate(
                                            [
                                                'parent_id' => $parent->id,
                                                'child_id' => $child_id,
                                            ],
                                            [
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => $processed ? '1' : '0',
                                            ]
                                        );

                                        if (!$processed) {
                                            dispatch(new ProcessChildInvitation($invitation));
                                        }
                                    }
                                }
                            }
                        }
                    }

                    DB::commit();

                    $data = [];
                    if (count($children) == 1) {
                        $data = ['child' => $children[0]];
                    }

                    // Mail::send('emails.parentaccountcreated', $data, function ($message) use ($parent) {
                    //     $message->to($parent->email)->subject('Your account is ready! Log in now');
                    // });

                    if (Auth::attempt(['email' => $parent->email, 'password' => $request->parentDetail['password']])) {
                        $request->session()->regenerate();
                        return new UserResource(Auth::user());
                    }
                }
            }
            throw new \Exception();
        } catch (\Exception $e) {
            dd('f');
            return response()->json([
                'messages' => 'The provided credentials do not match our ddddrecords.',
                'errors' => [['The provided credentials do not match our records.']]
            ], 400);
        }
    }

    // public function validateEmail(Request $request)
    // {
    //     $emailDomain = substr(strrchr($request->all()['email'], "@"), 1);
    //     $validator = Validator::make($request->all(), [
    //         'email' => [
    //             'required',
    //             'email:dns',
    //             function ($attribute, $value, $fail) use ($emailDomain) {
    //                 if (!checkdnsrr($emailDomain, 'MX')) {
    //                     $fail('The email domain does not have valid MX records.');
    //                 }
    //             },
    //         ],
    //     ]);

    //     if ($validator->fails()) {
    //         return response()->json(['valid' => false, 'errors' => $validator->errors()], 422);
    //     }

    //     return response()->json(['valid' => true]);
    // }

    public function validateEmail(Request $request)
    {
        $email = $request->all()['email'];
        $emailDomain = substr(strrchr($email, "@"), 1);

        if (!filter_var($email, FILTER_VALIDATE_EMAIL) || !checkdnsrr($emailDomain, 'MX')) {
            return response()->json(['valid' => false, 'errors' => ['email' => ['The email domain does not have valid MX records.']]], 422);
        }

        return response()->json(['valid' => true]);
    }
}
