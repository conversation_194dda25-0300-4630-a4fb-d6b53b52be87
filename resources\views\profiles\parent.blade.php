@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('edit-profile'))
@section('content')
    @push('stylesheets')
        <link media="screen" type="text/css" rel="stylesheet" href="{{ asset('assets/plugins/switchery/css/switchery.min.css') }}">
    @endpush
    @push('styles')
        <style>
            .select2-container{
                width: 100% !important;
            }
            .license-feature-list>li {
                line-height: normal;
                display: inline-block;
                width: 100%;
                padding-left: 3px !important;
            }

            .license-feature-list>li:before {
                content: '' !important;
            }

            .license-feature-list>li>img {
                width: 20px;
                float: left;
                margin-right: 5px;
            }

            .license-feature-list>li>span {
                width: calc(100% - 25px);
                float: left;
            }

            .custom-accordion .card>.card-header {
                padding: 20px 20px 7px 20px;
            }

            .custom-accordion .card>.card-header .card-title {
                padding: 0 !important;
            }

            .custom-accordion .card>.card-header .card-title>a {
                padding: 2px 7.5px 2px 10px;
                height: auto;
                background-color: blue;
                background-color: transparent !important;
                color: #fff !important;
            }

            .custom-accordion .card>.card-header .card-controls a {
                height: auto;
                display: block;
                background: none !important;
                padding: 0;
            }

            .custom-accordion.small-accordion .card>.card-header a[aria-expanded="true"],
            .custom-accordion.small-accordion .card>.card-header a:hover,
            .custom-accordion.small-accordion .card>.card-header a:focus {
                color: #fff !important;
                box-shadow: none;
            }

            .custom-accordion.small-accordion .card-block {
                background-color: transparent;
            }

        </style>
    @endpush
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a>
            {{ session()->get('message') }}
        </div>
    @endif
    <!-- Preloader -->
    <div class="loader-mask" style="display:none;">
        <div class="loader">
            "Loading..."
        </div>
        <h4 style=" text-align: center; width: 100%; top: 55%; position: absolute; ">Payment Processing</h4>
    </div>
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card card-transparent">
                <div class="card-header">
                    <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                    <div class="card-title custom-card-title">Update Profile</div>
                </div>
                <div class="card-block">
                    <form id="form-personal" action={{ url('profiles/edit') }} method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>First Name</label>
                                    <input type="text" class="form-control" name="firstname" required="" value="{{ $user->profile->firstname }}" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Last Name</label>
                                    <input type="text" class="form-control" name="lastname" required="" value="{{ $user->profile->lastname }}" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" value="{{ $user->email }}" required="" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default" aria-required="true">
                                    <label>Password</label>
                                    <input type="password" class="form-control" name="password" placeholder="Enter only if you want to change" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                    <label>Country</label>
                                    <select name="country" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                        @foreach ($countries as $country)
                                            <option @if ($country->id == $user->country_id) selected="selected" @endif value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6" id="state-container" style="display: none;">
                                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                    <label>State</label>
                                    <select name="state" class="form-control" id="state" data-init-plugin="select2" data-placement="Select..">
                                        <option selected disabled></option>
                                        @if ($user->state)
                                            @foreach ($user->state->country->states as $state)
                                                <option @if ($state->id == $user->state_id) selected="selected" @endif value="{{ $state->id }}">{{ $state->name }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Postcode</label>
                                    <input type="text" class="form-control" name="postcode" required="" value="{{ $user->postcode }}" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <button class="btn btn-black" type="submit">Update</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @if (!$user->onGenericTrial() && $user->stripe_id)
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div id="subscription-accordion" class="custom-accordion small-accordion" role="tablist">
                    <div class="card card-transparent">
                        <div class="card-header bg-transparent no-border" role="tab" id="heading-subscription">
                            <h5 class="mb-0">
                                <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                                <div class="card-title custom-card-title">
                                    <a class="oswald uppercase link" data-toggle="collapse" href="#collapse-subscription" aria-expanded="false" aria-controls="collapse-subscription">
                                        Subscription
                                    </a>
                                </div>
                            </h5>
                        </div>
                        <div id="collapse-subscription" class="collapse" role="tabpanel" aria-labelledby="heading-subscription" aria-expanded="false" data-parent="#subscription-accordion">
                            <div class="card-block">
                                {{-- @if ($user->subscribed('Parent'))
                        <form id="form-autorenewal" action={{ route('renew-toggle') }} method="POST" role="form" autocomplete="off" class="mb-4">
                            @csrf
                            <div class="row mt-2">
                                <div class="col-12">
                                    <label for="" class="p-r-10">Auto Renewal</label>
                                    <input type="hidden" class="" id="" name="autorenew" value="0" />
                                    <input type="checkbox" class="switchery" name="autorenew" value="1" onChange="this.form.submit();" @if (!$user->subscription('Parent')->cancelled()) checked @endif>
                                </div>
                            </div>
                        </form>
                        @endif --}}
                                <div class="card">
                                    <div class="card-header">
                                        <div class="card-title">Card</div>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">
                                            <div class="col-8">
                                                <p>{{ $user->card_brand . ' ****' . $user->card_last_four }} {{-- <button class="btn btn-link" id="btnCardEdit"><i class="fa fa-pencil"></i></button> --}}
                                                <a href="{{ url('/#/subscriptions') }}" class="btn btn-link"><i class="fa fa-pencil"></i> Manage Subscription</a>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="row" id="newCard" style="display:none;">
                                            <div class="col-12">
                                                <form action="{{ route('card-update') }}" method="post" id="payment-form">
                                                    @csrf
                                                    <div class="form-row">
                                                        <label for="card-element">
                                                            New card
                                                        </label>
                                                        <div id="card-element">
                                                            <!-- a Stripe Element will be inserted here. -->
                                                        </div>

                                                        <!-- Used to display form errors -->
                                                        <div id="card-errors" role="alert"></div>
                                                    </div>

                                                    <button type="submit" class="btn btn-primary submit mt-4 float-right">Update</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="row">
        <div class="col-md-8 mx-auto">
            
            
            <div id="children-accordion" class="custom-accordion small-accordion" role="tablist">
                <div class="card card-transparent">
                    <div class="card-header bg-transparent no-border" role="tab" id="heading-children">
                        <h5 class="mb-0">
                            <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                            <div class="card-title custom-card-title">
                                <a class="oswald uppercase link" data-toggle="collapse" href="#children" aria-expanded="false" aria-controls="collapse-children">
                                    Children
                                </a>
                            </div>
                        </h5>
                    </div>
                    <div id="children" class="collapse" role="tabpanel" aria-labelledby="heading-children" aria-expanded="false" data-parent="#children-accordion">
                        <div class="card-block">
                            {{-- <p>Invite your children to have their own account with The Careers Department. Once you purchase their account, they will be emailed with the details to get started. Once their account is created, you will be able to see the activities, profiling and what they are interested in.</p>
                            <p class="bold">If your child already has an account with their school - make sure you use the same email address they used for their school account set up.</p> --}}
                            {{-- <form id="parentLicenses" action={{ route('children.store') }} method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                                @csrf --}}
                                @forelse($invitees as $invite)
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="card-title">Child Details{{-- <span class="number">{{ $loop->iteration }}</span> --}}</div>
                                            <div class="card-controls">
                                                @if ($invite->processed)
                                                    <h4 class="text-blue uppercase oswald no-margin lh-normal">Added <i class="fa fa-check"></i></h4>
                                                @else
                                                    <h4 class="text-blue uppercase oswald no-margin lh-normal">Waiting <i class="fa fa-circle-o"></i></h4>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="card-block">
                                            @if (!$invite->processed)
                                                @if (!$invite->child->profile->accountcreated)
                                                    <p class="pb-2">Pending account creation. {{ $invite->child->name }} has received an invitation to create their account and connect it to yours, but has not set this up yet. You can reinvite them from below button.</p>
                                                @else
                                                    <p class="pb-2">Pending account connection. {{ $invite->child->name }} has an account but has not connected it to yours yet. You can reinvite them to do this from below button.</p>
                                                @endif
                                            @endif
                                            <div class="row clearfix">
                                                <div class="col-md-6">
                                                    <div class="form-group form-group-default disabled">
                                                        <label>First Name</label>
                                                        <input type="text" class="form-control" value="{{ $invite->child->profile->firstname }}" disabled>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group form-group-default disabled">
                                                        <label>Last Name</label>
                                                        <input type="text" class="form-control" value="{{ $invite->child->profile->lastname }}" disabled>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row clearfix">
                                                <div class="col-md-12">
                                                    <div class="form-group form-group-default disabled">
                                                        <label>Email</label>
                                                        <input type="email" class="form-control" value="{{ $invite->child->email }}" disabled>
                                                    </div>
                                                </div>
                                            </div>
                                            @if (!$invite->processed)
                                                <div class="text-right"><a href="{{ route('child.reinvite', $invite->id) }}" class="btn btn-primary"><i class="fa fa-paper-plane"></i> Re-invite</a></div>
                                            @endif
                                        </div>
                                    </div>
                                @empty
                                @endforelse
                                @if ($invitations->isNotEmpty())
                                    <h5 class="uppercase oswald">Child Invitations:</h5>
                                    @foreach ($invitations as $invitation)
                                    @if($invitation->child)
                                        <input type="hidden" name="invitationtoken[{{ $loop->iteration }}]" value="{{ $invitation->token }}">
                                        <div class="card">
                                            <div class="card-header">
                                                <div class="card-title">Child Details{{-- <span class="number">1</span> --}}</div>
                                                <div class="card-controls">
                                                    <a href={{ route('parentinvitees.destroy', $invitation->id) }} data-toggle="tooltip" title="Decline" data-method="delete" data-token="{{ csrf_token() }}" data-confirm="Are you sure?"><i class="fa fa-times text-danger"></i></a>
                                                </div>
                                            </div>
                                            <div class="card-block">
                                                <div class="row clearfix">
                                                    <div class="col-md-6">
                                                        <div class="form-group form-group-default disabled required">
                                                            <label> First name </label>
                                                            <input type="text" name="child_fnames[{{ $loop->iteration }}]" class="form-control" value="{{ $invitation->child->profile->firstname }}" readonly />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group form-group-default disabled required">
                                                            <label> Last name </label>
                                                            <input type="text" name="child_lnames[{{ $loop->iteration }}]" class="form-control" value="{{ $invitation->child->profile->lastname }}" readonly />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row clearfix">
                                                    <div class="col-md-12">
                                                        <div class="form-group form-group-default disabled required">
                                                            <label> Email </label>
                                                            <input type="email" name="child_emails[{{ $loop->iteration }}]" class="form-control" value="{{ $invitation->child->email }}" readonly />
                                                            <input type="hidden" name="child_invitation[{{ $loop->iteration }}]" class="form-control" value="{{ $invitation->token }}" />
                                                            <input type="hidden" name="amount" @if ($invitation->child->activeLicense()) value="0" @else value="4500" @endif>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                    @endforeach
                                @endif

                                {{-- <div class="m-b-10 clearfix text-right">
                                    <button class="btn btn-primary btn-custom-sm" type="button" id="btnAddChild">
                                        <i class="fa fa-plus" aria-hidden="true"></i> Add Another Child
                                    </button>
                                </div>
                                <div class="row {{ $invitations->isNotEmpty() ? '' : 'd-none' }}" id="rowAmount">
                                    <div class="col-md-12">
                                        <p>Your total billing is: <span id="total-amount" class="bold">-</span></p>
                                        <p class="fs-14 lh-normal mt-2 mb-3"><i>The digital licence(s) you are purchasing are valid for a year from the date of purchase</i></p>
                                    </div>
                                    <div class="col-md-12" id="btn-submit">
                                    </div>
                                    <button id="stripePayBtn" class="btn btn-primary">Add</button>
                                </div>
                            </form> --}}
                        </div>
                    </div>
                </div>
                {{-- <div class="card card-transparent">
                    <div class="card-header bg-transparent no-border" role="tab" id="heading-children">
                        <h5 class="mb-0">
                            <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                            <div class="card-title custom-card-title">
                                @if ($user->has_subscription_access)
                                <a class="" href="{{ url('/#/subscriptions') }}">
                                    Subscriptions
                                </a>
                                @endif
                            </div>
                        </h5>
                    </div>
                </div> --}}
            </div>
        </div>
    </div>
    {{-- <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div id="industries-accordion" class="custom-accordion small-accordion" role="tablist">
                <div class="card card-transparent">
                    <div class="card-header bg-transparent no-border" role="tab" id="heading-industries">
                        <h5 class="mb-0">
                            <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                            <div class="card-title custom-card-title">
                                <a class="oswald uppercase link" data-toggle="collapse" href="#collapse-industries" aria-expanded="false" aria-controls="collapse-industries">
                                    INDUSTRY PREFERENCES
                                </a>
                            </div>
                        </h5>
                    </div>
                    <div id="collapse-industries" class="collapse" role="tabpanel" aria-labelledby="heading-industries" aria-expanded="false" data-parent="#industries-accordion">
                        <div class="card-block">
                            <form action={{ route('parent-industries.store', $user->id) }} method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')
                                <p class="bold text-black">
                                    Tick the industries that you want to hear about.
                                </p>
                                <div class="row m-b-20">
                                    <div class="col-md-8 ">
                                        @foreach ($industrycategories->sortBy('name') as $key => $industrycategory)
                                            <div class="checkbox check-primary">
                                                <input type="checkbox" name="industries[]" value="{{ $industrycategory->id }}" id="industry{{ $industrycategory->id }}" @if ($selectedIndustries->contains($industrycategory->id)) checked @endif>
                                                <label for="industry{{ $industrycategory->id }}">{{ $industrycategory->name }}</label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <button class="btn btn-primary" type="submit">SAVE</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
    @push('scriptslib')
        <script type="text/javascript" src="{{ asset('assets/plugins/switchery/js/switchery.min.js') }}"></script>
        <script src="https://js.stripe.com/v3/"></script>
    @endpush
    @push('scripts')
        <script>
            var userStateId = "{{ $user->state_id }}";
            jQuery(document).ready(function() {
                if (window.location.hash) {
                    jQuery('[data-toggle="collapse"][href="' + window.location.hash + '"]').trigger('click');
                    jQuery('html, body').animate({
                        scrollTop: $(window.location.hash).prop("scrollHeight")
                    }, 500);
                }




                jQuery("select[name=country]").change(function() {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getStates') }}",
                        data: {
                            id: jQuery(this).val(),
                        },
                        success: function(response) {
                            jQuery("select[name=state]").html('<option selected disabled></option>');
                            jQuery("select[name=state]").val('');
                            if (!response || response.length == 0) {
                                jQuery("#state-container").hide();
                                return;
                            }
                            jQuery("#state-container").show();
                            
                            jQuery.each(response, function(i, v) {
                                let selected = v.id == userStateId ? 'selected' : '';
                                jQuery("select[name=state]").append('<option value="' + v.id + '" '+selected+'>' + v.name + '</option>')
                            });

                            jQuery("select[name=state]").trigger("change");
                        },
                        fail: function() {
                            jQuery("select[name=state]").html('<option selected disabled></option>');
                        }
                    });
                });

                jQuery("select[name=country]").trigger("change");

                var elem = document.querySelector('.switchery');

                var switchery = new Switchery(elem, {
                    color: '#fd3806',
                });


                $("#form-personal").validate({
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: {
                            required: true,
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $("input[name='email']").val();
                                    },
                                    id: {{ Auth::id() }},
                                },
                            }
                        },
                        country: 'required',
                        state: {
                            required: function() {
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                        },
                        postcode: 'required',
                    },
                    messages: {
                        email: {
                            remote: "Email already in use!"
                        },
                    }
                });
            });
        </script>

        <script>
            var stripe = Stripe("{{ config('services.stripe.key') }}");
            var checkoutButton = document.getElementById('stripePayBtn');

            // Close Checkout on page navigation:
            window.addEventListener('popstate', function() {
                handler.close();
            });

            function resetNumbering() {
                jQuery('.number').each(function(i) {
                    i++;
                    jQuery(this).text(i);
                });
            }
            resetNumbering();

            function totalAmount() {
                var sum = 0;
                $('input[name=amount]').each(function() {
                    sum += Number($(this).val());
                });
                if (sum) {
                    jQuery('#stripePayBtn').prop('disabled', false).text('Pay With Card');
                } else {
                    jQuery('#stripePayBtn').prop('disabled', false).text('Add');
                }

                return sum;
            }

            function formIsValid() {
                return $("#parentLicenses").valid();
            }

            jQuery(document).ready(function() {
                @if ($invitations->count())
                    var num = {{ $invitations->count() }};
                    amount = totalAmount()/100;
                    jQuery('#total-amount').text('$'+amount);
                @else
                    var num = 0;
                @endif

                jQuery(document).on('click', '.remove', function() {
                    jQuery(this).closest('.card').remove();
                    amount = totalAmount() / 100;
                    jQuery('#total-amount').text('$' + amount);
                    if (num == 0) {
                        jQuery('#rowAmount').addClass('d-none');
                    }
                    resetNumbering();

                });
                var click = 0
                jQuery(document).on('click', '#btnAddChild', function() {
                    num++;
                    click++;
                    remove = '<div class="card-controls"><i role="button" class="fa fa-times text-danger remove"></i></div>'
                    if (click == 1) {
                        jQuery('#rowAmount').removeClass('d-none');
                        remove = ''
                    }
                    jQuery(this).parent().before(
                        '<div class="card"> <div class="card-header"><div class="card-title">Enter Child details</div>' + remove + '</div> <div class="card-block"> <div class="row clearfix"> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> First name </label> <input type="text" class="form-control" name="child_fnames[' + num + ']" /> </div> </div> <div class="col-md-6"> <div class="form-group form-group-default required"> <label> Last name </label> <input type="text" class="form-control" name="child_lnames[' + num + ']" /> </div> </div> </div> <div class="row clearfix"> <div class="col-md-12"> <div class="form-group form-group-default required"> <label> Email </label> <input type="email" class="form-control" name="child_emails[' + num + ']" /> <input type="hidden" name="amount" value="0"> </div> </div> </div> </div> </div>'
                    );
                    // cartAmount = cartAmount + 2000;
                    $('input[name="child_lnames[' + num + ']"]').rules("add", {
                        required: true,
                    });

                    $('input[name="child_fnames[' + num + ']"]').rules("add", {
                        required: true,
                    });

                    $('input[name="child_emails[' + num + ']"]').rules("add", {
                        required: true,
                        email: true,
                        notEqualTo: ['input[type=email]'],
                        remote: {
                            url: "/checkEmail",
                            type: "get",
                            data: {
                                email: function() {
                                    return $('input[name="child_emails[' + num + ']"]').val();
                                }
                            },
                        },
                        messages: {
                            remote: "Email already in use!",
                            notEqualTo: "Email already entered!",
                        }
                    });
                    resetNumbering();
                });
            });

            $(document).ready(function() {

                jQuery(document).on('click', '#removeImg', function() {
                    var answer = confirm('Are you sure you want to remove this?');
                    if (answer) {
                        jQuery('#imgCol').remove();
                        jQuery('#inputCol').removeClass('col-md-8').addClass('col-md-12')
                        jQuery('#current-avatar').val('');
                    } else {
                        //do nothing
                    }
                });

                jQuery.validator.addMethod("notEqualTo", function(value, element, options) {
                    // get all the elements passed here with the same class
                    var elems = $(element).parents('form').find(options[0]);
                    // the value of the current element
                    var valueToCompare = value;
                    // count
                    var matchesFound = 0;
                    // loop each element and compare its value with the current value
                    // and increase the count every time we find one
                    jQuery.each(elems, function() {
                        thisVal = $(this).val();
                        if (thisVal == valueToCompare) {
                            matchesFound++;
                        }
                    });
                    // count should be either 0 or 1 max
                    if (this.optional(element) || matchesFound <= 1) {
                        //elems.removeClass('error');
                        return true;
                    } else {
                        //elems.addClass('error');
                    }
                }, "")

                $("#parentLicenses").validate({
                    submitHandler: function(form, event) {
                        jQuery('#stripePayBtn').prop('disabled', true)
                        if (totalAmount()) {
                            event.preventDefault();
                            var formdata = jQuery("#parentLicenses").serialize();
                            var childcount = jQuery('input[name="amount"][value="4500"]').length;
                            var data = formdata + '&licensecount=' + childcount;
                            axios.post("{{ route('stripe.checkoutsession') }}", data)
                                .then(function(response) {
                                    console.log(response);
                                    if (typeof response.data.id != 'undefined') {
                                        stripe.redirectToCheckout({
                                            // Make the id field from the Checkout Session creation API response
                                            // available to this file, so you can provide it as argument here
                                            // instead of the {CHECKOUT_SESSION_ID} placeholder.
                                            sessionId: response.data.id
                                        }).then(function(result) {
                                            console.log("error")
                                            // jQuery(form).find(":submit").attr('disabled', 'disabled');
                                            //             form.submit();
                                            // If `redirectToCheckout` fails due to a browser or network
                                            // error, display the localized error message to your customer
                                            // using `result.error.message`.
                                        });
                                    }
                                })
                                .catch(function(error) {
                                    console.log(error);
                                });

                            return '';

                            //this runs when the form validated successfully
                        }
                        form.submit();

                    }
                });
                var timer;
                jQuery(document).on('keyup paste change', 'input[name^=child_emails]', function() {
                    var $element = jQuery(this);

                    $element.parent().next('div').remove();
                    $element.removeData("previousValue");
                    jQuery("#parentLicenses").validate().element(this);
                    clearTimeout(timer);
                    timer = setTimeout(function() {
                        jQuery('#btn-submit').empty();
                        if ($element.valid() == true) {
                            jQuery.ajax({
                                type: "GET",
                                url: '/checkStudentExist',
                                data: {
                                    email: $element.val(),
                                },
                                success: function(response) {
                                    if (response.result == "true" && response.license) {
                                        $element.next('input').val(0);
                                        $element.parent().after('<div><p class="px-2 py-1 bold">Looks like your child already has an account with The Careers Department. <span class="text-blue">Great news, you save $45 on check out</span>!</p><div>You will need a parent licence to receive access to:</div><ul class="license-feature-list pl-0 no-list"><li><img src="{{ asset('images/favicon.png') }}"><span>Weekly parent newsletters</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>Your child&#39;s profile and their &#39;game plan&#39;</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>Access to over 500 careers explanation modules</span></li><li><img src="{{ asset('images/favicon.png') }}"><span>40 specialised parent &#39;lessons&#39; including how scaling works, how to use this product with your child and biggest course and industry misconceptions explained.</span></li></ul><p class="bold">Why buy a parent licence if the school has bought their licence? Simple: 90% of students tell us that their parents are the number one influencer. It pays for you to be informed.</p><p>Your child will receive a notification that you&#39;ve created an account.</p></div>')
                                    } else {
                                        $element.next('input').val(4500);
                                    }
                                    amount = totalAmount() / 100;
                                    jQuery('#total-amount').text('$' + amount);
                                }
                            });
                        }
                    }, 700);
                });

                jQuery(document).on('click', '#btnCardEdit', function() {
                    var $this = jQuery(this)
                    jQuery.ajax({
                        url: "{{ route('update.cardsession') }}",
                        success: function(response) {
                            if (typeof response.id != 'undefined') {
                                stripe.redirectToCheckout({
                                    // Make the id field from the Checkout Session creation API response
                                    // available to this file, so you can provide it as argument here
                                    // instead of the {CHECKOUT_SESSION_ID} placeholder.
                                    sessionId: response.id
                                }).then(function(result) {
                                    console.log("error")
                                    // jQuery(form).find(":submit").attr('disabled', 'disabled');
                                    //             form.submit();
                                    // If `redirectToCheckout` fails due to a browser or network
                                    // error, display the localized error message to your customer
                                    // using `result.error.message`.
                                });
                            }
                        },
                        error: function(response) {
                            $this.prop("checked", !$active);
                        }
                    });
                })
            });
        </script>
    @endpush
@endsection
