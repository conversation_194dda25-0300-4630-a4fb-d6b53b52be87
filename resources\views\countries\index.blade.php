@extends('layouts.admin')

@section('pageTitle', 'Countries')

@section('content')
<div class="row">
    <div class="col-lg-12">
        <div class="card card-default">
            <div class="card-header separator">
                <div class="card-title">
                    Countries
                </div>
                <div class="card-controls">
                    <ul>
                        <li>
                            <button
                                type="button"
                                class="btn btn-primary btn-cons add-country"
                            >
                                <i class="fa fa-plus"></i> Add New
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover custom-datatable no-footer" id="countriesTable">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>Name</th>
                                <th>Code</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('modals')
    @include('countries.modals.form')
@endpush

@include('partials.scripts.crud_essentials')
@push('scripts')
<script>
var $modalCountries = $('#modalCountriesCreateEdit');
var $form = $('#countriesForm');

$(function () {
    createTable();
});

$('.add-country').on('click', function () {
    addCountry();
});

$(document).on('click', '.edit-country', function () {
    const id = $(this).data('id');
    if (!id) return;
    
    editCountry(id);
});

$(document).on('click', '.delete-country', function () {
    const id = $(this).data('id');
    if (!id) return;

    let url = "{{ route('countries.destroy', ['country' => ':id']) }}";
    url = url.replace(":id", id);

    $.confirm({
        title: 'Alert!',
        content: 'Are you sure you want to delete this country?',
        buttons: {   
            yes: {
                text: 'Yes',
                btnClass: 'btn-primary',
                keys: ['enter'],
                action: function(){
                    $.ajax({
                        type: "DELETE",
                        url: url,
                        data: { _token: '{{ csrf_token() }}' },
                        success: function (response) {
                            if (response.success) {
                                toastr.success(response.message);
                                $('#countriesTable').DataTable().ajax.reload();
                            } else {
                                toastr.error(response.message);
                            }
                        }, 
                        error: function (xhr) {
                            toastr.error('Error deleting country.');
                        },
                    });
                }
            },
            cancel: function(){}
        }
    });
});

$form.on('submit', function (e) {
    e.preventDefault();
    
    let $submitButton = $modalCountries.find('button[type="submit"]')

    $submitButton.prop('disabled', true).data('original-text', $submitButton.html()).html('Processing...');

    $form.find('.custom-ajax-error').remove();
    $form.find('.is-invalid').removeClass('is-invalid');

    let formData = new FormData(this);

    $.ajax({
        type: "post",
        url: $form.attr('action'),
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
            if (response.success) {
                $modalCountries.modal('hide');
                toastr.success(response.message);
                $form.trigger('reset');
                $('#countriesTable').DataTable().ajax.reload();
            } else {
                toastr.error(response.message);
            }
        },
        error: function(xhr) {
            if (xhr.status === 422) { // Laravel Validation Error
                let errors = xhr.responseJSON.errors;
                $.each(errors, function (field, messages) {
                    let $input = $form.find('[name="' + field + '"]');
                    $input.addClass('is-invalid');
                    $input.after('<span class="text-danger custom-ajax-error">' + messages[0] + '</span>');
                });
            } else {
                toastr.error('Something went wrong, Try again.');
            }
        },
        complete: function () {
            $modalCountries.find('.modal-loading').hide();
            $submitButton.prop('disabled', false).html($submitButton.data('original-text'));
        }
    });
});

function createTable() {
    $('#countriesTable').DataTable({
        dom: 'Blfrtip',
        bLengthChange: true,
        "lengthChange": true,
        "lengthMenu": [
            [10, 25, 50, 100, -1],
            [10, 25, 50, 100, "All"]
        ],
        "bDestroy": true,
        processing: true,
        serverSide: true,
        stateSave: true,
        responsive: true,
        order: [
            [0, "desc"]
        ],
        "ajax": {
            url: "{{ route('countries.index') }}",
        },
        columns: [
            { data: 'id', name: 'id', orderable: true },
            { data: 'name', name: 'name', orderable: true },
            { data: 'code', name: 'code', orderable: true },
            { data: 'action', name: 'action', orderable: false },
        ],
        language: {
            emptyTable: "{{ __('No data available in the table') }}",
            search: "<i class='ti-search'></i>",
            searchPlaceholder: '{{ __('Quick Search') }}',
            paginate: {
                next: "Next",
                previous: "Previous"
            }
        },
        buttons: [
            {
                extend: 'copyHtml5',
                text: '<i class="fa fa-copy"></i>',
                title: $("#logo_title").val(),
                titleAttr: '{{ __('Copy') }}',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o" aria-hidden="true"></i>',
                titleAttr: '{{ __('Excel') }}',
                title: $("#logo_title").val(),
                margin: [10, 10, 10, 0],
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },

            },
            {
                extend: 'csvHtml5',
                text: '<i class="fa fa-download"></i>',
                titleAttr: '{{ __('CSV') }}',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'pdfHtml5',
                text: '<i class="fa fa-file-pdf"></i>',
                title: $("#logo_title").val(),
                titleAttr: '{{ __('PDF') }}',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },
                orientation: 'landscape',
                pageSize: 'A4',
                margin: [0, 0, 0, 12],
                alignment: 'center',
                header: true,
                customize: function (doc) {
                    doc.content[1].table.widths =
                        Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                }
            },
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i>',
                titleAttr: '{{ __('Print') }}',
                title: $("#logo_title").val(),
                exportOptions: {
                    columns: ':not(:last-child)',
                }
            },
        ],
    });
}

function addCountry() {
    let action = "{{ route('countries.store') }}";

    $modalCountries.find('.modal-title').text('Add Country');
    $modalCountries.find('.modal-loading').hide();
    $modalCountries.find('.modal-footer').show();

    $form.trigger('reset');
    $form.attr('action', action);
    $form.find('input[name="_method"]').val('POST');
    $form.show();

    $modalCountries.modal('show');
}

function editCountry(id) {
    let url = "{{ route('countries.show', ':id') }}";
    url = url.replace(':id', id);
    let action = "{{ route('countries.update', ':id') }}";
    action = action.replace(':id', id);
    
    $modalCountries.find('.modal-loading').show();
    $modalCountries.find('.modal-footer').hide();
    $modalCountries.find('.modal-title').text('Edit Country');

    $form.trigger('reset');
    $form.attr('action', action);
    $form.find('input[name="_method"]').val('PUT');
    $form.hide();
    
    $modalCountries.modal('show');

    $.ajax({
        url: url,
        type: 'GET',
        success: function (data) {
            $form.find('input[name="code"]').val(data.code);
            $form.find('input[name="name"]').val(data.name);
            $form.show();
            $modalCountries.find('.modal-footer').show();
        },
        error: function (xhr) {
            toastr.error('Error fetching country data.');
            $modalCountries.modal('hide');
        },
        complete: function () {
            $modalCountries.find('.modal-loading').hide();
        }
    });
}
</script>
@endpush