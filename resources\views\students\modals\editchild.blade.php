<div class="modal fade fill-in" id="modalChildEdit" role="dialog" aria-hidden="true">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        <i class="pg-close">
        </i>
    </button>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-left p-b-5">
                    <span class="semi-bold">
                        Edit Child
                    </span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class=" col-lg-12 ">
                        <!-- START card -->
                        <div class="card card-transparent">
                            <div class="card-block">
                                <form method="POST" action="" id="form-childedit" role="form" autocomplete="off">
                                    {{ csrf_field() }}
                                    <input type="hidden" name="_method" value="PUT">
                                    <input type="hidden" name="id" id="child-id">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>First Name</label>
                                                <input type="text" id="child-firstname" class="form-control" name="firstname" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Last Name</label>
                                                <input type="text" id="child-lastname" class="form-control" name="lastname" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Email</label>
                                                <input type="email" id="child-email" class="form-control" name="email" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Password</label>
                                                <input type="password" id="child-password" class="form-control" name="password" placeholder="Enter only if you want to change" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="stage-col">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Stage of life</label>
                                                <select class="form-control full-width" id="child-stage" name="stage" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value="">Select...</option>
                                                    <option value="school">I'm still in high school</option>
                                                    <option value="done">I've finished high school</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6 d-none" id="what-stage">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Graduated Year</label>
                                                <select class="full-width" name="graduate_year" data-init-plugin="select2" data-placeholder="Select.." id="child-graduate_year">
                                                    <option value="" selected></option>
                                                    @for ($i = 2018; $i <= date('Y'); $i++)
                                                        <option>{{ $i }}</option>
                                                    @endfor
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row d-none" id="school-detail">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Year</label>
                                                <select class="form-control full-width" name="school_year" data-init-plugin="select2" data-placeholder="Select.." id="child-school_year">
                                                    <option value="">Select..</option>
                                                    @foreach ($years as $year)
                                                        <option value="{{ $year->id }}">{{ $year->title }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>What School do you go to?</label>
                                                <input id="child-school_name" type="text" placeholder="Enter school name" class="form-control" name="school_name" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Gender</label>
                                                <select class="full-width" id="child-gender" name="gender" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    <option value="M">Male</option>
                                                    <option value="F">Female</option>
                                                    <option value="O">Other / Prefer not to say</option>
                                                </select>
                                            </div>
                                        </div>
                                         {{-- <div class="col-md-6" id="child_other_gender">
                                            <div class="form-group form-group-default required">
                                                <label>Other Gender</label>
                                                <input type="text" class="form-control other_gender" name="other_gender" />
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Country</label>
                                                <select class="full-width" id="child-country" name="country" data-init-plugin="select2" data-placeholder="Select.." required>
                                                    <option value=""></option>
                                                    @foreach ($countries as $country)
                                                        <option value="{{ $country->id }}"> {{ $country->name }} </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="child-edit-state-container" style="display: none;">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>State</label>
                                                <select id="child-state_id" class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Postcode</label>
                                                <input id="child-postcode" type="number" placeholder="" class="form-control" name="postcode">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        @if (Auth::user()->role->name == 'Admin')
                                            {{-- <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>School</label>
                                                <select class="full-width" id="child-school_id" name="school" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    @foreach ($schools as $key => $school)
                                                    <option value="{{$school->id}}">{{$school->name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div> --}}
                                        @endif
                                    </div>
                                    <div class="clearfix"></div>
                                    <button class="btn btn-primary pull-right" type="submit">
                                        Update
                                    </button>
                                </form>
                            </div>
                        </div>
                        <!-- END card -->
                    </div>
                </div>
            </div>
            <div class="modal-footer"></div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
@push('scripts')
    <script>
        $(function() {
            if ($('#child-stage').val() == 'school') {
                $('#school-detail').removeClass('d-none');
                $('#what-stage').addClass('d-none');
                $('#stage-col').addClass('col-md-12');
                $('#stage-col').removeClass('col-md-6');
                $('#what-stage select').val('').trigger("change.select2");
            } else {
                $('#stage-col').addClass('col-md-6');
                $('#stage-col').removeClass('col-md-12');
                $('#what-stage').removeClass('d-none');
                $('#school-detail').addClass('d-none');
                $('#school-detail select, #school-detail input').val('').trigger("change.select2");
            }
            $('#child-stage').change(function() {
                if ($(this).val() == 'school') {
                    $('#stage-col').addClass('col-md-12');
                    $('#stage-col').removeClass('col-md-6');
                    $('#school-detail').removeClass('d-none');
                    $('#what-stage').addClass('d-none');
                    $('#what-stage select').val('').trigger("change.select2");
                } else {
                    $('#stage-col').addClass('col-md-6');
                    $('#stage-col').removeClass('col-md-12');
                    $('#what-stage').removeClass('d-none');
                    $('#school-detail').addClass('d-none');
                    $('#school-detail select, #school-detail input').val('').trigger("change.select2");
                }
            });
        });
        var validator;
        jQuery(document).ready(function() {

            jQuery("#form-childedit select[name=country]").change(function() {
                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getStates') }}",
                    data: {
                        id: jQuery(this).val(),
                    },
                    success: function(response) {
                        // jQuery("#form-childedit select[name=state]").html('<option value=""></option>');
                        // jQuery.each(response, function(i, v) {
                        //     jQuery("#form-childedit select[name=state]").append('<option value="' + v.id + '">' + v.name + '</option>')
                        // });

                        jQuery("select[name=state]").html('<option selected disabled></option>');
                            // new logic for states - START
                            if (!response || response.length == 0) {
                                jQuery("#child-edit-state-container").hide();
                                return;
                            }
                            jQuery("#child-edit-state-container").show();

                            jQuery.each(response, function(i, v) {
                                 jQuery("#form-childedit select[name=state]").append('<option value="'+v.id+'">'+v.name+'</option>');
                            });

                            jQuery("select[name=state]").trigger("change");
                            // new logic for states - END
                    },
                    fail: function() {
                        jQuery("#form-childedit select[name=state]").html('<option value=""></option>');
                        jQuery("#child-edit-state-container").hide();
                    }
                });
            });

            jQuery('#modalChildEdit').on('show.bs.modal', function(e) {
                jQuery("#form-childedit input:not([type=hidden]):not([type=submit]), #form-childedit select:not([type=hidden]").val('').trigger("change.select2");
                jQuery("#form-childedit select[name=state]").html('<option value=""></option>');
                var $trigger = $(e.relatedTarget);
                jQuery("#form-childedit").attr("action", "children/" + $trigger.data('id'));
                jQuery.ajax({
                    url: "children/" + $trigger.data('id'),
                    dataType: 'json',
                    success: function(data) {
                        jQuery.each(data, function(i, v) {
                            if (i == 'state_id' || i == 'school_id') {
                                var timer;
                                timer = setInterval(function() {
                                    jQuery('#modalChildEdit #child-' + i).val(v);
                                    if (jQuery('#modalChildEdit #child-' + i).hasClass('select2-hidden-accessible')) {
                                        jQuery('#modalChildEdit #child-' + i).trigger("change");
                                    }
                                    clearTimeout(timer);
                                }, 1000);
                            }else if (i == 'gender') {
                                if(v != 'M' && v != 'F'){
                                  jQuery("#child_other_gender").show();
                                  jQuery('#modalChildEdit .other_gender').val(v);
                                  jQuery('#modalChildEdit #child-' + i).val('Other').change();
                                }else{
                                  jQuery("#child_other_gender").hide();
                                  jQuery('#modalChildEdit #child-' + i).val(v).change();
                                }

                            } else if (i == 'school_year') {
                                if (v != 7) {
                                    jQuery('#modalChildEdit #child-stage').val('school').trigger("change");
                                } else {
                                    jQuery('#modalChildEdit #child-stage').val('done').trigger("change");
                                }
                                jQuery('#modalChildEdit #child-' + i).val(v).trigger("change");
                            } else {
                                jQuery('#modalChildEdit #child-' + i).val(v);
                                if (jQuery('#modalChildEdit #child-' + i).hasClass('select2-hidden-accessible')) {
                                    jQuery('#modalChildEdit #child-' + i).trigger("change");
                                }
                            }
                        });
                    }
                });
            });

            // jQuery('#child-gender').on('change', function() {
            //     if (this.value == 'Other') {
            //         jQuery("#child_other_gender").show();
            //     } else {
            //         jQuery("#child_other_gender").hide();
            //         jQuery('#modalChildEdit .other_gender').val('');
            //     }
            // });


            jQuery('#modalChildEdit').on('shown.bs.modal', function(e) {
                validator = jQuery('#form-childedit').validate({
                    errorElement: 'span',
                    errorClass: 'help-block error-help-block',

                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length ||
                            element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            error.insertAfter(element.parent());
                            // else just place the validation message immediatly after the input
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    highlight: function(element) {
                        jQuery(element).closest('.form-group').removeClass('has-success').addClass('has-error'); // add the Bootstrap error class to the control group
                    },

                    focusInvalid: false, // do not focus the last invalid input
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: {
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return jQuery("#child-email").val();
                                    },
                                    id: function() {
                                        return jQuery("#child-id").val();
                                    },
                                },
                            },
                        },
                        // other_gender: {
                        //     required: function() {
                        //         return (jQuery('#child-gender').val() == 'Other');
                        //     }
                        // },
                        password: {
                            minlength: 5,
                        },
                        country:"required",
                        state: {
                        // new logic for states - START
                            required: function() {
                                console.log(jQuery('select[name=state] option[value]').length);
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                        // new logic for states - START
                        },
                        stage: 'required',
                        school_year: {
                            required: function() {
                                return (jQuery('#child-stage').val() == 'school');
                            }
                        },
                        school_name: {
                            required: function() {
                                return (jQuery('#child-stage').val() == 'school');
                            }
                        },
                        graduate_year: {
                            required: function() {
                                return (jQuery('#child-stage').val() == 'done');
                            }
                        },
                    },
                    messages: {
                        email: {
                            remote: 'This email is already in use!'
                        }
                    }
                })
            });

            jQuery("#modalChildEdit").on('hidden.bs.modal', function() {
                jQuery("#form-childedit input:not([type=hidden]):not([type=submit]), #form-childedit select:not([type=hidden]").val('');
                jQuery("#form-childedit select").trigger("change.select2");
                validator.destroy()
            })
        });
    </script>
@endpush
