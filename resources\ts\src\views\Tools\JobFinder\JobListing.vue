<template>
    <div class="position-relative">
        <img :src="'https://picsum.photos/1200/400'" class="w-100" style="height: 40vh; object-fit: cover;" />
        <div class="card p-4 mx-auto position-relative z-1 mb-xxl-8" style="margin-top: -230px; max-width: 95%;">
            <div class="row">
                <div class="col-xl-6 p-20">
                    <div class="card">
                        <div class="card-header border-0">
                            <div class="card-title">
                                <h1>IT Internship</h1>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <p class="fs-1">Nexora Systems</p>

                            <h3>Work Experience Opportunity – IT Support for Defence Project Canberra, ACT | 6-Week
                                Program | Onsite Placement</h3>

                            <p class="fs-4">Are you a student or recent graduate looking to gain hands-on IT experience
                                in a meaningful, real-world environment?
                            </p>

                            <p class="fs-4">
                                We’re offering a 6-week work experience placement for motivated individuals to join a
                                team supporting critical systems within a major defence project.
                            </p>
                            <p>
                            </p>
                            <ul>
                                <h3>What You’ll Gain:</h3>
                                <li class="fs-4">
                                    Exposure to a fast-paced IT operations environment working on production support
                                    systems
                                </li>
                                <li class="fs-4">
                                    Practical experience in incident response, system monitoring, and troubleshooting
                                </li>

                                <li class="fs-4">
                                    Mentoring and guidance from experienced IT professionals
                                </li>

                                <li class="fs-4">
                                    A behind-the-scenes look at the technology supporting national defence initiatives
                                </li>
                            </ul>

                            <ul>
                                <h3>Program Details:</h3>
                                <li class="fs-4">Location: ACT – Onsite placement</li>
                                <li class="fs-4">Duration: 6 weeks</li>
                                <li class="fs-4">Eligibility: Australian Citizenship required (must be eligible to
                                    obtain NV1 security clearance)
                                </li>
                            </ul>
                            <ul>
                                <h3>Who This Is For:</h3>

                                <li class="fs-4">Students or recent graduates (0–2 years’ experience) in IT, computer
                                    science, or related fields</li>
                                <li class="fs-4">
                                    Individuals eager to build practical skills and contribute to meaningful projects
                                </li>
                                <li class="fs-4">Strong communicators with a willingness to learn and solve problems
                                </li>
                            </ul>
                            <p class="fs-4">This is a unique opportunity to build your experience, network with
                                professionals, and explore a potential future in IT within the defence sector.</p>
                            <p class="fs-4">
                                To apply, please click the ‘Apply Now’ button at the top of this page. </p>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 p-20">

                    <div class="card">

                        <div class="card-header border-0 p-0 justify-content-end">

                            <div class="card-title ">

                                <button class=" border-0 px-14 py-4 rounded  fs-6 bg-grey transition">
                                    save

                                </button>

                                <button class=" border-0 px-14 py-4 rounded ms-5 fs-6 bg-grey transition">
                                    Apply Now

                                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1.3125 7.00042C1.3125 6.88439 1.3553 6.77311 1.43149 6.69106C1.50767 6.60902 1.61101 6.56292 1.71875 6.56292L11.3006 6.56292L8.74363 3.81017C8.66734 3.72802 8.62449 3.6166 8.62449 3.50042C8.62449 3.38425 8.66734 3.27282 8.74363 3.19067C8.81991 3.10852 8.92337 3.06237 9.03125 3.06237C9.13913 3.06237 9.24259 3.10852 9.31887 3.19067L12.5689 6.69067C12.6067 6.73131 12.6367 6.77959 12.6572 6.83275C12.6777 6.8859 12.6882 6.94288 12.6882 7.00042C12.6882 7.05797 12.6777 7.11495 12.6572 7.1681C12.6367 7.22126 12.6067 7.26953 12.5689 7.31017L9.31888 10.8102C9.24259 10.8923 9.13913 10.9385 9.03125 10.9385C8.92337 10.9385 8.81991 10.8923 8.74363 10.8102C8.66734 10.728 8.62449 10.6166 8.62449 10.5004C8.62449 10.3842 8.66734 10.2728 8.74363 10.1907L11.3006 7.43792L1.71875 7.43792C1.61101 7.43792 1.50767 7.39183 1.43149 7.30978C1.3553 7.22774 1.3125 7.11646 1.3125 7.00042Z" fill="#606060" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="card-body border">

                            <h1>Details</h1>

                            <div class="location d-flex justify-content-start">

                                <svg class="me-4 mt-1" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8 16C8 16 14 10.314 14 6C14 4.4087 13.3679 2.88258 12.2426 1.75736C11.1174 0.632141 9.5913 0 8 0C6.4087 0 4.88258 0.632141 3.75736 1.75736C2.63214 2.88258 2 4.4087 2 6C2 10.314 8 16 8 16ZM8 9C7.20435 9 6.44129 8.68393 5.87868 8.12132C5.31607 7.55871 5 6.79565 5 6C5 5.20435 5.31607 4.44129 5.87868 3.87868C6.44129 3.31607 7.20435 3 8 3C8.79565 3 9.55871 3.31607 10.1213 3.87868C10.6839 4.44129 11 5.20435 11 6C11 6.79565 10.6839 7.55871 10.1213 8.12132C9.55871 8.68393 8.79565 9 8 9Z" fill="#282828" />
                                </svg>

                                <p class="fs-4"> Canberra</p>
                            </div>

                            <div class="time d-flex justify-content-start ">

                                <svg class="me-4 mt-1" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_1860_1618)">
                                        <path d="M16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8ZM8 3.5C8 3.36739 7.94732 3.24021 7.85355 3.14645C7.75979 3.05268 7.63261 3 7.5 3C7.36739 3 7.24021 3.05268 7.14645 3.14645C7.05268 3.24021 7 3.36739 7 3.5V9C7.00003 9.08813 7.02335 9.17469 7.06761 9.25091C7.11186 9.32712 7.17547 9.39029 7.252 9.434L10.752 11.434C10.8669 11.4961 11.0014 11.5108 11.127 11.4749C11.2525 11.4391 11.3591 11.3556 11.4238 11.2422C11.4886 11.1288 11.5065 10.9946 11.4736 10.8683C11.4408 10.7419 11.3598 10.6334 11.248 10.566L8 8.71V3.5Z" fill="#282828" />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_1860_1618">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>

                                <p class="fs-4"> 6 weeks · Part-Time</p>
                            </div>

                            <div class="money d-flex justify-content-start ">

                                <svg class="me-4 mt-1" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0 4C0 3.73478 0.105357 3.48043 0.292893 3.29289C0.48043 3.10536 0.734784 3 1 3H15C15.2652 3 15.5196 3.10536 15.7071 3.29289C15.8946 3.48043 16 3.73478 16 4V12C16 12.2652 15.8946 12.5196 15.7071 12.7071C15.5196 12.8946 15.2652 13 15 13H1C0.734784 13 0.48043 12.8946 0.292893 12.7071C0.105357 12.5196 0 12.2652 0 12V4ZM3 4C3 4.53043 2.78929 5.03914 2.41421 5.41421C2.03914 5.78929 1.53043 6 1 6V10C1.53043 10 2.03914 10.2107 2.41421 10.5858C2.78929 10.9609 3 11.4696 3 12H13C13 11.4696 13.2107 10.9609 13.5858 10.5858C13.9609 10.2107 14.4696 10 15 10V6C14.4696 6 13.9609 5.78929 13.5858 5.41421C13.2107 5.03914 13 4.53043 13 4H3Z" fill="#282828" />
                                    <path d="M8 10C8.53043 10 9.03914 9.78929 9.41421 9.41421C9.78929 9.03914 10 8.53043 10 8C10 7.46957 9.78929 6.96086 9.41421 6.58579C9.03914 6.21071 8.53043 6 8 6C7.46957 6 6.96086 6.21071 6.58579 6.58579C6.21071 6.96086 6 7.46957 6 8C6 8.53043 6.21071 9.03914 6.58579 9.41421C6.96086 9.78929 7.46957 10 8 10Z" fill="#282828" />
                                </svg>
                                <p class="fs-4"> $35/hr</p>
                            </div>
                            <p class="fs-1">Posted 2 days ago</p>
                            <h2>About us</h2>
                            <p class="fs-4">
                                Nexora Systems is a Canberra-based IT solutions provider specialising in secure
                                infrastructure, application support, and digital transformation within government and
                                defence sectors. With a focus on reliability, innovation, and national impact, Nexora
                                empowers organisations through tailored technology solutions and expert support teams.
                            </p>
                            <h2>Resources</h2>
                            <div class="button d-flex flex-column w-xl-50">
                                <button class=" border-0 px-14 py-4 rounded mb-5 fs-6 bg-grey transition">
                                    Program Guidelines
                                    <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.8125 7.00042C0.8125 6.88439 0.855301 6.77311 0.931488 6.69106C1.00767 6.60902 1.11101 6.56292 1.21875 6.56292L10.8006 6.56292L8.24363 3.81017C8.16734 3.72802 8.12449 3.6166 8.12449 3.50042C8.12449 3.38425 8.16734 3.27282 8.24363 3.19067C8.31991 3.10852 8.42337 3.06237 8.53125 3.06237C8.63913 3.06237 8.74259 3.10852 8.81887 3.19067L12.0689 6.69067C12.1067 6.73131 12.1367 6.77959 12.1572 6.83275C12.1777 6.8859 12.1882 6.94288 12.1882 7.00042C12.1882 7.05797 12.1777 7.11495 12.1572 7.1681C12.1367 7.22126 12.1067 7.26953 12.0689 7.31017L8.81888 10.8102C8.74259 10.8923 8.63913 10.9385 8.53125 10.9385C8.42337 10.9385 8.31991 10.8923 8.24363 10.8102C8.16734 10.728 8.12449 10.6166 8.12449 10.5004C8.12449 10.3842 8.16734 10.2728 8.24363 10.1907L10.8006 7.43792L1.21875 7.43792C1.11101 7.43792 1.00767 7.39183 0.931488 7.30978C0.855301 7.22774 0.8125 7.11646 0.8125 7.00042Z" fill="#606060" />
                                    </svg>
                                </button>
                                <button class=" border-0 px-14 py-4 rounded  fs-6 bg-grey transition">
                                    Download Application Form
                                    <svg width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M-9.53674e-07 12L-7.94466e-08 2L4 2L4 3L1 3L0.999999 11L10 11L10 3L7 3L7 2L11 2L11 12L-9.53674e-07 12ZM6 5.5L8 5.5L5.5 8.5L3 5.5L5 5.5L5 4.29138e-07L6 5.1656e-07L6 5.5Z" fill="#55576A" />
                                    </svg>
                                </button>
                            </div>

                        </div>
                    </div>

                </div>

            </div>

        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent } from "vue";

    export default defineComponent({
        name: "JobListing",
    });
</script>
<style scoped></style>