<?php

namespace App\Http\Controllers;

use App\Events\TeacherImportCompleted;
use App\Country;
use App\Events\LicenseRenewed;
use App\Http\Requests\StoreTeacher;
use App\Http\Requests\TeacherImportRequest;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Mail\TeacherWelcomeMail;
use App\Position;
use App\Role;
use App\School;
use App\Teacher;
use App\User;
use App\Profile;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\TeacherImport;
use Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use App\Exports\TeachersExport;
use App\Jobs\NotifyUserOfCompletedTeachersExport;

class TeachersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index()
    {
        //  $teachers = Teacher::all();
        //   foreach ($teachers as $teacher) {
        //       if (count($teacher->positions)) {
        //         $profile = Profile:: where('user_id', $teacher->id)->update([
        //           "position" => $teacher->positions[0]->name
        //       ]);
        //     }
        // }
        // $positions = Position::select('id', 'name')->get();
        $states = Country::find(1)->states()->get();
        $countries = Country::all();
        if (Auth::user()->isTeacher()) {
            // $campuses = Auth::user()->school->campuses()->get();
            $campuses = Auth::user()->campuses()->get();
            // dd($campuses);
        }
        if (Auth::user()->isAdmin()) {
            $schools = School::confirmed()->select('id', 'name')->get();
            return view('teachers.index', compact('schools', 'states', 'countries'));
        } else {
            return view('teachers.teachers.index', compact('states', 'countries', 'campuses'));
        }
    }
    public function downloadsample($campus = null)
    {
        // if ($campus) {
        //     return Storage::download('teacherssamplewithcampus.xlsx');
        // }
        // return Storage::download('teacherssample.xlsx');
        if ($campus) {
            return Storage::download('samples/csv/TeacherImportCampus.csv');
        }
        return Storage::download('samples/csv/TeacherImport.csv');
    }
    public function import(Request $request)
    {
        $schools = School::confirmed()->select('id', 'name')->get();
        return view('teachers.import', compact('schools'));
    }
    public function importTeachers(TeacherImportRequest $request)
    {
        if ($request->hasFile('teachersfile')) {
            $exceldirectory = "excel";
            if (!Storage::exists($exceldirectory)) {
                Storage::makeDirectory($exceldirectory, 0777);
            }
            if (Auth::user()->isTeacher()) {
                $school = Auth::user()->school_id;
            } else {
                $school = $request->school;
            }
            if (!School::whereId($school)->exists()) {
                return back()->with('message', 'Invalid school selected for import.');
            }
            Excel::import(new TeacherImport($school, Auth::id(), []), $request->file('teachersfile'));
            return redirect()->route("teachers.index")->with("message", "We are processing your import. You will get notified once this is completed and these accounts will each receive an email with their login details.");
        }
        return redirect()->route("teachers.index")->with("message", "Please make sure your uploaded file matches the sample file format");
    }
    public function getAdvanceFilterData(Request $request)
    {
        $teachers = Teacher::select('users.id', 'users.school_id', 'users.name', 'users.email', 'users.state_id', 'users.country_id', 'users.created_at', 'profiles.firstname as firstname', 'profiles.lastname as lastname', 'profiles.access as access','countries.name as country_name','countries.code as country_code')
            ->join('profiles', 'users.id', '=', 'profiles.user_id')
            ->leftJoin('countries', 'users.country_id', '=', 'countries.id')
            ->with('school:id,name,state_id', 'school.state:id,code', 'school.detail:school_id,type,gender,subscription_ending_on', 'state.country', 'positions:id,name')->with('sessionStats');
        // $teachers = Teacher::with('positions', 'school:id,name,state_id', 'school.state:id,code', 'school.detail:school_id,type,gender', 'state.country', 'profile:user_id,access,position')/* ->get() */;
        if (Auth::user()->isTeacher()) {
            $campus = false;
            $campuses = Auth::user()->campuses()->pluck('campus_id');
            if ($campuses->count()) {
                if (request('campus')) {
                    $campus = array(request('campus'));
                } else {
                    $campus = $campuses;
                }
            }
            $teachers->where("users.id", "<>", Auth::id())->where('school_id', Auth::user()->school_id)
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if (request('campus')) {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas('campuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('campuses');
                        }
                    });
                })
                ->when(session('schoolSection') == 'primary', function ($query) {
                    $query->whereHas("profile", function ($query) {
                        $query->where('primary_section', true);
                    });
                })
                ->when(session('schoolSection') == 'secondary', function ($query) {
                    $query->whereHas("profile", function ($query) {
                        $query->where('secondary_section', true);
                    });
                });
        }
        $datatable = Datatables::of($teachers)
            ->filter(function ($query) {
                if ($name = request('name')) {
                    $query->where('users.name', 'like', "%{$name}%");
                }
                if ($school = request('school')) {
                    $query->where('school_id', $school);
                }

                if (request('campus')) {
                    $query->whereHas('campuses', function ($q) {
                        $q->where('campus_id', request('campus'));
                    });
                }
                if ($state = request('state')) {
                    $query->whereHas('school', function ($q) use ($state) {
                        $q->whereStateId($state);
                    });
                }
                if ($type = request('type')) {
                    $query->whereHas('school', function ($q) use ($type) {
                        $q->whereHas('detail', function ($q2) use ($type) {
                            $q2->whereType($type);
                        });
                    });
                }
                if ($gender = request('gender')) {
                    $query->whereHas('school', function ($q) use ($gender) {
                        $q->whereHas('detail', function ($q2) use ($gender) {
                            $q2->whereGender($gender);
                        });
                    });
                }
                if (request('active_inactive') == 'Active') {
                    $query->whereHas('school', function ($q) {
                        $q->whereHas('detail', function ($q2) {
                            $q2->whereDate('subscription_ending_on', '>=', Carbon::now());
                        });
                    });
                } elseif (request('active_inactive') == 'Inactive') {
                    $query->whereHas('school', function ($q) {
                        $q->whereHas('detail', function ($q2) {
                            $q2->whereDate('subscription_ending_on', '<=', Carbon::now());
                        });
                    });
                }
            }, true);

        // return $datatable->addColumn('action', '@if ($profile->access == "Full") <a href="teachers/{{$id}}" data-method="delete" data-token="{{csrf_token()}}" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a> @endif<a data-target="#modalTeacherEdit" data-id="{{$id}}" data-toggle="modal" id="btnFillSizeToggler" class="" href="#"> <i class="fa fa-edit"></i></a> <a href="teachers/{{$id}}" data-method="delete" data-token="{{csrf_token()}}" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>')
        return $datatable
            ->addColumn('action', function (Teacher $user) {
                // if ($user->profile->access == "Full") {
                return '<a href="teachers/' . $user->id . '/invite"> <i class="fa fa-paper-plane-o text-danger" data-toggle="tooltip" title="Resend welcome email with account details."></i></a><a data-target="#modalTeacherEdit" data-id="' . $user->id . '" data-toggle="modal" id="btnFillSizeToggler" class="" href="#"> <i class="fa fa-edit"></i></a> <a href="teachers/' . $user->id . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
                // }
                // return '<a data-target="#modalTeacherEdit" data-id="'.$user->id.'" data-toggle="modal" id="btnFillSizeToggler" class="" href="#"> <i class="fa fa-edit"></i></a> <a href="teachers/'.$user->id.'" data-method="delete" data-token="'.csrf_token().'" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
            })
            ->addColumn('access_level', function (Teacher $user) {
                return $user->profile->access_level;
            })
            ->addColumn('registration_date', function (Teacher $user) {
                return Carbon::parse($user->created_at)->toFormattedDateString();
            })
            ->addColumn('subscription_end_date', function (Teacher $user) {
                if ($user->school->detail->subscription_ending_on) {
                    return Carbon::parse($user->school->detail->subscription_ending_on)->toFormattedDateString();
                }
                return '';
            })
            ->addColumn('campuses', function (Teacher $user) {
                $data = '';
                if ($user->campuses()->exists()) {
                    foreach ($user->campuses as $campus) {
                        $data .= '<span class="label">' . $campus->name . '</span>';
                    }
                } else {
                    $data .= 'Unassigned';
                }
                return $data;
            })
            ->addColumn('lastseen', function (Teacher $user) {
                // return $user->last_seen;
                return $user->sessionStats ? Carbon::parse($user->sessionStats->last_seen)->toDayDateTimeString() : '-';
            })
            ->addColumn('logincount', function (Teacher $user) {
                // return $user->login_count;
                return $user->sessionStats ? $user->sessionStats->login_count : '-';
            })
            ->addColumn('avgduration', function (Teacher $user) {
                // return $user->avg_session_duration ? CarbonInterval::seconds(round($user->avg_session_duration))->cascade()->forHumans(['short' => true]) : '-';
                return $user->sessionStats ? CarbonInterval::seconds(round($user->sessionStats->avg_duration))->cascade()->forHumans(['short' => true]) : '-';
            })
            ->rawColumns(['action', 'registration_date', 'campuses', 'subscription_end_date', 'lastseen', 'logincount', 'avgduration'])
            ->make(true);
        // ->ToJson();
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreTeacher $request)
    {

        $roleid = Role::where('name', "Teacher")->value('id');

        if (Auth::user()->isTeacher()) {
            $school = Auth::user()->school_id;
            $state = School::where('id', $school)->value('state_id');
            if (Auth::user()->campuses()->count() > 1) {
                $campuses = request('campuses');
            } else {
                $campuses = Auth::user()->campuses()->pluck('campus_id');
            }
        } else {
            $school = request('school');
            $state = School::where('id', $school)->value('state_id');
            $campuses = request('campuses');
        }

        $position = Position::find(request('position'));
        if (!$position) {
            $position = Position::create(['name' => request('position')]);
        }

        $password = request('password') ?? rand(20000, 999909999);
        $teacher = new Teacher;
        $teacher->name = request('firstname') . ' ' . request('lastname');
        $teacher->email = request('email');
        $teacher->role_id = $roleid;
        $teacher->password = bcrypt($password);
        $teacher->school_id = $school;
        $teacher->country_id = 1;
        $teacher->state_id = $state;
        $teacher->postcode = request('postcode');
        $teacher->created_at = date('Y-m-d H:i:s');
        $teacher->updated_at = date('Y-m-d H:i:s');

        $profile = new Profile;
        $profile->firstname = request('firstname');
        $profile->lastname = request('lastname');
        $profile->access = request('access');
        if (Auth::user()->isTeacher() && !Auth::user()->school->detail->hasFullSchoolAccess()) {
            if (Auth::user()->school->detail->hasSecondarySchoolAccess()) {
                $secondary_section = true;
                $primary_section = false;
            } else {
                $secondary_section = false;
                $primary_section = true;
            }
        } elseif (Auth::user()->isTeacher()) {
            $secondary_section = session('schoolSection') == 'secondary' ? true : false;
            $primary_section = session('schoolSection') == 'primary' ? true : false;
        } else {
            $secondary_section = request('secondary_section') ? true : false;
            $primary_section = request('primary_section') ? true : false;
        }
        $profile->secondary_section = $secondary_section;
        $profile->primary_section = $primary_section;
        // $profile->newsletter = request('newsletter') ? true : false;
        $profile->position = request('position');
        $profile->accountcreated = true;
        $teacher->save();
        $teacher->profile()->save($profile);

        $teacher->positions()->attach($position->id);
        $teacher->campuses()->attach($campuses);
        $teacher->password = $password;

        \Event::dispatch(new LicenseRenewed(User::where("id", $teacher->id)->first()));

        $emailData = collect();
        $emailData->teacher = $teacher;
        $emailData->adminTeachers = Teacher::where("school_id", $teacher->school->id)->where('id', '<>', $teacher->id)
            ->whereHas('profile', function ($query) {
                $query->whereIn('access',  ['Full', 'Lead Administrator']);
            })->pluck('name');
        Mail::to($teacher->email)->send(new TeacherWelcomeMail($emailData));

        // User::addHelpcrunchAccount($teacher->id);


        $user = User::find($teacher->id);
        // UpdateMailchimpAudienceJob::dispatch($user);
        // $redirectUrl = url()->previous();
        // if($redirectUrl == url('updateUserSession') || strpos($redirectUrl,'api') !== false) {
        //     $redirectUrl = session()->get('previousUrl');
        // }
        // return redirect($redirectUrl);

        if (Auth::user()->isTeacher()) {
            return redirect()->route('teachers.index')->with('message', 'Teacher has been added successfully. They will now receive an automatic welcome email with their account details.');
        } else {
            return redirect()->route('teachers.index')->with('message', 'Teacher has been added successfully!');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Teacher  $teacher
     * @return \Illuminate\Http\Response
     */
    public function show(Teacher $teacher)
    {
        $teacher->load('school.state.country');
        $teacher->firstname = $teacher->profile->firstname;
        $teacher->lastname = $teacher->profile->lastname;
        $teacher->access = $teacher->profile->access;
        $teacher->secondary_section = $teacher->profile->secondary_section;
        $teacher->primary_section = $teacher->profile->primary_section;
        // $teacher->country = @$teacher->state->country->id;
        $teacher->country = @$teacher->country_id;
        $teacher->campusIds = $teacher->campuses()->pluck('campus_id');
        $teacher->position_id = @$teacher->positions->first()->id;
        $teacher->teacher_position = @$teacher->positions->first()->name;
        return $teacher;
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Teacher  $teacher
     * @return \Illuminate\Http\Response
     */
    public function edit(Teacher $teacher) {}

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Teacher  $teacher
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());
        $teacher = Teacher::findOrFail($id);
        $oldEmail = $teacher->email;
        $teacher->name = request('firstname') . ' ' . request('lastname');
        $teacher->email = (request('email')) ? request('email') : str_random(40);
        if (request('password')) {
            $teacher->password = bcrypt(request('password'));
        }
        if (Auth::user()->isTeacher()) {
            $school = Auth::user()->school_id;
            // if (Auth::user()->campuses()->count() > 1) {
            //     $campuses = request('campuses');
            // } else {
            //     $campuses = Auth::user()->campuses()->pluck('campus_id');
            // }
        } else {
            $school = request('school');
        }
        $campuses = request('campuses');

        $position = Position::find(request('position'));

        if (!$position) {
            $position = Position::create(['name' => request('position')]);
        }

        if ($teacher->school_id != $school) {
            Cache::forget('schoolPlans' . $teacher->school_id);
        }


        $teacher->school_id = $school;
        // $teacher->state_id = request('state') ?: $teacher->state_id;
        $teacher->state_id = request('state');
        $teacher->country_id = request('country');
        // $teacher->state_id = request('state');
        $teacher->postcode = request('postcode');
        // $teacher->updated_at = date('Y-m-d H:i:s');

        $profile = $teacher->profile;
        $profile->firstname = request('firstname');
        $profile->lastname = request('lastname');
        $profile->access = request('access');
        if (Auth::user()->isTeacher() && !Auth::user()->school->detail->hasFullSchoolAccess()) {
            if (Auth::user()->school->detail->hasSecondarySchoolAccess()) {
                $secondary_section = true;
                $primary_section = false;
            } else {
                $secondary_section = false;
                $primary_section = true;
            }
        } elseif (Auth::user()->isTeacher()) {
            $secondary_section = session('schoolSection') == 'secondary' ? true : false;
            $primary_section = session('schoolSection') == 'primary' ? true : false;
        } else {
            $secondary_section = request('secondary_section') ? true : false;
            $primary_section = request('primary_section') ? true : false;
        }
        $profile->secondary_section = $secondary_section;
        $profile->primary_section = $primary_section;
        // $profile->newsletter = request('newsletter') ? true : false;
        $profile->position = request('position');
        $teacher->save();
        $teacher->profile()->save($profile);

        $teacher->positions()->sync($position->id);
        $teacher->campuses()->sync($campuses);
        \Event::dispatch(new LicenseRenewed(User::where("id", $teacher->id)->first()));

        Cache::tags(['userhasFullAccess', 'hasFullAccess.' . $teacher->id])->flush();
        Cache::tags(['userhasManagerAccess', 'hasManagerAccess.' . $teacher->id])->flush();
        Cache::forget('schoolPlans' . $teacher->school_id);

        // User::addHelpcrunchAccount($teacher->id);

        // if ($oldEmail != $request->email) {
        //     Newsletter::updateEmailAddress($oldEmail, $request->email, 'teachers');
        // }

        $user = User::find($teacher->id);
        // UpdateMailchimpAudienceJob::dispatch($user);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Teacher has been updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Teacher  $teacher
     * @return \Illuminate\Http\Response
     */
    public function destroy(Teacher $teacher)
    {
        if (Auth::user()->isTeacher()) {
            $teacher = Teacher::fellowTeachers()->find($teacher->id);
        }
        $delete = $teacher->delete();

        if ($delete) {
            User::deleteHelpcrunchAccount($teacher->id);
        }

        if (request()->wantsJson()) {
            return response([], 204);
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Teacher has been deleted successfully!');
    }
    public function reSendInvitation($id)
    {
        $teacher = Teacher::with('profile:user_id,firstname')->findOrFail($id);
        $emailData = collect();
        $emailData->teacher = $teacher;
        $emailData->adminTeachers = Teacher::where("school_id", $teacher->school->id)->where('id', '<>', $teacher->id)
            ->whereHas('profile', function ($query) {
                $query->whereIn('access',  ['Full', 'Lead Administrator']);
            })->pluck('name');
        Mail::to($teacher->email)->send(new TeacherWelcomeMail($emailData));
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Automated welcome email with account details has been resent successfully.');
    }

    public function editTeachers(Request $request)
    {
        if ($request->withselected == "Reset Profiling") {
            $message = 'Students profile reset successfully!';
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', $message);
    }

    public function export(Request $request)
    {
        $filename = 'exports/teachers-' . time() . '.xlsx';
        $user = Auth::user();
        (new TeachersExport($request->all(), $user))->queue($filename)->chain([
            new NotifyUserOfCompletedTeachersExport(request()->user(), $filename),
        ]);

        return redirect('/teachers')->with('message', "Export is in process, you will get an email with download link once it is ready.");
    }
}
