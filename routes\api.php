<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

use App\Http\Controllers\Api\CountryController;
use App\Http\Controllers\Api\IntercomController;
use App\Http\Controllers\Vue\CompanyPagesController;
use App\Http\Controllers\Vue\BadgesController;

require_once __DIR__ . '/api_account.php';

Route::middleware('auth')->group(function () {
    Route::prefix('v1')->group(function () {
        Route::get('subject-selections-quiz', "SsqController@index");
        Route::get('subject-selections-quiz/status', "SsqController@status");
        Route::get('subject-selections-quiz/statistics', "SsqController@stats");

        Route::get('subject-selections-quiz/school-rules', "SsqSchoolRulesController@get");

        Route::get('subject-selections-quiz/school-guides', "SsqSchoolQuidesController@get");
        Route::post('subject-selections-quiz/school-guides', "SsqSchoolQuidesController@store");
        Route::delete('subject-selections-quiz/school-guides/{id}', "SsqSchoolQuidesController@delete");

        Route::get('subject-selections-quiz/inputs', "SsqInputsController@get");
        Route::post('subject-selections-quiz/inputs', "SsqInputsController@store");

        Route::post('subject-selections-quiz/send-email', "SsqEmailController@sendEmail");

        // Student Preferences
        Route::get('subject-preferences', "UserSubjectsController@get");
        Route::post('subject-preferences', "UserSubjectsController@store");
        Route::delete('subject-preferences/{id}', "UserSubjectsController@delete");

        Route::get('subject-types', "SubjectTypesController@getStateSubjectTypes");

        // Student rule
        Route::get('students/{studentId}/rules', "StudentRuleController@get");
        Route::post('students/{studentId}/rules', "StudentRuleController@store");

        // Subjects
        Route::get('subjects', "SubjectsController@getSubjects");
        Route::get('subjects/{id}', "SubjectsController@getSubject");
        Route::patch('subjects/{id}', "SubjectsController@update");

        // School CSV Reports
        Route::get('schools/{id}/reports/subjects', "SchoolReportController@subjectsExport");
        Route::get('schools/{id}/reports/students', "SchoolReportController@studentsExport");
        Route::get('schools/{id}/reports/subject-areas', "SchoolReportController@subjectAreasExport");
        Route::get('schools/{id}/reports/subject-types', "SchoolReportController@subjectTypesExport");

        // Subject Gallery
        Route::get('subjects/{id}/gallery', "SubjectGalleryController@get");
        Route::post('subjects/{id}/gallery', "SubjectGalleryController@store");
        Route::patch('subjects/{subjectid}/gallery/{galleryItemUpdate}', "SubjectGalleryController@update");
        Route::post('subjects/{subjectId}/gallery/{galleryItemUpdate}/edit', "SubjectGalleryController@updateImage");
        Route::post('subjects/{id}/gallery/upload-image', "SubjectGalleryController@uploadImage");
        Route::post('subjects/{id}/gallery/order', "SubjectGalleryController@reorder");
        Route::delete('subjects/{id}/gallery', "SubjectGalleryController@delete");

        // School subjects
        Route::get('schools/{id}/subjects', "SchoolSubjectController@get");
        Route::post('schools/{id}/subjects', "SchoolSubjectController@store");
        Route::delete('schools/{schooldId}/subjects/{subjectId}', "SchoolSubjectController@delete");
        Route::get('schools/{schooldId}/subjects/export', "SchoolSubjectController@export");
        Route::get('schools-subjects-filters', "SchoolSubjectController@filters");

        // School rules
        Route::get('schools/{schooldId}/rules', 'SchoolRuleController@get');
        Route::post('schools/{schoolId}/rules', 'SchoolRuleController@store');
        Route::delete('schools/{schoolId}/rules/{ruleId}', 'SchoolRuleController@delete');
        Route::patch('schools/{schoolId}/rules/{ruleId}', 'SchoolRuleController@update');

        Route::get('students', "StudentsController@get");
        Route::get('getYears', "StudentsController@getYears");
        Route::get('getCampuses',"StudentsController@getCampuses");
        Route::get('getAllCourseType', "SsqController@getAllCourseType");
        Route::get('getAllSubjectType',"SsqController@getAllSubjectType");
    });

    Route::get('/intercom-status', [IntercomController::class, 'status'])->name('intercom-status');

    // Company Pages API Routes
     Route::prefix('company-pages')->group(function () {
        Route::get('/', [CompanyPagesController::class, 'index']);
        Route::post('/', [CompanyPagesController::class, 'store']);
        Route::get('/{companyPage}', [CompanyPagesController::class, 'show']);
        Route::put('/{companyPage}', [CompanyPagesController::class, 'update']);
        Route::delete('/{companyPage}', [CompanyPagesController::class, 'destroy']);
        Route::post('/reorder', [CompanyPagesController::class, 'reorder']);
    });
});

Route::post('/verify-credential', [BadgesController::class, 'verifyCredential'])->name('verify-credential');
Route::get('countries', [CountryController::class, 'index'])->name('api.countries.index');
