<?php

namespace App\Http\Controllers;

use App\Country;
use App\Events\LicenseRenewed;
use App\Http\Requests\StoreStaff;
use App\Mail\StaffWelcomeMail;
use App\Position;
use App\Role;
use App\Organisation;
use App\Staff;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use DataTables;
use Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index()
    {
        $organisations = Organisation::confirmed()->select('id', 'name')->get();
        $positions = Position::select('id', 'name')->get();
        $states = Country::find(1)->states()->get();
        $countries = Country::all();

        if (Auth::user()->isStaff()) {
            $campuses = Auth::user()->orgCampuses()->get();
        }
        if (Auth::user()->isAdmin()) {
            return view('staff.index', compact('organisations', 'positions', 'states', 'countries'));
        } else {
            return view('staff.staff.index', compact('positions', 'states', 'countries', 'campuses'));
        }
    }
    public function downloadsample($campus = null)
    {
        if ($campus) {
            return Storage::download('teacherssamplewithcampus.xlsx');
        }
        return Storage::download('teacherssample.xlsx');
    }
    // public function import(Request $request)
    // {
    //     $schools = School::confirmed()->select('id', 'name')->get();
    //     return view('teachers.import', compact('schools'));
    // }
    // public function importTeachers(TeacherImportRequest $request)
    // {
    //     if ($request->hasFile('teachersfile')) {
    //         $exceldirectory = "excel";
    //         if (!Storage::exists($exceldirectory)) {
    //             Storage::makeDirectory($exceldirectory, 0777);
    //         }
    //         if (Auth::user()->isStaff()) {
    //             $organisation = Auth::user()->organisation_id;
    //         } else {
    //             $organisation = $request->school;
    //         }
    //         Excel::import(new TeacherImport($school, Auth::user(), []), $request->file('teachersfile'));
    //         return redirect()->route("teachers.index")->with("message", "We are processing your import. You will get notified once this is completed and these accounts will each receive an email with their login details.");
    //     }
    //     return redirect()->route("teachers.index")->with("message", "Please make sure your uploaded file matches the sample file format");
    // }
    public function getAdvanceFilterData(Request $request)
    {
        $staff = Staff::select('users.id', 'users.organisation_id', 'users.name', 'users.email', 'users.state_id', 'users.created_at', 'profiles.firstname as firstname', 'profiles.lastname as lastname', 'profiles.access as access', 'profiles.position as position','countries.name as country_name','countries.code as country_code')
            ->join('profiles', 'users.id', '=', 'profiles.user_id')
            ->leftJoin('countries', 'users.country_id', '=', 'countries.id')
            ->with('organisation:id,name,state_id', 'organisation.state:id,code', 'organisation.detail:school_id,type,gender,subscription_ending_on', 'state.country');
        if (Auth::user()->isStaff()) {
            $campus = false;
            $campuses = Auth::user()->orgCampuses()->pluck('campus_id');
            if ($campuses->count()) {
                if (request('campus')) {
                    $campus = array(request('campus'));
                } else {
                    $campus = $campuses;
                }
            }
            $staff->where("users.id", "<>", Auth::id())->where('organisation_id', Auth::user()->organisation_id)
                ->when($campus, function ($query) use ($campus) {
                    $query->where(function ($query) use ($campus) {
                        if (request('campus')) {
                            return $query->whereHas('orgCampuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            });
                        } else {
                            return $query->whereHas('orgCampuses', function ($q) use ($campus) {
                                $q->whereIn('campus_id', $campus);
                            })->orDoesntHave('orgCampuses');
                        }
                    });
                });
        }
        $datatable = Datatables::of($staff)
            ->filter(function ($query) {
                if ($name = request('name')) {
                    $query->where('users.name', 'like', "%{$name}%");
                }
                if ($organisation = request('organisation')) {
                    $query->where('organisation_id', $organisation);
                }

                if (request('campus')) {
                    $query->whereHas('campuses', function ($q) {
                        $q->where('campus_id', request('campus'));
                    });
                }
                if ($state = request('state')) {
                    $query->whereHas('organisation', function ($q) use ($state) {
                        $q->whereStateId($state);
                    });
                }
                if ($type = request('type')) {
                    $query->whereHas('organisation', function ($q) use ($type) {
                        $q->whereHas('detail', function ($q2) use ($type) {
                            $q2->whereType($type);
                        });
                    });
                }
                if ($gender = request('gender')) {
                    $query->whereHas('organisation', function ($q) use ($gender) {
                        $q->whereHas('detail', function ($q2) use ($gender) {
                            $q2->whereGender($gender);
                        });
                    });
                }
                if (request('active_inactive') == 'Active') {
                    $query->whereHas('organisation', function ($q) {
                        $q->whereHas('detail', function ($q2) {
                            $q2->whereDate('subscription_ending_on', '>=', Carbon::now());
                        });
                    });
                } elseif (request('active_inactive') == 'Inactive') {
                    $query->whereHas('organisation', function ($q) {
                        $q->whereHas('detail', function ($q2) {
                            $q2->whereDate('subscription_ending_on', '<=', Carbon::now());
                        });
                    });
                }
            }, true);
        return $datatable
            ->addColumn('action', function (Staff $user) {
                return '<a href="staff/' . $user->id . '/invite"> <i class="fa fa-paper-plane-o text-danger" data-toggle="tooltip" title="Resend welcome email with account details."></i></a><a data-target="#modalStaffEdit" data-id="' . $user->id . '" data-toggle="modal" id="btnFillSizeToggler" class="" href="#"> <i class="fa fa-edit"></i></a> <a href="staff/' . $user->id . '" data-method="delete" data-token="' . csrf_token() . '" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i></a>';
            })
            ->addColumn('access_level', function (Staff $user) {
                return $user->profile->access_level;
            })
            ->addColumn('registration_date', function (Staff $user) {
                return Carbon::parse($user->created_at)->toFormattedDateString();
            })
            ->addColumn('subscription_end_date', function (Staff $user) {
                if ($user->organisation->detail->subscription_ending_on) {
                    return Carbon::parse($user->organisation->detail->subscription_ending_on)->toFormattedDateString();
                }
                return '';
            })
            ->addColumn('campuses', function (Staff $user) {
                $data = '';
                if ($user->orgCampuses()->exists()) {
                    foreach ($user->orgCampuses as $campus) {
                        $data .=  '<span class="label">' . $campus->name . '</span>';
                    }
                } else {
                    $data .= 'Unassigned';
                }
                return $data;
            })
            ->rawColumns(['action', 'registration_date', 'campuses', 'subscription_end_date'])
            ->make(true);
        // ->ToJson();
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreStaff $request)
    {
        // dd($request->all());
        $roleid = Role::where('name', "Staff")->value('id');

        if (Auth::user()->isStaff()) {
            $organisation = Auth::user()->organisation_id;
            $state = Organisation::where('id', $organisation)->value('state_id');
            if (Auth::user()->orgCampuses()->count() > 1) {
                $campuses = request('campuses');
            } else {
                $campuses = Auth::user()->orgCampuses()->pluck('campus_id');
            }
        } else {
            $organisation = request('organisation');
            $state = Organisation::where('id', $organisation)->value('state_id');
            $campuses = request('campuses');
        }

        $password = request('password') ?? rand(20000, 999909999);
        $staff = Staff::create([
            'name' => request('firstname') . ' ' . request('lastname'),
            'email' => request('email'),
            'role_id' => $roleid,
            'password' => bcrypt($password),
            'organisation_id' => $organisation,
            'country_id' => 1,
            'state_id' => $state,
            'postcode' => request('postcode'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        if ($staff) {

            $staff->profile()->create([
                'firstname' => request('firstname'),
                'lastname' => request('lastname'),
                'access' => request('access'),
                // 'newsletter' => request('newsletter'),
                // 'newsletter' => true,
                'position' => request('position'),
                'accountcreated' => true,
            ]);

            $staff->orgCampuses()->attach($campuses);
            $staff->password = $password;

            \Event::dispatch(new LicenseRenewed(User::where("id", $staff->id)->first()));
            Mail::to($staff->email)->send(new StaffWelcomeMail($staff));

            // User::addHelpcrunchAccount($staff->id);
            // User::updateUserMailchimpDetail($staff->id);
        }
        // $redirectUrl = url()->previous();
        // if($redirectUrl == url('updateUserSession') || strpos($redirectUrl,'api') !== false) {
        //     $redirectUrl = session()->get('previousUrl');
        // }
        // return redirect($redirectUrl);

        if (Auth::user()->isStaff()) {
            return redirect()->route('staff.index')->with('message', 'Staff has been added successfully. They will now receive an automatic welcome email with their account details.');
        } else {
            return redirect()->route('staff.index')->with('message', 'Staff has been added successfully!');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Staff  $staff
     * @return \Illuminate\Http\Response
     */
    public function show(Staff $staff)
    {
        $staff->firstname = $staff->profile->firstname;
        $staff->lastname = $staff->profile->lastname;
        $staff->access = $staff->profile->access;
        // $staff->country = @$staff->state->country->id;
        $staff->country = @$staff->country_id;
        $staff->campusIds = $staff->orgCampuses()->pluck('campus_id');
        $staff->teacher_position = $staff->profile->position;
        return $staff;
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Staff  $staff
     * @return \Illuminate\Http\Response
     */
    public function edit(Staff $staff)
    {
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Staff  $staff
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());
        $staff = Staff::findOrFail($id);
        $oldEmail = $staff->email;
        $staff->name = request('firstname') . ' ' . request('lastname');
        $staff->email = (request('email')) ? request('email') : str_random(40);
        if (request('password')) {
            $staff->password = bcrypt(request('password'));
        }
        if (Auth::user()->isStaff()) {
            $organisation = Auth::user()->organisation_id;
        } else {
            $organisation = request('organisation');
        }

        if ($staff->organisation_id != $organisation) {
            Cache::forget('orgPlans' . $staff->organisation_id);
        }

        $staff->organisation_id = $organisation;
        $staff->country_id = request('country');
        $staff->state_id = request('state');
        $staff->postcode = request('postcode');

        $profile = $staff->profile;
        $profile->firstname = request('firstname');
        $profile->lastname = request('lastname');
        $profile->access = request('access');
        // $profile->newsletter = request('newsletter');
        // $profile->newsletter = true;
        $profile->position = request('position');
        $staff->save();
        $staff->profile()->save($profile);

        $staff->orgCampuses()->sync(request('campuses'));
        \Event::dispatch(new LicenseRenewed(User::where("id", $staff->id)->first()));

        Cache::tags(['userhasFullAccess', 'hasFullAccess.' . $staff->id])->flush();
        Cache::tags(['userhasManagerAccess', 'hasManagerAccess.' . $staff->id])->flush();
        Cache::forget('orgPlans' . $staff->organisation_id);

        // User::addHelpcrunchAccount($staff->id);

        // if ($oldEmail != $request->email) {
        //     Newsletter::updateEmailAddress($oldEmail, $request->email, 'staff');
        // }

        // User::updateUserMailchimpDetail($staff->id);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Staff has been updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Staff  $staff
     * @return \Illuminate\Http\Response
     */
    public function destroy(Staff $staff)
    {
        if (Auth::user()->isStaff()) {
            $staff = Staff::fellowTeachers()->find($staff->id);
        }
        $delete = $staff->delete();

        // if ($delete) {
        //     User::deleteHelpcrunchAccount($staff->id);
        // }

        if (request()->wantsJson()) {
            return response([], 204);
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Staff has been deleted successfully!');
    }
    public function reSendInvitation($id)
    {
        $staff = Staff::find($id);
        Mail::to($staff->email)->send(new StaffWelcomeMail($staff));
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Automated welcome email with account details has been resent successfully.');
    }
}
