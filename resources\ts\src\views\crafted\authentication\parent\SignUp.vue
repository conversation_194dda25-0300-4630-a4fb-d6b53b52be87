<template>
    <div class="d-flex flex-column flex-lg-row flex-column-fluid stepper stepper-pills stepper-column stepper-multistep first" ref="wizardRef" id="kt_create_account_stepper">
        <div class="d-none flex-column flex-lg-row-auto w-lg-350px w-xl-500px">
            <div class="top-0 bottom-0 d-flex flex-column position-lg-fixed w-lg-350px w-xl-500px scroll-y bgi-size-cover bgi-position-center" style="background-image: url(media/misc/auth-bg.png)">
                <div class="py-10 d-flex flex-center py-lg-20 mt-lg-20">
                    <router-link to="/">
                        <img alt="Logo" src="media/logos/custom-1.png" class="h-70px" />
                    </router-link>
                </div>
                <div class="p-10 d-flex flex-row-fluid justify-content-center">
                    <div class="stepper-nav">
                        <div class="stepper-item " :class="[(currentStepIndex == 0) ? 'current' : 'pending']" data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon rounded-3">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">1</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title fs-2">Account Type</h3>
                                    <div class="stepper-desc fw-normal">
                                        Select your account type
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>
                        <div class="stepper-item" :class="[(currentStepIndex == 1) ? 'current' : 'pending']" data-kt-stepper-element="nav">
                            <div class="stepper-wrapper">
                                <div class="stepper-icon rounded-3">
                                    <i class="stepper-check fas fa-check"></i>
                                    <span class="stepper-number">2</span>
                                </div>
                                <div class="stepper-label">
                                    <h3 class="stepper-title fs-2">Account Settings</h3>
                                    <div class="stepper-desc fw-normal">
                                        Setup your account settings
                                    </div>
                                </div>
                            </div>
                            <div class="stepper-line h-40px"></div>
                        </div>


                    </div>
                </div>
                <div class="flex-wrap px-5 py-10 d-flex flex-center">
                    <div class="d-flex fw-normal">
                        <a href="" class="px-5 text-success" target="_blank">Terms</a>
                        <a href="" class="px-5 text-success" target="_blank">Plans</a>
                        <a href="" class="px-5 text-success" target="_blank">Contact Us</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-column flex-lg-row-fluid">
            <form class="pb-5 text-gray-700" novalidate="novalidate" id="kt_create_account_form" @submit="handleStep" @change="() => false">
                <div class="" :class="[(currentStepIndex == 0) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step1 :formData="formData" :errors="errors" />
                </div>


                <div class="" :class="[(currentStepIndex == 1) ? 'current' : '']" data-kt-stepper-element="content">
                    <Step2 :formData="formData" :errors="errors" />
                </div>


                <div class="pt-10">
                    <!-- <button
                    type="button"
                    class="btn btn-lg btn-light-primary me-3 w-100 rounded-0 mb-10"
                    data-kt-stepper-action="previous"
                    @click="previousStep"
                  >
                    <span class="svg-icon svg-icon-4 me-1">
                      <inline-svg src="media/icons/duotune/arrows/arr063.svg" />
                    </span>
                    Back
                  </button> -->
                    <button type="submit" class="btn btn-lg btn-primary w-100 rounded-0 pt-3" data-kt-stepper-action="submit" v-if="currentStepIndex === totalSteps - 1">
                        <span class="indicator-label"> NEXT </span>
                        <span class="indicator-progress"> Please wait... <span class="align-middle spinner-border spinner-border-sm ms-2" ></span> </span>
                    </button>
                    <button type="button" class="btn btn-lg btn-primary me-3 w-100 rounded-0 mb-10" @click="confirmChild" v-else-if="(currentStepIndex == 0 && hasUnConfirmedChild)">
                        <span class="indicator-label"> CONFIRM </span>
                        <span class="indicator-progress"> Please wait... <span class="align-middle spinner-border spinner-border-sm ms-2" ></span> </span>
                    </button>
                    <button v-else type="submit" class="btn btn-lg btn-primary w-100 rounded-0 pt-3">
                        NEXT
                        <!-- <span class="svg-icon svg-icon-4 ms-1 me-0">
                      <inline-svg src="media/icons/duotune/arrows/arr064.svg" />
                    </span> -->
                    </button>

                    <p class="fs-6 pt-4" v-if="currentStepIndex == 0">Not interested in adding a child? <span class="cursor-pointer text-blue" @click="nextStep">Create a free parent only account</span></p>
                </div>
            </form>
        </div>
    </div>
</template>
<script lang="ts">
    import { computed, defineComponent, onMounted, ref } from "vue";
    import LayoutService from "@/core/services/LayoutService";
    import { useRegisterStore } from "@/stores/Auth/RegisterStore";
    import { getIllustrationsPath } from "@/core/helpers/assets";
    import Step1 from "@/views/crafted/authentication/parent/steps/Step1.vue";
    import Step2 from "@/views/crafted/authentication/parent/steps/Step2.vue";
    import { StepperComponent, defaultStepperOptions } from "@/assets/ts/components";
    import * as Yup from "yup";
    import { useForm, configure } from "vee-validate";
    import Swal from "sweetalert2/dist/sweetalert2.min.js";
    import { useStore } from "vuex";
    import { Actions } from "@/store/enums/StoreEnums";
    import { useRouter, useRoute } from 'vue-router'
    configure({
        validateOnBlur: false,
        validateOnChange: false,
        validateOnInput: false,
        validateOnModelUpdate: false,
    });

    interface Child {
        email: string;
        isRegistered: boolean;
        isConfirmed: boolean;
        isParent: boolean;
        childPlan: string;
    }

    interface IStep1 {
        children: Child[];
        plan: string;
    }


    interface IStep2 {
        email: string;
        firstname: string;
        lastname: string;
        password: string;
        confirm_password: string;
        state: string;
        country: string;
        postcode: string;
    }


    interface CreateAccount extends IStep1, IStep2 { }

    export default defineComponent({
        name: "parent-sign-up",
        components: {
            Step1,
            Step2

        },
        setup() {
            const store = useStore();
            const router = useRouter();
            const route = useRoute()

            const authStore = useRegisterStore();
            const _stepperObj = ref<StepperComponent | null>(null);
            // defaultStepperOptions.startIndex=authStore.currentStage;
            const wizardRef = ref<HTMLElement | null>(null);
            const currentStepIndex = ref(0);
            const formData = ref<CreateAccount>({
                children: [{
                    email: '',
                    isRegistered: true,
                    childPlan: '',
                    isConfirmed: false,
                    isParent: false
                }],
                plan: '',
                email: authStore.email,
                firstname: authStore.parentDetail.firstname,
                lastname: authStore.parentDetail.lastname,
                password: "",
                confirm_password: "",
                state: authStore.parentDetail.state,
                country: authStore.parentDetail.country,
                postcode: ""

            });

            onMounted(() => {


                _stepperObj.value = StepperComponent.createInsance(
                    wizardRef.value as HTMLElement, defaultStepperOptions
                );
                nextStep();

                LayoutService.emptyElementClassesAndAttributes(document.body);

                store.dispatch(Actions.ADD_BODY_CLASSNAME, "app-blank");

                store.dispatch(Actions.ADD_BODY_CLASSNAME, "bg-body");
                var d = route.params;
                if (d.stage == "buylicense" && d.invitetoken.length) {
                    // fetchParentDetails(d.invitetoken);
                }



            });
            const testChildEmail = async (value, key) => {
                var ci = key.options.index;
                formData.value = {
                    ...formData.value,
                    ...values,
                };
                // console.log(values, formData.value.children[ci], ci, "Asdhjkas hdjka shd");

                if (!value.length || (formData.value.plan.length && !formData.value.children[ci].isRegistered) || formData.value.children[ci].isConfirmed) {
                    console.log("valid email ");
                    return true;
                }

                var f = { ...formData.value };
                await store.dispatch(Actions.CHECK_CHILD_EMAIL, { 'childEmail': value });
                const [errorName] = Object.keys(store.getters.getErrors);
                const error = store.getters.getErrors[errorName];
                const errorParent = store.getters.getErrors[1];

                if (!error) {
                    console.log("Is registered");
                    formData.value.children[ci].isRegistered = true;
                    return true;
                }

                formData.value.children[ci].isParent = errorParent.isParent;
                formData.value.children[ci].isRegistered = false;
                return false;
            };

            const fetchParentDetails = async (token) => {
                const response = await fetch(
                    'parent/validateToken?invitetoken=' + token,
                    {
                    }
                );

                const data = await response.json();
                console.log(data);
            };

            const checkUniqueEmail = async (value) => {
                // console.log(value);
                await store.dispatch(Actions.CHECK_UNIQUE_EMAIL, { 'email': value });
                var [errorName] = Object.keys(store.getters.getErrors);
                var error = store.getters.getErrors[errorName];
                if (!error) {
                    // console.log("Success returned ", error);
                    return true;
                } else {
                    // console.log("Error returned ", error, value);
                }
                return false;
            };

            const createAccountSchema = /* [ */
                // Yup.object().shape({
                //     children: Yup.
                //         array()
                //         .of(
                //             Yup.object().shape({
                //                 email: Yup.string().email().required().label('Child Email')/* .test("Child-Registered", "This email hasn’t been registered! Try another email or choose a subscription below.", testChildEmail) */
                //             })
                //         )
                // }),
                // '',
                Yup.object({
                    email: Yup.string().required("Email is required.").email().label('Email').test('Unique-Email', 'Email address is already registered!', checkUniqueEmail),
                    firstname: Yup.string().required("First Name is required."),
                    lastname: Yup.string().required("Last Name is required."),
                    password: Yup.string().required('Password is required.').min(6).label('Password'),
                    state: Yup.string().when('parentInvite', {
                        is: (val) => { return formData.value.country == '1' },
                        then: Yup.string().required('State is required')
                    }),
                    country: Yup.string().required('Country is required'),
                    postcode: Yup.string().required('Postcode is required.'),
                    confirm_password: Yup.string()
                        .oneOf([Yup.ref('password'), null], 'Passwords are not matching.')
                });
            // ];

            const currentSchema = computed(() => {
                return createAccountSchema[currentStepIndex.value];
            });

            const { errors, resetForm, handleSubmit, values, validate } = useForm<
                IStep2
            >({
                validationSchema: createAccountSchema,
                initialValues: formData,
                validateOnMount: false,
            });

            const totalSteps = computed(() => {
                if (!_stepperObj.value) {
                    return;
                }

                return _stepperObj.value.totatStepsNumber;
            });

            resetForm({
                values: {
                    ...formData.value,
                },
            });

            const handleStep = handleSubmit((values) => {
                console.log("Handle Submit parent sign up");
                resetForm({
                    values: {
                        ...formData.value,
                    },
                });
                formData.value = {
                    ...formData.value,
                    ...values,
                };


                if (currentStepIndex.value == 1) {
                    // authStore.parentDetail.childEmail=formData.value.childEmail;
                    authStore.parentDetail.childPlan = formData.value.plan;
                    authStore.email = formData.value.email;
                    authStore.parentDetail.firstname = formData.value.firstname;
                    authStore.parentDetail.lastname = formData.value.lastname;
                    authStore.parentDetail.password = formData.value.password;
                    authStore.parentDetail.confirm_password = formData.value.confirm_password;
                    authStore.parentDetail.state = formData.value.state;
                    authStore.parentDetail.country = formData.value.country;
                    authStore.parentDetail.postcode = formData.value.postcode;
                    if (formData.value.plan == 'limited') {

                        authStore.parentDetail.children = formData.value.children = [];

                    }
                    submitRegistrationForm(formData.value);

                    return;
                }
                currentStepIndex.value++;
                authStore.currentStage = currentStepIndex.value;
                console.log(_stepperObj.value);
                /* Check if last step , if so submit form  */
                if (!_stepperObj.value) {
                    return;
                }

                _stepperObj.value.goNext();
            });
            const submitRegistrationForm = async (values) => {

                await store.dispatch(Actions.BUY_PARENT_LICENSE, values);

                const [errorName] = Object.keys(store.getters.getErrors);
                console.log(errorName);
                const error = store.getters.getErrors[errorName];
                console.log(error);
                if (!error) {
                    const stripedata = store.getters.getStripeData;
                    console.log(stripedata);
                    if (typeof stripedata['url'] != "undefined" && stripedata['url'].length) {
                        window.location.href = stripedata['url'];
                    } else if (stripedata.data['email'].length) {
                        router.push({ name: "dashboard" });
                    }


                } else {
                    var errormsg = '';
                    if (typeof error.password != 'undefined') {
                        errormsg = error.password
                    }
                    Swal.fire({
                        text: errormsg,
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Try again!",
                        customClass: {
                            confirmButton: "btn fw-semobold btn-light-danger",
                        },
                    });
                }
                // if(authStore.studentDetail.schoolUnavailable || authStore.studentDetail.inSchool=='notinschool'){
                //     await store.dispatch(Actions.BUY_INDIVIDUAL_LICENSE,authStore);
                //   }else{
                //    await store.dispatch(Actions.REGISTER,authStore);
                //   }


                //   const [errorName] = Object.keys(store.getters.getErrors);
                //   console.log(errorName);
                //   const error = store.getters.getErrors[errorName];
                //   console.log(error);
                //   if (!error) {
                //     if(authStore.studentDetail.schoolUnavailable || authStore.studentDetail.inSchool=='notinschool'){
                //       const stripedata = store.getters.getStripeData;
                //       console.log(stripedata);
                //       if( stripedata['url'].length){
                //         window.location.href = stripedata['url'];
                //       }

                //     }else{
                //       router.push({ name: "dashboard" });
                //     }
                //   }else{
                //     var errormsg='';
                //     if(typeof error.password !='undefined'){
                //       errormsg=error.password
                //     }
                //     Swal.fire({
                //       text: errormsg,
                //       icon: "error",
                //       buttonsStyling:false,
                //       confirmButtonText: "Try again!",
                //       customClass: {
                //         confirmButton: "btn fw-semobold btn-light-danger",
                //       },
                //     });
                //   }
                return true;
            };
            const previousStep = () => {
                if (!_stepperObj.value) {
                    return;
                }

                currentStepIndex.value--;

                _stepperObj.value.goPrev();
            };
            const nextStep = () => {
                if (!_stepperObj.value) {
                    return;
                }

                currentStepIndex.value++;

                _stepperObj.value.goNext();
            };
            const confirmChild = async () => {
                const { valid, errors } = await validate();
                if (valid) {
                    formData.value = {
                        ...formData.value,
                        ...values,
                    };
                    if (formData.value.plan == 'premium') {
                        const i = formData.value.children.findIndex(child => child.isConfirmed === false);
                        formData.value.children[i].isConfirmed = true;
                    }
                    nextStep();
                }
            };

            const formSubmit = () => {
                Swal.fire({
                    text: "All is cool! Now you submit this form",
                    icon: "success",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn fw-semobold btn-light-primary",
                    },
                }).then(() => {
                    // window.location.reload();
                });
            };
            const hasUnConfirmedChild = computed(() => {
                const found = formData.value.children.findIndex(child => child.isConfirmed === false);
                return found > -1;
            });
            return {
                wizardRef,
                previousStep,
                nextStep,
                handleStep,
                formSubmit,
                totalSteps,
                currentStepIndex,
                getIllustrationsPath,
                formData,
                handleSubmit,
                errors,
                hasUnConfirmedChild,
                confirmChild,
                values
            };
        },
    });
</script>
<style>
    .text-blue {
        color: #0062FF;
    }

    .bg-blue {
        background: #0062ff;
    }

    .float-right {
        float: right !important;
    }

    .middle {
        text-align: center;
    }

    .middle h1 {
        font-family: 'Dax', sans-serif;
        color: #fff;
    }

    .middle input[type="radio"] {
        display: none;
    }

    .middle input[type="radio"]:checked+.box {
        background-color: #0062ff;
    }

    .middle input[type="radio"]:checked+.box div * {
        color: #fff;
    }

    .middle .box {
        width: 200px;
        height: 100px;
        background-color: #fff;
        transition: all 250ms ease;
        will-change: transition;
        /* display: inline-block; */
        text-align: center;
        cursor: pointer;
        position: relative;
        font-family: 'Dax', sans-serif;
        font-weight: 900;
        border: 1px solid#0062ff;
        /* margin: 0px 5px; */
    }

    .plan-detail {
        width: 200px;
        height: 200px;
        background-color: grey;
        text-align: left;
        font-family: 'Dax', sans-serif;
        font-weight: 900;
        display: flex;
        /* margin: 0px 5px; */
    }

    label {
        display: inline-grid;
    }

    .plan-detail div {
        overflow-y: auto;
        overflow-x: hidden;
        flex: 1;
    }

    .middle .box:active {
        transform: translateY(10px);
    }

    .middle .box .centered-item {
        position: absolute;
        top: 50%;
        right: 15%;
        left: 15%;
        transform: translate(0%, -50%);
        font-size: 1.5em;
        user-select: none;
    }

    .middle .box div * {
        color: #0062ff;
    }

    .middle .box span:before {
        font-size: 1.2em;
        font-family: FontAwesome;
        display: block;
        transform: translateY(-80px);
        opacity: 0;
        transition: all 300ms ease-in-out;
        font-weight: normal;
        color: white;
    }

    .middle p {
        color: #fff;
        font-family: 'Dax', sans-serif;
        font-weight: 400;
    }

    .middle p a {
        text-decoration: underline;
        font-weight: bold;
        color: #fff;
    }

    .limited-box {
        display: none;
    }

    .premium-box {
        display: none;
    }

    #limited:hover~.limited-box {
        display: flex;
        background: #fcfdfc;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    }

    #premium:hover~.premium-box {
        display: flex;
        background: #fcfdfc;
        box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    }

    .plan-detail div>* {
        color: #000;
    }

    .top-gap {
        padding-top: 5%;
    }

    /* Child Input Box */
    .child-input-box {
        position: relative;
    }

    .child-input-box .top-right {
        text-align: right;
        float: right;
        position: absolute;
        top: 1%;
        right: 1%;
    }

    .child-input-box .bottom-right {
        background-color: #aca8a8;
        text-align: right;
        float: right;
        position: absolute;
        bottom: 1%;
        right: 5%;
    }

    .swal2-popup {
        border-radius: 0px;
    }

    .multiselect-placeholder {
        font-size: 1.075rem !important;
    }

    .card-left {
        text-align: left !important;
    }

    .card-right {
        text-align: right !important;
    }

    @media only screen and (max-width: 1040px) {
        .top-gap {
            padding-top: 0%;
        }

        .card-left {
            text-align: center !important;
        }

        .card-right {
            text-align: center !important;
        }
    }
</style>