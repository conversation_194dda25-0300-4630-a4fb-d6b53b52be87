@extends('layouts.admin',['noGreyBg' => 'no-grey-bg'])
@push('stylesheets')
    <link media="screen" type="text/css" rel="stylesheet" href="{{ asset('assets/plugins/switchery/css/switchery.min.css') }}">
@endpush
@push('styles')
    <style>
        .access-level {
            background: #f6f6f6;
        }

        .black {
            color: black;
        }

        .active-access {
            background: #ffffff;
        }

        .blue {
            color: #0a0afd;
        }

    </style>
@endpush
@section('content')
    @if (session('studentView') == 'true')
        @include('partials.studentbannertabs')
    @else
        @section('breadcrumbs', Breadcrumbs::render('edit-profile'))
    @endif
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card card-transparent">
                <div class="card-header">
                    @if (session('studentView') == 'true')
                        <div class="text-center">
                            <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                            <p class="uppercase bold oswald">Your Account</p>
                        </div>
                    @else
                        <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                        <div class="card-title custom-card-title">Update Profile</div>
                    @endif
                </div>
                <div class="card-block">
                    @if (session('studentView'))
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>First Name</label>
                                    <input type="text" class="form-control" name="firstname" required="" value="John" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Last Name</label>
                                    <input type="text" class="form-control" name="lastname" required="" value="Doe" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" value="<EMAIL>" required="" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default" aria-required="true">
                                    <label>Password</label>
                                    <input type="password" class="form-control" name="password" placeholder="Enter only if you want to change" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required disabled">
                                    <label>Organisation</label>
                                    <input type="text" class="form-control" value="{{ $user->organisation->name }}" disabled>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Year</label>
                                    <select class="form-control" name="year" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Select..</option>
                                        @foreach ($years as $key => $year)
                                            @if ($year->title != 'I’ve finished high school')
                                                <option value="{{ $year->id }}" @if ($year->id == 3) selected @endif>{{ $year->title }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                    <label>Country</label> 
                                    <select name="country" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                        <option selected disabled></option>
                                        @foreach ($countries as $country)
                                            <option @if ($user->country_id && $country->id == $user->country_id) selected="selected" @endif value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                    <label>State</label>
                                    <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                        <option selected disabled></option>
                                        @if ($user->state)
                                            @foreach ($user->state->country->states as $state)
                                                <option @if ($state->id == $user->state_id) selected="selected" @endif value="{{ $state->id }}">{{ $state->name }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Postcode</label>
                                    <input type="text" class="form-control" name="postcode" required="" value="12345" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <a href="#" class="btn btn-black">Update</a>
                    @else
                        <form id="form-personal" method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                            {{ csrf_field() }}
                            <input type="hidden" name="_method" value="PUT">
                            <div class="row clearfix">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required" aria-required="true">
                                        <label>First Name</label>
                                        <input type="text" class="form-control" name="firstname" required="" value="{{ $user->profile->firstname }}" aria-required="true">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required" aria-required="true">
                                        <label>Last Name</label>
                                        <input type="text" class="form-control" name="lastname" required="" value="{{ $user->profile->lastname }}" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="row clearfix">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required" aria-required="true">
                                        <label>Email</label>
                                        <input type="email" class="form-control" name="email" value="{{ $user->email }}" required="" aria-required="true">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group form-group-default" aria-required="true">
                                        <label>Password</label>
                                        <input type="password" class="form-control" name="password" placeholder="Enter only if you want to change" autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                @if (Auth::user()->organisation->campuses->isNotEmpty())
                                    <div class="col-md-6">
                                        <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                            <label>Campuses</label>
                                            <select name="orgcampuses[]" class="form-control" data-init-plugin="select2" data-placement="Select.." multiple>
                                                <option disabled></option>
                                                @foreach (Auth::user()->organisation->campuses as $campus)
                                                    <option @if ($user->orgCampuses && $user->orgCampuses->pluck('id')->contains($campus->id)) selected="selected" @endif value="{{ $campus->id }}">{{ $campus->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                                <div class="@if (Auth::user()->organisation->campuses->isNotEmpty())col-md-6  @else col-md-12 @endif">
                                    <div class="form-group form-group-default required disabled" aria-required="true">
                                        <label>Organisation</label>
                                        <input type="text" class="form-control" name="firstname" required="" value="{{ $user->organisation->name }}" aria-required="true" disabled="">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                        <label>Country</label>
                                        <select name="country" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                            <option selected disabled></option>
                                            @foreach ($countries as $country)
                                                <option @if ($user->country_id && $country->id == $user->country_id) selected="selected" @endif value="{{ $country->id }}">{{ $country->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row clearfix">
                                <div class="col-md-6" id="staff-state-container" style="display: none;">
                                    <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                        <label>State</label>
                                        <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                            <option selected disabled></option>
                                            @if ($user->state)
                                                @foreach ($user->state->country->states as $state)
                                                    <option @if ($state->id == $user->state_id) selected="selected" @endif value="{{ $state->id }}">{{ $state->name }}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required" aria-required="true">
                                        <label>Postcode</label>
                                        <input type="text" class="form-control" name="postcode" required="" value="{{ $user->postcode }}" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="row">
                    <div class="col-md-6">
                        <label for="" class="p-r-10">Subscribe to newsletter </label>
                        <input type="hidden" class="" id="" name="newsletter" value="0" />
                        <input type="checkbox" class="switchery" id="newsletter" name="newsletter" value="1" @if ($user->profile->newsletter == 1) checked @endif  />
                    </div>
                </div> --}}
                            <div class="clearfix text-right">
                                <button class="btn btn-black" type="submit">Update</button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
            @if (!session('studentView') == 'true')
                <div class="access-level p-4 m-2">
                    <p class="uppercase oswald bold p-3">Access levels explained</p>
                    <div class="p-3 @if (in_array($user->profile->access, ['Lead Administrator', 'Full'])) active-access @endif">
                        <p class="bold black">Administrator Access @if (in_array($user->profile->access, ['Lead Administrator', 'Full']))-<span class="blue"> this is you!</span>@endif</p>
                        <p>Manager access + can manage subscription details, teacher accounts, student accounts and access to program features.</p>
                    </div>
                    <div class="p-3 @if ($user->profile->access == 'Manager') active-access @endif">
                        <p class="bold black">Manager Access @if ($user->profile->access == 'Manager')-<span class="blue"> this is you!</span>@endif</p>
                        <p>Content access + can view student details and provide feedback on submitted work. Can access group reports.</p>
                    </div>
                    <div class="p-3 @if ($user->profile->access == 'Content') active-access @endif">
                        <p class="bold black">Content Access @if ($user->profile->access == 'Content')-<span class="blue"> this is you!</span>@endif</p>
                        <p>Can view content and teaching resources. Cannot access individual student information.</p>
                    </div>
                    @if ($user->profile->access != 'Full')
                        <p class="mt-1 p-3">To request a change to your access level, please get in touch with your account administrators who manage your subscription and teacher accounts.</p>
                        <p class="pl-3">Your organisation administrators:</p>
                        @if ($administrators->count() >= 1)
                            <ul>
                                @foreach ($administrators as $administrator)
                                    <li>{{ $administrator->name }}</li>
                                @endforeach
                            </ul>
                        @else
                            <p class="pl-3 bold">Unassigned</p>
                        @endif
                    @endif
                </div>
            @endif
        </div>
    </div>
    @push('scriptslib')
        <script src="https://cdn.ckeditor.com/4.14.1/standard/ckeditor.js"></script>
        <script type="text/javascript" src="{{ asset('assets/plugins/switchery/js/switchery.min.js') }}"></script>
    @endpush
    @push('scripts')
        <script>
            var userStateId = "{{ $user->state_id }}";
            jQuery(document).ready(function() {

                jQuery("select[name=country]").change(function() {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getStates') }}",
                        data: {
                            id: jQuery(this).val(),
                        },
                        success: function(response) {
                            jQuery("select[name=state]").html('<option selected disabled></option>');

                            if (!response || response.length == 0) {
                                jQuery("#staff-state-container").hide();
                                return;
                            }
                            jQuery("#staff-state-container").show();

                            jQuery.each(response, function(i, v) {
                                let selected = v.id == userStateId ? 'selected' : '';
                                
                                jQuery("select[name=state]").append('<option value="' + v.id + '" '+selected+'>' + v.name + '</option>')
                            });
                        },
                        fail: function() {
                            jQuery("select[name=state]").html('<option selected disabled></option>');
                        }
                    });
                });

                jQuery("select[name=country]").trigger("change");
             
                jQuery(document).on('click', '#removeImg', function() {
                    var answer = confirm('Are you sure you want to remove this?');
                    if (answer) {
                        jQuery('#imgCol').remove();
                        jQuery('#inputCol').removeClass('col-md-8').addClass('col-md-12')
                        jQuery('#current-avatar').val('');
                    } else {
                        //do nothing
                    }
                });

                jQuery('#form-personal').validate({
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: {
                            required: true,
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $("input[name='email']").val();
                                    },
                                    id: {{ Auth::id() }},
                                },
                            }
                        },
                        'orgcampuses[]': 'required',
                        country: 'required',
                        state: {
                            required: function() {
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                        },
                        postcode: 'required',
                    },
                    messages: {
                        email: {
                            remote: "Email already in use!"
                        },
                    }
                })

                CKEDITOR.config.allowedContent = true;
                var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
                elems.forEach(function(html, ) {
                    var switchery = new Switchery(html, {
                        color: '#000',
                        size: 'small',
                    });
                });

            });
        </script>
        {{-- {!! JsValidator::formRequest('App\Http\Requests\UpdateProfile')->selector('#form-personal') !!} --}}
    @endpush
@endsection
