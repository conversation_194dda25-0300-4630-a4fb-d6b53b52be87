@push('stylesheets')
    <link media="screen" type="text/css" rel="stylesheet" href="{{ asset('assets/plugins/switchery/css/switchery.min.css') }}">
@endpush
<div class="modal fade fill-in" id="modalTeacherEdit" role="dialog" aria-hidden="true">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        <i class="pg-close">
        </i>
    </button>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-left p-b-5">
                    <span class="semi-bold">
                        Edit Teacher
                    </span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class=" col-lg-12 ">
                        <!-- START card -->
                        <div class="card card-transparent">
                            <div class="card-block">
                                <form method="POST" action="" id="form-teacheredit" autocomplete="off">
                                    {{ csrf_field() }}
                                    <input type="hidden" name="_method" value="PUT">
                                    <input type="hidden" name="id" id="id">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>First Name</label>
                                                <input type="text" id="firstname" class="form-control" name="firstname" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Last Name</label>
                                                <input type="text" id="lastname" class="form-control" name="lastname" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="@if (Auth::user()->isAdmin()) col-md-6 @else col-md-12 @endif">
                                            <div class="form-group form-group-default required">
                                                <label>Email</label>
                                                <input type="email" id="email" class="form-control" name="email" />
                                            </div>
                                        </div>
                                        @if (Auth::user()->isAdmin())
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default required">
                                                    <label>Password</label>
                                                    <input type="password" id="password" class="form-control" name="password" placeholder="Enter only if you want to change" />
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Access level</label>
                                                <select class="full-width" id="access" name="access" data-init-plugin="select2" data-placeholder="Select.." required>
                                                    <option value=""></option>
                                                    <option value="Lead Administrator" title="Administrator access + responsible for managing renewal notices, and invoicing.">Lead Administrator</option>

                                                    <option value="Full" title="Manager access + can manage subscription details, teacher accounts, student accounts and access to program features.">Administrator</option>

                                                    <option value="Manager" title="Content access + can view student details and provide feedback on submitted work. Can access group reports.">Manager</option>

                                                    <option value="Content" title="Can view content and teaching resources. Cannot access individual student information.">Content</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Country</label>
                                                <select class="full-width" id="country" name="country" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    @foreach ($countries as $country)
                                                        <option value="{{ $country->id }}">{{ $country->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="teacher-edit-state-container" style="display: none;">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>State</label>
                                                <select id="state_id" class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                                   <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Postcode</label>
                                                <input type="number" id="postcode" placeholder="" class="form-control" name="postcode">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        @if (Auth::user()->role->name == 'Admin')
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>School</label>
                                                    <select class="full-width" id="school_id" name="school" data-init-plugin="select2" data-placeholder="Select...">
                                                        <option value="">Select...</option>
                                                        @foreach ($schools as $key => $school)
                                                            <option value="{{ $school->id }}">{{ $school->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        @endif
                                        {{-- <div class="col-md-6">
                                    <div class="form-group form-group-default form-group-default-select2 required">
                                        <label>Position</label>
                                        <select class="full-width" id="position_id" name="position"
                                        data-placeholder="Type to add" data-tags='true'>
                                        <option value="">Select...</option>
                                        @foreach ($positions as $key => $position)
                                        <option value="{{$position->id}}">{{$position->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div> --}}
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Position</label>
                                                <input type="text" id="teacher_position" class="form-control" name="position" />
                                            </div>
                                        </div>
                                        {{-- @if (Auth::user()->isTeacher() && $campuses->count() > 1 && in_array(Auth::user()->profile->access, ['Lead Administrator', 'Full']) &&  Auth::user()->campuses->isNotEmpty())
                                            <div class="col-md-6" id="teacher-campus-edit">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Campuses</label>
                                                    <select class="full-width" id="campusIds" name="campuses[]" data-init-plugin="select2" data-placeholder="Select.." multiple>
                                                        @foreach ($campuses as $campus)
                                                        <option value="{{ $campus->id }}"> {{ $campus->name }} </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        @endif --}}
                                          @if (Auth::user()->isTeacher() && in_array(Auth::user()->profile->access, ['Lead Administrator', 'Full']) &&  Auth::user()->campuses->isNotEmpty())
                                            <div class="col-md-6" id="teacher-campus-edit">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Campuses</label>
                                                    <select class="full-width" id="campusIds" name="campuses[]" data-init-plugin="select2" data-placeholder="Select.." multiple>
                                                        @foreach ($campuses as $campus)
                                                        <option value="{{ $campus->id }}"> {{ $campus->name }} </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    @if (Auth::user()->isAdmin())
                                    <div class="row clearfix" id="campus-edit" style="display:none;">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Campuses</label>
                                                <select class="full-width" id="campusIds" name="campuses[]" data-init-plugin="select2" data-placeholder="Select.." multiple>
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                    @if (Auth::user()->role->name == 'Admin')
                                        <div class="row clearfix">
                                            <div class="col-md-12">
                                                <div class="form-group form-group-checkbox required">
                                                    <label> School Access For </label>
                                                    <div class="checkbox check-primary no-margin pick"><input type="checkbox" value="1" id="secondary_section" name="secondary_section" class="require-one-edit"><label for="secondary_section" class="no-margin">Secondary</label></div>
                                                    <div class="checkbox check-primary no-margin pick"><input type="checkbox" value="1" id="primary_section" name="primary_section" class="require-one-edit"><label for="primary_section" class="no-margin">Primary</label></div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="row">
                                        <div class="col-md-6">
                                            {{-- <label for="" class="p-r-10">Subscribe to newsletter </label>
                                            <input type="hidden" class="" id="" name="newsletter" value="0" />
                                            <input type="checkbox" class="switchery" id="newsletter" name="newsletter" value="1" /> --}}
                                        </div>
                                    </div>
                                    <div class="clearfix text-right">
                                        <button class="btn btn-primary" type="submit">
                                            Update
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- END card -->
                    </div>
                </div>
            </div>
            <div class="modal-footer"></div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
@push('scripts')
    <script>
        jQuery(document).ready(function() {
            // Initialize select2 for country and state dropdowns
            jQuery("#form-teacheredit select[name=country]").select2({
                placeholder: "Select..",
                allowClear: false
            });
            jQuery("#form-teacheredit select[name=state]").select2({
                placeholder: "Select..",
                allowClear: false
            });

            // Function to fetch states based on country ID
            function fetchStates(countryId, callback) {
                if (!countryId) {
                    jQuery("#form-teacheredit select[name=state]").html('<option value=""></option>').trigger("change.select2");
                    if (callback) callback();
                    return;
                }
                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getStates') }}",
                    data: {
                        id: countryId
                    },
                    dataType: 'json',
                    success: function(response) {
                        // var $stateSelect = jQuery("#form-teacheredit select[name=state]");
                        var $stateSelect =  jQuery("select[name=state]").html('<option selected disabled></option>');
                        // Clear existing options
                        // $stateSelect.html('<option value=""></option>');
                        // Prepare select2 options
                         if (!response || response.length == 0) {
                                jQuery("#teacher-edit-state-container").hide();
                                return;
                            }
                        var options = response.map(function(state) {
                            return {
                                id: state.id,
                                text: state.name
                            };
                        });
                        jQuery("#teacher-edit-state-container").show();
                        // Update select2 with new options
                        $stateSelect.select2({
                            data: options,
                            placeholder: "Select..",
                            allowClear: false
                        });
                        $stateSelect.trigger("change.select2");
                        if (callback) callback();
                    },
                    error: function(xhr, status, error) {
                        jQuery("#form-teacheredit select[name=state]").html('<option value=""></option>').trigger("change.select2");
                        if (callback) callback();
                    }
                });
            }

            
            // Fetch states on modal load
            var defaultCountryId = jQuery("#form-teacheredit select[name=country]").val();
            if (defaultCountryId) {
                fetchStates(defaultCountryId);
            }

            // On country change (for future-proofing)
            jQuery("#form-teacheredit select[name=country]").on('change', function() {
                var countryId = jQuery(this).val();
                if (countryId) {
                    fetchStates(countryId);
                } else {
                    jQuery("#form-teacheredit select[name=state]").html('<option value=""></option>').trigger("change.select2");
                }
            });

            // School change handler for campuses
            jQuery('#form-teacheredit select[name=school]').on('change', function() {
                jQuery('#form-teacheredit select[name="campuses[]"]').html('<option value=""></option>');
                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getSchoolCampuses') }}",
                    data: {
                        id: jQuery(this).val()
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (Object.keys(response).length) {
                            jQuery.each(response, function(id, name) {
                                jQuery('#form-teacheredit select[name="campuses[]"]').append('<option value="' + id + '">' + name + '</option>');
                            });
                            jQuery('#campus-edit').show();
                            jQuery('#form-teacheredit select[name="campuses[]"]').rules('add', 'required');
                        } else {
                            jQuery('#campus-edit').hide();
                            jQuery('#form-teacheredit select[name="campuses[]"]').rules('remove', 'required');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Failed to fetch campuses:", status, error, xhr.responseText);
                    }
                });
            });

            // Clear form on modal hide
            jQuery(document).on('hide.bs.modal', '#modalTeacherEdit', function() {
                jQuery('#campus-edit').hide();
                jQuery('#teacher-campus-edit').hide();
                jQuery("#form-teacheredit input:not([type=hidden]):not([type=submit]):not([type=checkbox]):not([type=radio]), #form-teacheredit select:not([type=hidden]").val('');
                jQuery('#form-teacheredit select').trigger("change.select2");
                if (jQuery('input.require-one-edit').is(':checked')) {
                    jQuery(".require-one-edit").prop("checked", false);
                }
            });

            // Populate form on modal show
            jQuery('#modalTeacherEdit').on('show.bs.modal', function(e) {
                jQuery("#form-teacheredit input:not([type=hidden]):not([type=submit]):not([type=checkbox]):not([type=radio]), #form-teacheredit select:not([type=hidden]").val('');
                jQuery("#form-teacheredit select[name=state]").html('<option value=""></option>').trigger("change.select2");
                $trigger = jQuery(e.relatedTarget);
                jQuery("#form-teacheredit").attr("action", "teachers/" + $trigger.data('id'));
                jQuery.ajax({
                    url: "teachers/" + $trigger.data('id'),
                    dataType: 'json',
                    success: function(data) {
                        jQuery.each(data, function(i, v) {
                            if (i === 'state_id' || i === 'campusIds') {
                                // Set state_id or campusIds after states are populated
                                // setTimeout(function() {
                                    jQuery('#modalTeacherEdit #' + i).val(v).trigger("change.select2");
                                // }, 1500);
                            } else if (i === 'secondary_section' || i === 'primary_section') {
                                if (v == 1) {
                                    jQuery('#modalTeacherEdit #' + i).prop('checked', true);
                                }
                            } else {
                                jQuery('#modalTeacherEdit #' + i).val(v);
                                if (jQuery('#modalTeacherEdit #' + i).hasClass('select2-hidden-accessible')) {
                                    jQuery('#modalTeacherEdit #' + i).trigger("change.select2");
                                }
                            }
                        });
                        // Fetch states for the teacher's country
                        // if (data.country) {
                        //     fetchStates(data.country, function() {
                        //         // Set state_id after states are populated
                        //         if (data.state_id) {
                        //             jQuery('#modalTeacherEdit #state_id').val(data.state_id).trigger("change.select2");
                        //         }
                        //     });
                        // }

                       let countryId = data.country || (data.school && data.school.state && data.school.state.country ? data.school.state.country.id : '');
                        let stateId = data.state_id || (data.school && data.school.state ? data.school.state.id : '');
                        if (countryId) {
                            jQuery('#modalTeacherEdit #country').val(countryId).trigger("change.select2");
                            fetchStates(countryId, function() {
                                if (stateId) {
                                    jQuery('#modalTeacherEdit #state_id').val(stateId).trigger("change.select2");
                                }
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Failed to fetch teacher data:", status, error, xhr.responseText);
                    }
                });
            });

            // Initialize select2 for position
            jQuery('#position_id').select2({
                tags: true,
                placeholder: "Select..."
            });

            // Validation for at least one checkbox
            jQuery.validator.addMethod('require-one-edit', function(value) {
                return jQuery('.require-one-edit:checked').length > 0;
            }, 'Please check at least one box.');
            var checkboxes = jQuery('.require-one-edit');
            var checkbox_names = jQuery.map(checkboxes, function(e, i) {
                return jQuery(e).attr("name");
            }).join(" ");

            // Validation on modal shown
            jQuery('#modalTeacherEdit').on('shown.bs.modal', function(e) {
                console.log("Modal shown, initializing validation");
                @if(Auth::user()->isTeacher() && Auth::user()->school->campuses()->count() > 1 && in_array(Auth::user()->profile->access, ['Lead Administrator', 'Full']) &&  Auth::user()->campuses->isNotEmpty())
                jQuery('#teacher-campus-edit').show();
                jQuery('#form-teacheredit select[name="campuses[]"]').rules('add', 'required');
                @endif
                jQuery('#form-teacheredit .error-help-block').remove();
                jQuery('#form-teacheredit .form-group').removeClass('has-error');

                jQuery('#form-teacheredit').validate({
                    errorElement: 'span',
                    errorClass: 'help-block error-help-block',
                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length ||
                            element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            error.insertAfter(element.parent());
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    highlight: function(element) {
                        jQuery(element).closest('.form-group').removeClass('has-success').addClass('has-error');
                    },
                    focusInvalid: false,
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: {
                            required: true,
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return jQuery("#email").val();
                                    },
                                    id: function() {
                                        return jQuery("#id").val();
                                    },
                                },
                            },
                        },
                        password: {
                            required: false,
                            minlength: 5,
                        },
                        country: 'required',
                        state: {
                            // new logic for states - START
                            required: function() {
                                console.log(jQuery('select[name=state] option[value]').length);
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                            // new logic for states - START
                        },
                        access: 'required',
                        school: 'required',
                        position: 'required',
                    },
                    groups: {
                        checks: checkbox_names
                    },
                    errorPlacement: function(error, element) {
                        if (element.attr("type") === "checkbox") {
                            error.insertBefore(checkboxes.first().parent());
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    messages: {
                        email: {
                            remote: 'This email is already in use!'
                        }
                    }
                });
            });
        });
    </script>
@endpush
