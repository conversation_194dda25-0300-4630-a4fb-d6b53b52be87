<template>
    <div class=" card mt-20 mt-md-10 mt-xl-5">
        <!-- <div class=" card-header align-items-center border-0">

        </div> -->

        <div class="card-body pb-0">
            <div class="d-flex justify-content-between align-items-center mb-10">
                <h3>Company Page</h3>

                <div class="d-flex">
                <button
                    type="button"
                    class="btn btn-secondary"
                    :disabled="isViewCompanyPageDisabled"
                    :class="{ 'disabled': isViewCompanyPageDisabled }"
                >
                    <i class="bi bi-eye-fill fs-3"></i>
                    View Company Page
                </button>
                </div>
            </div>
           <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bold">
                <li v-for="tab in tabs" :key="tab.query" class="nav-item">
                    <router-link
                        :to="{ name: 'company', query: { tab: tab.query } }"
                        class="nav-link text-active-primary"
                        :class="{ active: activeTab === tab.query }"
                    >
                        {{ tab.name }}
                    </router-link>
                </li>
            </ul>
        </div>
    </div>




    <div>
        <CompanyProfile v-if="activeTab === 'CompanyProfile'" />

        <CompanyPages v-if="activeTab === 'CompanyPages'" />

        <JobsAndOpportunities v-if="activeTab === 'JobsandOpportunities'" />

        <LinkedCourses v-if="activeTab === 'LinkedCourses'" />

        <LinkedContent v-if="activeTab === 'LinkedContent'" />

        <div v-if="activeTab === 'Content'">
            Linked Content placeholder
        </div>
    </div>


    <!-- Nav Card -->
</template>


<script lang="ts">
    import { defineComponent, ref, onMounted, watch, computed } from "vue";
    import { useRoute, LocationQueryValue } from "vue-router";
    import { useStore } from "vuex";
    import ApiService from "@/core/services/ApiService";
    import CompanyProfile from "@/components/employer/company/CompanyProfile.vue";
    import CompanyPages from "@/components/employer/company/CompanyPages.vue";
    import JobsAndOpportunities from "@/components/employer/company/JobsandOpportunities.vue";
    import LinkedCourses from "@/components/employer/company/LinkedCourses.vue";
    import LinkedContent from "@/components/employer/company/LinkedContent.vue";

    interface CompanyDetail {
        description: string|null,
        status: string|null,
        url: string|null,
        logo: string|null,
        banner: string|null,
        social_links: any[]|null
        last_published_at: any,
    }

    interface Company {
        name: string,
        description: string,
        industries: any[],
        detail: CompanyDetail
    }

    export default defineComponent({
        name: "CompanyPagesTabs",
        components: { CompanyProfile, CompanyPages, JobsAndOpportunities, LinkedCourses, LinkedContent },
        setup() {
            const route = useRoute();
            const store = useStore();
            const currentUser = store.getters.currentUser;
            const company = ref<Company|null>(null);

            const tabs = ref([
                { name: "Company Profile", query: "CompanyProfile" },
                { name: "Company Pages", query: "CompanyPages" },
                { name: "Jobs and Opportunities", query: "JobsandOpportunities" },
                { name: "Linked Courses", query: "LinkedCourses" },
                { name: "Linked Content", query: "LinkedContent" },
            ]);

            const activeTab = ref();

            // Valid tabs to prevent invalid tab values
            const validTabs = tabs.value.map((tab) => tab.query);

            // Computed property to check if View Company Page button should be disabled
            const isViewCompanyPageDisabled = computed(() => {
                return company.value?.detail?.status === 'draft';
            });

            const setActiveTab = (tabValue: LocationQueryValue | LocationQueryValue[]) => {
                if (typeof tabValue === "string" && validTabs.includes(tabValue)) {
                    activeTab.value = tabValue;
                } else {
                    activeTab.value = tabs.value[0].query; // Default tab
                }
            };

            const fetchCompany = async () => {
                if (!currentUser?.company_id) {
                    return;
                }

                try {
                    const { data: response } = await ApiService.get(`/api/companies/${currentUser.company_id}`);

                    if (response?.success) {
                        company.value = response.data;
                    } else {
                        company.value = null;
                    }
                } catch (error: any) {
                    company.value = null;
                    console.error("Error fetching company:", error);
                }
            };

            onMounted(() => {
                setActiveTab(route.query.tab);
                fetchCompany();
            });

            watch(
                () => route.query.tab,
                (newTab) => {
                    setActiveTab(newTab);
                }
            );

            return { activeTab, tabs, isViewCompanyPageDisabled };
        },
    });
</script>


<style scoped>
    .nav-link {
        cursor: pointer;
    }
    .nav-link:not(.active) {
        font-weight: normal;
    }

    .btn:disabled,
    .btn.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
</style>
