<?php

namespace App\Http\Controllers;

use App\ChildInvitee;
use App\ChildParent;
use App\IndividualStudent;
use App\Invoice;
use App\InvoiceItem;
use App\Jobs\ProcessChildInvitation;
use App\Jobs\ProcessChildRegistration;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Mail\ParentInvitation;
use App\ParentInvitee;
use App\Role;
use App\Standard;
use App\State;
use App\Student;
use App\User;
use Auth;
use Newsletter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Stripe\Stripe;
use Mail;
use PDF;
use Illuminate\Support\Str;

class ChildrenController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $states = State::All();
        $user = ChildParent::where('id', Auth::id())->with('children')->first();
        // return view('children.add');
        return view('children.add', compact('user', 'states'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (Auth::user()->isParent()) {
            $parent = ChildParent::findOrFail(Auth::id());
            if ($request->stripeToken) {
                try {
                    Stripe::setApiKey(config('services.stripe.secret'));

                    $roleId = Role::where('name', "Individual Student")->value('id');
                    $fnames = $request->child_fnames;
                    $lnames = $request->child_lnames;
                    $emails = $request->child_emails;

                    $quantity = 0;
                    $children = [];
                    foreach ($emails as $key => $email) {
                        if ($email && $fnames[$key] && $lnames[$key]) {
                            if (User::where('email', $email)->exists()) {
                                $invitation = ChildInvitee::create([
                                    'parent_id' => Auth::id(),
                                    'child_id' => User::where('email', $email)->value('id'),
                                    'token' => Str::random(40),
                                    'processed' => '0',
                                ]);

                                dispatch(new ProcessChildInvitation($invitation));
                            } else {
                                $child = $parent->children()->create([
                                    'name' => $fnames[$key] . " " . $lnames[$key],
                                    'email' => $email,
                                    'role_id' => $roleId,
                                    'password' => bcrypt(Str::random(10)),
                                ]);
                                $children[] = $child->id;
                                $child->profile()->create([
                                    'firstname' => $fnames[$key],
                                    'lastname' => $lnames[$key],
                                ]);

                                if ($child) {
                                    $invitation = ChildInvitee::create([
                                        'parent_id' => Auth::id(),
                                        'child_id' => $child->id,
                                        'token' => Str::random(40),
                                        'processed' => '0',
                                    ]);
                                    dispatch(new ProcessChildRegistration($invitation));
                                }
                                $quantity++;
                            }
                        }
                    }

                    $subscription = Auth::user()->newSubscription('Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);

                    $invoices = Auth::user()->invoices();

                    foreach ($invoices as $invoicedata) {
                        if ($invoicedata->subscription == $subscription->stripe_id) {
                            $invoice = new Invoice;
                            $invoice->user_id = $parent->id;
                            $invoice->subscription_id = $subscription->stripe_id;
                            $invoice->invoicenum = $invoicedata->number;
                            $invoice->date = date("Y-m-d H:i:s", $invoicedata->created);
                            $invoice->duedate = date("Y-m-d H:i:s", $invoicedata->created);
                            $invoice->datepaid = date("Y-m-d H:i:s", $invoicedata->created);
                            $invoice->subtotal = $invoicedata->subtotal / 100;
                            $invoice->credit = 0;
                            $invoice->tax = $invoicedata->tax;
                            $invoice->total = $invoicedata->total / 100;
                            $invoice->status = $invoicedata->status;
                            $invoice->paymentmethod = "stripe";
                            $invoice->save();
                            $lineitems = [];
                            if (count($invoicedata->lines->data) > 0) {
                                foreach ($invoicedata->lines->data as $key => $line) {
                                    $description = "Child Licence";
                                    // $userid = @$children[$key];

                                    $lineitems[] = new InvoiceItem([
                                        // 'user_id' => $userid,
                                        'description' => $description,
                                        'subscription_id' => $line->subscription,
                                        'quantity' => $line->quantity,
                                        'planid' => $line->plan->id,
                                        'start' => date("Y-m-d H:i:s", $line->period->start),
                                        'end' => date("Y-m-d H:i:s", $line->period->end),
                                        'amount' => ($line->amount / 100),
                                    ]);
                                }
                                $invoice->lineItems()->saveMany($lineitems);
                            }
                            break;
                        }
                    }

                    view()->share(['invoice' => $invoice]);
                    $pdf = PDF::loadView('auth.license.invoicepdf');
                    $invoicePDF = $pdf->output();
                    Mail::send('emails.parentaccountcreated', [
                        'name' => $parent->name,
                    ], function ($message) use ($parent, $invoicePDF) {
                        $message->to($parent->email)->subject('Account created successfully.');

                        // $message->attachData($invoicePDF, 'invoice.pdf', [
                        $message->attachData($invoicePDF, 'Receipt-from-The-Careers-Department.pdf', [
                            'mime' => 'application/pdf',
                        ]);
                    });

                    $redirectUrl = url()->previous();
                    if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                        $redirectUrl = session()->get('previousUrl');
                    }
                    return redirect($redirectUrl)->with('message', "Your child has been emailed their next steps!");
                } catch (\Exception $ex) {
                    return $ex->getMessage();
                }
            } else {
                $fnames = $request->child_fnames;
                $lnames = $request->child_lnames;
                $emails = $request->child_emails;
                foreach ($emails as $key => $email) {
                    if ($email && $fnames[$key] && $lnames[$key]) {
                        if (User::where('email', $email)->exists()) {
                            $invitation = ChildInvitee::create([
                                'parent_id' => $parent->id,
                                'child_id' => User::where('email', $email)->value('id'),
                                'token' => Str::random(40),
                                'processed' => '0',
                            ]);

                            dispatch(new ProcessChildInvitation($invitation));
                        }
                    }
                }
                $redirectUrl = url()->previous();
                if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                    $redirectUrl = session()->get('previousUrl');
                }
                return redirect($redirectUrl)->with('message', "Your child has been emailed their next steps!");
            }
        }
        return abort(404);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $child = IndividualStudent::with('profile:user_id,firstname,lastname,gender,standard_id,graduate_year,school')->find($id);
        if (!$child) {
            $child = Student::with('profile:user_id,firstname,lastname,gender,standard_id,graduate_year,school')->find($id);
        }
        if (request()->wantsJson()) {
            // $child->country = @$child->state->country->id;
            $child->country = @$child->country_id;
            $child->firstname = $child->profile->firstname;
            $child->lastname = $child->profile->lastname;
            $child->gender = @$child->profile->gender;
            $child->school_year = $child->profile->standard_id;
            $child->graduate_year = $child->profile->graduate_year;
            $child->school_name = $child->profile->school;
            return $child;
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        if (Auth::user()->isParent()) {
            $child = IndividualStudent::with('profile')->find($id);
            if (!$child) {
                $child = Student::with('profile')->find($id);
            }
            $years = Standard::All();
            $states = State::All();
            return view('children.edit', compact('child', 'years', 'states'));
        } else {
            abort(404, 'Page not found');
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $child = IndividualStudent::find($id);
        if (!$child) {
            $child = Student::find($id);
        }

        $oldEmail = $child->email;

        $child->name = $request->firstname . " " . $request->lastname;
        $child->email = $request->email;
        if ($request->password) {
            $child->password = bcrypt($request->password);
        }
        $child->state_id = $request->state;
        $child->country_id = $request->country;
        $child->postcode = $request->postcode;

        if (request('other_gender')) {
            $gender = request('other_gender');
        } else {
            $gender = request('gender');
        }

        $profile = $child->profile;
        $profile->firstname = $request->firstname;
        $profile->lastname = $request->lastname;
        $profile->gender = $gender;
        if (request('stage') == 'school') {
            $profile->standard_id = $request->school_year;
            $profile->stage_id = null;
            $profile->graduate_year = null;
            $profile->school = $request->school_name;
        } else {
            // $profile->stage_id = $request->stage_id;
            $profile->graduate_year = $request->graduate_year;
            $profile->school = null;
            $profile->standard_id = Standard::nonStudentId();
        }
        if (Auth::user()->isAdmin()) {
            $child->school_id = request('school');
        }
        $child->save();
        $child->profile()->save($profile);


        if ($child->profile->accountcreated) {
            // User::addHelpcrunchAccount($child->id);
            if ($oldEmail != $request->email) {
                // Newsletter::updateEmailAddress($oldEmail, $request->email, 'students');
                // User::updateAssociatedUsersMailchimpDetail($child->id);
            }
            $user = User::find($child->id);
            // UpdateMailchimpAudienceJob::dispatch($user);
        }

        Cache::tags('user.' . $child->id)->flush();
        Cache::tags(['profile' . $child->id])->flush();
        Cache::forget('profile' . $child->id);
        Cache::forget('location' . $child->id);
        Cache::forget('belongs_to_school' . $child->id);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Child has been updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function inviteParent(Request $request)
    {
        dd($_POST);
        if (Auth::user()->isStudent()) {
            $invitation = ParentInvitee::create([
                'child_id' => Auth::id(),
                'relation' => $request->parent_relation,
                'email' => $request->parent_email,

                'token' => Str::random(40),
                'processed' => '0',
            ]);

            Mail::to($invitation->email)->send(new ParentInvitation($invitation));
            return redirect('/home')->with('message', 'Invitation has been sent successfully');
        }
        return abort(404);
    }
    public function reinviteChild($id)
    {
        $invitation = ChildInvitee::whereProcessed('0')->findOrFail($id);
        if ($invitation->parent_id == Auth::id()) {
            if ($invitation->child->profile->accountcreated) {
                dispatch(new ProcessChildInvitation($invitation));
            } else {
                dispatch(new ProcessChildRegistration($invitation));
            }
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Your child has been emailed their next steps!');
        }
        return abort(403);
    }
}
