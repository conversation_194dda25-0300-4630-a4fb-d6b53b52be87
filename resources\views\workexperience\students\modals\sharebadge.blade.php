<div class="modal fade" id="modalShareBadge" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered mw-800px">
        <div class="modal-content rounded-0">
            <div class="separator text-white d-flex align-items-center justify-content-between px-4 py-3">
                <h6 class="modal-title fw-500 fs-18 text-black">Share Badge</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="pg-close"></i>
                </button>
            </div>

            <div class="modal-body pt-4 mx-3">
                <div class="d-flex align-items-center justify-content-around border border-solid">
                    <div class="pb-4 separator shadow-md max-w-md mx-auto">
                        <p class="bold fs-18">Publish your achievements for your network to see.</p>
                        <p class="bold fs-16 my-3">Add to your LinkedIn Profile</p>

                        <p class="mt-2"><span>Here’s a step-by-step guide to adding badges or certificates to the ‘Licenses &amp; Certifications’ section of your LinkedIn Profile:</span></p>
                        <p class="mt-2"><span>1. Go to your LinkedIn profile and scroll to the ‘Licenses &amp; certifications’ section.</span></p>
                        <p class="mt-2"><span>2. Click the + icon.</span></p>
                        <p class="mt-2"><span>3. Provide all the relevant information about the badge. You can find this below.</span></p>
                        <p class="mt-2"><span>4. Don't forget to also mention the skills you gained from earning the badge. This will give your profile an extra boost and help potential employers understand your expertise. </span></p>
                    </div>
                </div>

                <div class=" my-4">
                    <div style="width: 100%; height: 1px; background-color: rgba(0, 0, 0, .07);"></div>
                </div>

                <div class="container p-0">
                    <p class="mt-3 fw-500 mb-4">Copy the below fields to your profile</p>
                    <div class="row">
                        <!-- Left Section: Text Fields -->
                        <div class="col-12 col-md-6">

                            <!-- Badge Fields -->
                            <div class="badge-field-container">
                                <div class="fs-12">Name</div>
                                <div class="border py-2 px-3 d-flex justify-content-between rounded-border align-items-center">
                                    <p class="fs-13 fw-500" id="shareBadgeName">Loading...</p>
                                    <button class="p-0 btn btn-sm copy-btn" onclick="copyToClipboard('shareBadgeName', this)">
                                        <i class="fa fa-copy"></i>
                                        <span class="tooltip-text position-absolute d-none">Copied!</span>
                                    </button>
                                </div>
                            </div>

                            <div class="badge-field-container mt-2">
                                <div class="fs-12">Issuing Organisation</div>
                                <div class="border py-2 px-3 d-flex justify-content-between rounded-border align-items-center">
                                    <p class="fs-13 fw-500" id="shareBadgeCompanies">Loading...</p>
                                    <button class="p-0 btn btn-sm copy-btn" onclick="copyToClipboard('shareBadgeCompanies', this)">
                                        <i class="fa fa-copy"></i>
                                        <span class="tooltip-text position-absolute d-none">Copied!</span>
                                    </button>
                                </div>
                            </div>

                            <div class="badge-field-container mt-2">
                                <div class="fs-12">Issue Date</div>
                                <div class="border py-2 px-3 d-flex justify-content-between rounded-border align-items-center">
                                    <p class="fs-13 fw-500" id="shareBadgeIssueDate">Loading...</p>
                                    <button class="p-0 btn btn-sm copy-btn" onclick="copyToClipboard('shareBadgeIssueDate', this)">
                                        <i class="fa fa-copy"></i>
                                        <span class="tooltip-text position-absolute d-none">Copied!</span>
                                    </button>
                                </div>
                            </div>

                            <div class="badge-field-container mt-2">
                                <div class="fs-12">Expiry Date</div>
                                <div class="border py-2 px-3 d-flex justify-content-between rounded-border align-items-center">
                                    <p class="fs-13 fw-500" id="shareBadgeExpiryDate">Loading...</p>
                                    <button class="p-0 btn btn-sm copy-btn" onclick="copyToClipboard('shareBadgeExpiryDate', this)">
                                        <i class="fa fa-copy"></i>
                                        <span class="tooltip-text position-absolute d-none">Copied!</span>
                                    </button>
                                </div>
                            </div>

                            <div class="badge-field-container mt-2">

                                <div class="fs-12">Credential ID</div>
                                <div class="border py-2 px-3 d-flex justify-content-between rounded-border align-items-center">
                                    <p class="fs-13 fw-500" id="shareBadgeCredential">Loading...</p>
                                    <button class="p-0 btn btn-sm copy-btn" onclick="copyToClipboard('shareBadgeCredential', this)">
                                        <i class="fa fa-copy"></i>
                                        <span class="tooltip-text position-absolute d-none">Copied!</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Right Section: Badge Image -->
                        <div class="col-12 col-md-6 text-center mt-4">
                            <div class="badge-image-container">
                                <img id="shareBadgeImage" class="img-fluid rounded" alt="Badge">
                            </div>
                            <a id="downloadBadgeLink" class="btn btn-outline-primary mt-3" target="_blank" download>
                                <i class="fa fa-download mr-2"></i> Download Image
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button role="button" class="no-border rounded text-black fs-16 mt-5 px-4 py-3 view-badge">
                    View Badge
                </button>
            </div>
        </div>
    </div>
</div>

@push('styles')
    <style>
        .modal-dialog.mw-800px {
            max-width: 800px !important;
            width: 90%;
        }

        .copy-btn {
            border: none;
            background: none;
            padding: 0.25rem 0.5rem;
            cursor: pointer;
            background-color: #c3c4c9;
        }

        .copy-btn:hover {
            background-color: rgba(0, 0, 0, .03);
        }

        .tooltip-text {
            top: -30px;
            right: 0;
            background-color: rgba(0, 0, 0, .8);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }

        .badge-image-container img {
            max-width: 100%;
            height: auto;
            display: none;
        }

        #modalShareBadge {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        #modalShareBadge::-webkit-scrollbar {
            display: none;
        }
    </style>
@endpush
