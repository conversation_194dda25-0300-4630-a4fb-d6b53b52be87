<?php

namespace App\Services;

use App\IntercomAdmin;
use App\IntercomCompany;
use App\IntercomUser;
use App\Jobs\SyncCustomCompanyDataToIntercom;
use Carbon\Carbon;
use Exception;
use Http\Client\Exception\HttpException;
use Illuminate\Support\Facades\Cache;
use Intercom\IntercomClient;
use Illuminate\Support\Facades\Log;

class Intercom {

    protected $config;
    public $client;


    public function __construct(

    )
    {
        $this->config = config('intercom');
        $this->client = new IntercomClient(
            $this->config['access_token'],
            null,
            [
                'Intercom-Version' => $this->config['intercom_version']
            ]
        );
    }

    /**
     * Get Intercom User record from table
     *
     * @param integer $userId
     * @return mixed
     */
    public function getIntercomUserById($userId)
    {
        return IntercomUser::firstOrCreate([
            'user_id' => $userId
        ]);
    }


    /**
     * Get Intercom User object by user id - Primary Key
     *
     * @param integer $userId
     * @return mixed
     */
    public function getUserById($userId): mixed
    {
        $intercomUser = $this->getIntercomUserById($userId);

        $existingContacts = $this->client->contacts->search([
            'query' => [
                'field' => 'external_id',
                'operator' => '=',
                'value' => (string)($intercomUser->uuid),
            ]
        ]);

        if(!isset($existingContacts->data)) return null;

        if(empty($existingContacts->data[0])) {
            return null;
        } else {
            return $existingContacts->data[0];
        }
    }


    /**
     * Get Intercom User object by user uuid
     *
     * @param string $userUuid
     * @return mixed
     */
    public function getUserByUuid(string $userUuid): mixed
    {
        $existingContacts = $this->client->contacts->search([
            'query' => [
                'field' => 'external_id',
                'operator' => '=',
                'value' => $userUuid,
            ]
        ]);

        if(!isset($existingContacts->data)) return null;

        if(empty($existingContacts->data[0])) {
            return null;
        } else {
            return $existingContacts->data[0];
        }
    }

    /**
     * Sync user with intercom
     *
     * @param mixed $user
     * @param array $data
     * @return mixed
     */
    public function syncUser(mixed $user, array $data): mixed
    {
        if (!$this->isEnabled()) return null;

        $existingUser = null;
        $intercomRelation = $user->intercom;

        // First, attempt to find the user by their unique uuid
        if($intercomRelation) {
            $existingUser = $this->getUserByUuid($intercomRelation->uuid); // Api calls To SEARCH

            if(empty($existingUser)) {
                // $user->intercom()->delete();
                $intercomRelation = null;
            }
        }

        if (empty($intercomRelation) || empty($existingUser)) {
            $intercomRelation = $this->getIntercomUserById($user->id);
            $user->intercom = $intercomRelation;
        }

        // if(empty($existingUser)) {
        //     $user->intercom = $this->getIntercomUserById($user->id);
        //     $existingUser = $this->getUserById($user->id); // Api calls To SEARCH and CREATE new UUID record in intercom_users
        // }

        $user = $user->load('intercom');

        $data = $this->prepareData($data, 'contact');

        if($existingUser) {
            $res = $this->updateUser($user, $existingUser->id, $data);
        } else {
            $res = $this->createUser($user, $data);
        }
        
        if($res && isset($res->id)) {
            $user->intercom()->update([
                'intercom_id' => $res->id,
            ]);

            // Attach/deattach contact to/from company 
            $this->syncUserCompany($user, $res);
        } else {
            $user->intercom()->update([
                'intercom_id' => null
            ]);
        }

        $userSchool = $user->school ?? null;

        if ($userSchool) {
            SyncCustomCompanyDataToIntercom::dispatch($userSchool);
        }

        return $res;
    }


    /**
     * Sync User with company
     *
     * @param mixed $user - Laravel User model
     * @param mixed $intercomUser - Intercom User object
     * @return mixed
     */
    public function syncUserCompany(mixed $user, $intercomUser = null)
    {
        try {
            $intercomUser = $intercomUser ?: $this->getUserById($user->id);

            // For users attached to school
            if (isset($user->school)) {
                $school = $user->school;
                $intercomCompany = $school->intercomCompany;
                $intercomCompanyId = $intercomCompany?->intercom_id;
                
                if ($intercomCompanyId) {
                    $company = $this->getCompany($intercomCompanyId);
                    
                    // Make sure it is right company
                    if ($company && $company->company_id != $intercomCompany->uuid) {
                        $this->deleteCompany($intercomCompanyId);
                        $intercomCompanyId = null;
                    }
                } 

                if (!$intercomCompanyId) {
                    $intercomCompanyData = $school->intercom_company_data;
                    
                    if (!empty($intercomCompanyData)) {
                        $res = $this->syncCompany($school, $school->intercom_company_data);
    
                        if ($res) {
                            $intercomCompanyId = $res->id;
                        }
                    }
                }
    
                // Attach contact to a company 
                if ($intercomCompanyId) {
                    $this->attachContactCompany($intercomUser->id, $intercomCompanyId);
                }
            } else {
                $attachedCompanies = $intercomUser->companies?->data;
    
                if (!empty($attachedCompanies)) {
                    foreach ($attachedCompanies as $attachedCompany) {
                        $this->detachContactCompany($intercomUser->id, $attachedCompany->id);
                    }
                }
            }
        } catch (HttpException $e) {
            Log::error($e->getMessage(), [
                'stack' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }
    
    /**
     * Prepare data for intercom
     *
     * @param array $data
     * @param string $model
     * @return array
     */
    public function prepareData(array $data, $model = 'contact'): array
    {
        $customAttributes = $this->getAvailableCDACollection($model);

        if (isset($data['custom_attributes'])) {
            foreach ($data['custom_attributes'] as $name => $value) {
                if ($value == "") {
                    $data['custom_attributes'][$name] = null;
                } else {
                    $existingAttribute = $customAttributes->firstWhere('name', $name);

                    // CHANGE timezone of dates to UTC timestamp being sent to intercom
                    if($existingAttribute->data_type == 'date' && $value != null) {
                        $data['custom_attributes'][$name] = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($value)), config('app.timezone'))
                                                                ->setTimezone('UTC')
                                                                ->timestamp;
                    }

                    if($existingAttribute->data_type == 'integer' && $value != null) {
                        $data['custom_attributes'][$name] = (int)$value;
                    }

                    if($existingAttribute->data_type == 'float' && $value != null) {

                        $data['custom_attributes'][$name] = (float)number_format((float)$value, 2, '.', '');
                    }

                    if($existingAttribute->data_type == 'string' && $value != null && is_array($value)) {
                        $value = array_filter($value, function($arrItem) {
                            return $arrItem != null && $arrItem != "";
                        });
                        
                        $data['custom_attributes'][$name] = $this->truncateIdsForIntercomCDA(implode("|", $value));
                    }
                }
            }
        }

        return $data;
    }


    /**
     * Create User in intercom
     *
     * @param mixed $user - Laravel Model Instance
     * @param array $data
     * @return mixed
     */
    public function createUser($user, $data)
    {
        try{
            return $this->client->contacts->create([
                'external_id' => $user->intercom->uuid,    // Your app's unique user ID
            ] + $data);
        } catch (HttpException $e) {
            Log::error("Intercom API Error while creating user ".$user->id." : " . $e->getMessage(), [
                'user_id' => $user->id,
                'uuid' => $user->intercom?->uuid,
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }


    /**
     * Update User in intercom
     *
     * @param mixed $user - User Model Id
     * @param string|integer $intercomUserId - Intercom User ID
     * @param array $data - fields with values to be updated
     *
     * @return mixed
     */
    public function updateUser($user, $intercomUserId, array $data)
    {
        try{
            $res = $this->client->contacts->update($intercomUserId, $data);

            return $res;
        } catch (HttpException $e) {
            Log::error("Intercom API Error while updating user ".$user->id." : " . $e->getMessage(), [
                'user_id' => $user->id,
                'uuid' => $user->intercom?->uuid,
                'intercom_id' => $intercomUserId,
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }


    /**
     * Sync Company with intercom
     *
     * @param mixed $model
     * @param array $data
     * @return mixed
     */
    public function syncCompany(mixed $model, array $data) : mixed
    {
        if (!$this->isEnabled()) return null;

        $existingCompany = null;

        $intercomCompanyRelation = $model->intercomCompany;

        if ($intercomCompanyRelation) {
            $existingCompany = $this->getCompanyByUuid($intercomCompanyRelation->uuid); // Api calls To SEARCH

            if(empty($existingCompany)) {
                // $model->intercomCompany()->delete();
                $intercomCompanyRelation = null;
            }
        }

        if (empty($intercomCompanyRelation) || empty($existingCompany)) {
            $intercomCompanyRelation = $this->getIntercomCompanyById($model->id);
            $model->intercomCompany = $intercomCompanyRelation;
        }

        // Second, attempt to find the company by their primary id
        // if(empty($existingCompany)) {
        //     $existingCompany = $this->getCompanyById($model->id); // Api calls To SEARCH and CREATE new UUID record in intercom_companies
        // }

        $model->refresh();

        $model = $model->load('intercomCompany');
        
        $data = $this->prepareData($data, 'company');
        
        $res = $this->saveCompany($model, $data);

        if($res && isset($res->id)) {
            $model->intercomCompany()->update([
                'intercom_id' => $res->id,
            ]);
        } else {
            $model->intercomCompany()->update([
                'intercom_id' => null
            ]);
        }

        return $res;
    }


    /**
     * Create or update a company
     *
     * @param mixed $model | Eloquent Model like User or School
     * @param array $data
     * @return mixed
     */
    public function saveCompany($model, array $data)
    {
        try {
            return $this->client->companies->create([
                'company_id' => $model->intercomCompany->uuid
            ] + $data);
        } catch (HttpException $e) {
            Log::error("Intercom API Error while saving company ".$model->id." : " . $e->getMessage(), [
                'model_id' => $model->uuid,
                'uuid' => $model->intercomCompany?->uuid,
                'code' => $e->getCode(),
            ]);

            return null;
        }
    }


    /**
     * Get Intercom Company Object by model id - Primary Key
     *
     * @param integer $modelId
     * @return mixed
     */
    public function getCompanyById($modelId)
    {
        try {
            $intercomCompany = $this->getIntercomCompanyById($modelId);

            $existingCompany = $this->client->get('/companies', [
                'company_id' => (string)($intercomCompany->uuid),
            ]);
    
            return $existingCompany;
        } catch (HttpException $e) {
            return null;
        }
    }


    /**
     * Get Intercom Company Object by uuid
     *
     * @param string $userUuid
     * @return mixed
     */
    public function getCompanyByUuid(string $userUuid)
    {
        try {
            $existingCompany = $this->client->get('/companies', [
                'company_id' => $userUuid,
            ]);
    
            return $existingCompany;
        } catch (HttpException $e) {
            return null;
        }
    }

    /**
     * Get company by intercom id
     *
     * @param string|integer $intercomCompanyId
     * @return mixed
     */
    public function getCompany($intercomCompanyId)
    {
        try {
            return $this->client->companies->getCompany($intercomCompanyId);
        } catch (HttpException $e) {
            return null;
        }
    }


    /**
     * Intercom Company by model id
     *
     * @param integer $id | Eloquent model id
     * @return mixed
     */
    public function getIntercomCompanyById($id)
    {
        return IntercomCompany::firstOrCreate([
            'model_id' => $id
        ]);
    }


    /**
     * Attach contact with company
     *
     * @param string $contactId
     * @param string $companyId
     * @return mixed
     */
    public function attachContactCompany(string $contactId, string $companyId)
    {
        return $this->client->companies->attachContact($contactId, $companyId);
    }

    /**
     * Deattach contact with company
     *
     * @param string $contactId
     * @param string $companyId
     * @return mixed
     */
    public function detachContactCompany(string $contactId, string $companyId)
    {
        return $this->client->companies->detachContact($contactId, $companyId);
    }


    /**
     * Delete company in intercom
     *
     * @param integer $id - primary key
     * @return mixed
     */
    public function deleteCompanyById($id)
    {
        $intercomCompany = $this->getCompanyById($id);

        if($intercomCompany) {
            return $this->deleteCompany($intercomCompany->id);
        }
    }

    /**
     * Delete company by intercom id
     *
     * @param integer|string $intercomId
     * @return mixed
     */
    public function deleteCompany($intercomId)
    {
        try {
            return $this->client->delete('/companies/'.($intercomId), []);
        } catch (HttpException $e) {
            return null;
        }
    }

    // Temporary testing fields for user
    public function getEditableFields($user): array
    {
        $gender = $user->profile?->gender;

        if($gender) {
            if(in_array(strtolower($gender), ['m','male'])) {
                $gender = 'Male';
            } elseif (in_array(strtolower($gender), ['f','female'])) {
                $gender = 'Female';
            } else {
                $gender = 'Other';
            }
        }

        return [
            'email' => $user->email,
            'name' => $user->name,
            'custom_attributes' => [
                'User Role' => $user->role?->name,
                'Gender' => $gender,
                'Country' => $user->state?->country?->name ?: $user->country?->name,
                'State' => $user->state?->name,
                'Postcode' => $user->postcode,
                "cvs" => $user->cvs,
            ],
        ];
    }


    /**
     * Delete user in intercom by model id
     *
     * @param integer $userId - primary key
     * @return mixed
     */
    public function deleteUserById($userId)
    {
        try {
            $intercomUser = $this->getUserById($userId);

            if($intercomUser) {
                return $this->deleteUser($intercomUser->id);
            }
        } catch (HttpException $e) {
            return null;
        }
    }

    /**
     * Delete user by intercom id 
     *
     * @param string $intercomId
     * @return mixed
     */
    public function deleteUser (string $intercomId)
    {
        try {
            return $this->client->contacts->deleteContact($intercomId);
        } catch (HttpException $e) {
            return null;
        }
    }


    /**
     * Sync intercom admins locally
     *
     * @return array
     */
    public function syncAdminsLocally(): array
    {
        $data = $this->getAdmins();

        if($data && isset($data->admins)) {
            foreach ($data->admins as $admin) {
                IntercomAdmin::updateOrCreate([
                    'email' => $admin->email,
                ], [
                    'intercom_id' => $admin->id,
                    'name' => $admin->name,
                ]);
            }
        }

        Cache::forget('intercomSuperAdmin');

        return [
            'success' => true,
            'data' => $data,
        ];
    }


    /**
     * Get all intercom admins from API
     *
     * @return mixed
     */
    public function getAdmins()
    {
        return $this->client->admins->getAdmins();
    }

    /**
     * Get Inetrcom Super admin from Database Table 'intercom_admins'
     *
     * @param boolean $cached
     * @return void
     */
    public function getSuperAdmin($cached = false)
    {
        if($cached) {
            return Cache::remember('intercomSuperAdmin', 60 * 60 * 24 , function () {
                return IntercomAdmin::where('email', config('intercom.admin.email'))->first();
            });
        } else {
            return IntercomAdmin::where('email', config('intercom.admin.email'))->first();
        }
    }


    /**
     * Send Mail using intercom api
     *
     * @param string $subject Subject of email
     * @param string $body Content
     * @param string|integer $fromIntercomId
     * @param string|integer $to
     * @param string $recipientType  "id" (i.e intercom_id) or "email"
     * @param string $template Possible values 'plain' or 'personal'
     * @return mixed
     */
    public function sendMail(
        string $subject,
        string $body,
        $fromIntercomId = null,
        $to = null,
        string $recipientType = 'id',
        string $template = "plain"
    )
    {
        $recipientType = $recipientType == 'id' ? $recipientType : 'email';

        return $this->client->messages->create([
            'message_type' => 'email',
            'subject' => $subject,
            'body' => $body,
            'template' => $template,
            'from' => [
                'id' => $fromIntercomId,
                'type' => 'admin',
            ],
            'to' => [
                $recipientType => $to,
                'type' => 'user',
            ],
        ]);
    }

    
    /**
     * All editable CDAs available in intercom
     *
     * @param string $model
     * @param boolean $cached
     * @return array
     */
    public function getFillableAttributes($model = 'contact', $cached = false): array
    {
        if($cached) {
            return Cache::rememberForever('intercomFillableAttributes', function () use($model) {
                return $this->getAllAttributes($model);
            });
        } else {
            Cache::forget('intercomFillableAttributes');
            return $this->getAllAttributes($model);
        }
    }


    public function getAllAttributes($model = 'contact')
    {
        $res =  $this->client->get('/data_attributes', [
            'model' => $model,
        ]);

        return [
            'standard' => array_filter($res->data, function($item) {
                return $item->custom == false && $item->api_writable;
            }),
            'custom' => array_filter($res->data, function($item) {
                return $item->custom == true && $item->api_writable;
            }),
        ];
    }

    public function syncCustomAttributeFields($model = 'contact')
    {
        $intercomDataAttributes = $this->getFillableAttributes($model);
        $customAttributes = $this->getAvailableCDA($model);

        $intercomCDANames = [];
        $intercomCustomDataAttributes = collect();

        if(!empty($intercomDataAttributes['custom'])) {
            $intercomCustomDataAttributes = collect($intercomDataAttributes['custom']);
            $intercomCDANames = $intercomCustomDataAttributes->pluck('name', 'id')->toArray();
        }

        foreach ($customAttributes as $customAttribute) {
            if(!in_array($customAttribute['name'], $intercomCDANames)) {
                try{
                    $res =  $this->client->post('/data_attributes', [
                        'model' => $model,
                    ] + $customAttribute);
    
                    if($res && isset($customAttribute['options'])) {
                        $res = $this->updateCDA($res->id, [
                            'options' => $customAttribute['options']
                        ]);
                    }
                } catch (Exception $e) {
                    dd('Error in following CDA', $customAttribute, $e->getMessage());
                }
            }
        }
    }

    public function updateCDA($id, $data)
    {
        $putData = [];

        if(isset($data['options'])) {
            $putData['options'] = $data['options'];
        }

        return $this->client->put('/data_attributes/'. $id, $putData);
    }

    public function getAvailableCDA($model = 'contact')
    {
        // Company CDAs
        if ($model == 'company') {
            return $this->getCompanyCDAsArray();
        }

        return $this->getContactCDAsArray();
    }


    /**
     * Company CDAs
     * 
     * ALLOWED DATA TYPES: 
     * "string", "integer", "float", "boolean", "date"
     * 
     * https://developers.intercom.com/docs/references/rest-api/api.intercom.io/data-attributes/data_attribute
     * 
     * @return array
     */
    public function getCompanyCDAsArray(): array
    {
        return [
            [
                'name' => 'School_Type',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Independent'],
                    ['value' => 'Catholic'],
                    ['value' => 'Government'],
                ],
            ],
            [
                'name' => 'Logo',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Campuses',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School Gender',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Boys'],
                    ['value' => 'Girls'],
                    ['value' => 'Coed'],
                ],
            ],
            [
                'name' => 'School_Code',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'country',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'state',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Region',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Suburb',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'postcode',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School Contact Person',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Email',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Phone',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Subscription Start Date',
                'data_type' => 'date',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Subscription_End_Date',
                'data_type' => 'date',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Account_Limit',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Percentage of Accounts Used',
                'data_type' => 'float',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Active Accounts',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Inactive Accounts',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Graduated Accounts',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Teacher Accounts',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Parent Accounts',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'ICSEA',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Location',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Indigenous Percentage',
                'data_type' => 'float',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School Level',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Primary'],
                    ['value' => 'Secondary'],
                    ['value' => 'Combined'],
                ],
            ],
            // [
            //     'name' => 'Account Status',
            //     'data_type' => 'string',
            //     'messenger_writable' => true,
            // ],
            [
                'name' => 'Product Access',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
        ];
    }



    /**
     * Contacts CDAs
     *
     * @return array
     */
    public function getContactCDAsArray()
    {
        return [
            [
                'name' => 'User Role',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'First Name',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Last Name',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Campus',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Login Count',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Last Login',
                'data_type' => 'date',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Avg Login Time',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Account Created',
                'data_type' => 'date',
                'messenger_writable' => true,
                // 'options' => [
                //     ['value' => 'Yes'],
                //     ['value' => 'No']
                // ],
            ],
            // STUDENT CDAs
            [
                'name' => 'Gender',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Male'],
                    ['value' => 'Female'],
                    ['value' => 'Other'],
                ],
            ],
            [
                'name' => 'Country',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'State',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School State',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Postcode',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Stage of life',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Year',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Graduated Year',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Registered',
                'data_type' => 'string',
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
                'messenger_writable' => true,
            ],
            [
                'name' => 'Intentions',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Industries',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Institutes',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            // NEW GAMEPLAN CDAs - START
            [
                'name' => 'Faculty',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Course Year',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Experience',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'International Status',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            // NEW GAMEPLAN CDAs - END
            [
                'name' => 'Lessons',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Virtual Work Experience',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Skills Training',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Graduated',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
            ],
            [
                'name' => 'Profling',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
            ],
            [
                'name' => 'Resumes',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
            ],
            [
                'name' => 'ePortfolio',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
            ],
            [
                'name' => 'Subject Selections Tool Access',
                'data_type' => 'string',
                'messenger_writable' => true,
                'options' => [
                    ['value' => 'Yes'],
                    ['value' => 'No']
                ],
            ],
            // Teacher CDAs
            [
                'name' => 'Position',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Account Type',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Account Status',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Subscription End Date',
                'data_type' => 'date',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Access Type',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            // Parents CDAs
            [
                'name' => 'Children',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            // Schools CDAs
            [
                'name' => 'School Code',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'School Type',
                'data_type' => 'string',
                'messenger_writable' => true,
            ],
            [
                'name' => 'Account Limit',
                'data_type' => 'integer',
                'messenger_writable' => true,
            ],
        ];
    }

    public function getDynamicGameplanQuestionLabels ()
    {
        return [
            'Faculty',
            'Course Year',
            'Experience',
            'International Status',
        ];
    }

    public function getAvailableCDACollection($model = 'contact'): mixed
    {
        return collect($this->getAvailableCDA($model))->map(function ($item) {
            return (object) $item;
        });
    }

    public function unArchiveUser($intercomUserId): mixed
    {
        return $this->client->post("/contacts/" . $intercomUserId . "/unarchive", []);
    }

    public function submitEvent($eventName, $userUuid, $metaData): mixed
    {
        if (!$this->isEnabled()) return null;

        return $this->client->events->create([
            "event_name" => $eventName,
            "created_at" => Carbon::now('UTC')->timestamp,
            "user_id" => $userUuid,
            "metadata" => $metaData,
        ]);
    }


    public function isEnabled()
    {
        if (config('app.env') != 'production') {
            return false;
        }

        return true;
    }


    public function truncateIdsForIntercomCDA(string|array $input)
    {
        $maxlength = 255;
        $separator = '|';

        if (is_array($input)) {
            $string = $separator . implode($separator, $input) . $separator;
        } else {
            $string = $separator . trim($input, $separator) . $separator;
        }

        if (strlen($string) > $maxlength) {
            // truncate
            $string = substr($string, 0, $maxlength); 

            // Remove unnecessary ids
            if (substr($string, -1) != $separator) {
                $arr = explode($separator, $string);
                array_pop($arr);
                $string = implode($separator, $arr) . $separator;
            }
        }

        return $string;
    }

    
}