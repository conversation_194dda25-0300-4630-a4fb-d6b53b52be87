@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
{{-- @section('breadcrumbs', Breadcrumbs::render('edit-profile')) --}}
@section('content')
    @include('partials.studentbannertabs')
    @push('styles')
        <style>
            .pb-8 {
                padding-bottom: 2.5rem;
            }
        </style>
    @endpush
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card card-transparent">
                <div class="card-header text-center">
                    <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                    <p class="uppercase bold oswald">Your Account</p>
                </div>
                <div class="card-block">
                    <form id="form-personal" method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>First Name</label>
                                    <input type="text" class="form-control" name="firstname" required=""
                                        value="{{ $user->profile->firstname }}" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Last Name</label>
                                    <input type="text" class="form-control" name="lastname" required=""
                                        value="{{ $user->profile->lastname }}" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" value="{{ $user->email }}"
                                        required="" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default" aria-required="true">
                                    <label>Password</label>
                                    <input type="password" class="form-control" name="password"
                                        placeholder="Enter only if you want to change" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            {{-- <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required"
                                    aria-required="true">
                                    <label>Country</label>
                                    <select name="country" class="form-control" data-init-plugin="select2"
                                        data-placement="Select..">
                                        <option selected disabled></option>
                                        @foreach ($countries as $country)
                                            <option @if ($user->state || $country->id == $user->country_id) selected="selected" @endif
                                                value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div> --}}
                            {{-- <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required"
                                    aria-required="true">
                                    <label>State</label>
                                    <select name="state" class="form-control" data-init-plugin="select2"
                                        data-placement="Select.." >  
                                        <option selected ></option>
                                        @if ($user->state)
                                            @foreach ($user->state->country->states as $state)
                                                @if($state->id == $user->state_id)
                                                    <option  selected="selected" value="{{ $state->id }}">{{ $state->name }}</option>
                                                @endif
                                            @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div> --}}
                            @if($user->school->show_postcode)
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required" aria-required="true">
                                        <label>Postcode</label>
                                        <input type="text" class="form-control" name="postcode" required=""
                                            value="{{ $user->postcode }}" aria-required="true">
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Gender</label>
                                    <select class="full-width" id="gender" name="gender" data-init-plugin="select2"
                                        data-placeholder="Select..">
                                        <option value=""></option>
                                        <option value="M" @if ($user->profile->gender == 'M') selected @endif>Male
                                        </option>
                                        <option value="F" @if ($user->profile->gender == 'F') selected @endif>Female
                                        </option>
                                        <option value="O" @if ($user->profile->gender && $user->profile->gender != 'M' && $user->profile->gender != 'F') selected @endif>Other / Prefer not to say
                                        </option>
                                    </select>
                                </div>
                            </div>
                            {{-- <div class="col-md-6 {{ $user->profile->gender && $user->profile->gender != 'M' && $user->profile->gender != 'F' ? '' : 'd-none' }}"
                                id="edit_other_gender">
                                <div class="form-group form-group-default required">
                                    <label>Other Gender</label>
                                    <input type="text" class="form-control other_gender" name="other_gender"
                                        @if ($user->profile->gender != 'M' && $user->profile->gender != 'F') value ="{{ $user->profile->gender }}" @endif />
                                </div>
                            </div> --}}
                        </div>
                        <div class="clearfix"></div>
                        <button class="btn btn-black" type="submit">Update</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
        <script>
            $(function() {
                if ($('#stage').val() == 'school') {
                    $('#school-detail').removeClass('d-none');
                    $('#what-stage').addClass('d-none');
                    $('#stage-col').addClass('col-md-12');
                    $('#stage-col').removeClass('col-md-6');
                    $('#what-stage select').val('').trigger("change.select2");
                } else {
                    $('#stage-col').addClass('col-md-6');
                    $('#stage-col').removeClass('col-md-12');
                    $('#what-stage').removeClass('d-none');
                    $('#school-detail').addClass('d-none');
                    $('#school-detail select, #school-detail input').val('').trigger("change.select2");
                }
                $('#stage').change(function() {
                    if ($(this).val() == 'school') {
                        $('#stage-col').addClass('col-md-12');
                        $('#stage-col').removeClass('col-md-6');
                        $('#school-detail').removeClass('d-none');
                        $('#what-stage').addClass('d-none');
                        $('#what-stage select').val('').trigger("change.select2");
                    } else {
                        $('#stage-col').addClass('col-md-6');
                        $('#stage-col').removeClass('col-md-12');
                        $('#what-stage').removeClass('d-none');
                        $('#school-detail').addClass('d-none');
                        $('#school-detail select, #school-detail input').val('').trigger("change.select2");
                    }
                });
            });
            jQuery(document).ready(function() {
                jQuery("select[name=country]").change(function() {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getStates') }}",
                        data: {
                            id: jQuery(this).val(),
                        },
                        success: function(response) {
                            jQuery("select[name=state]").html(
                            '<option selected disabled></option>');
                            jQuery.each(response, function(i, v) {
                                jQuery("select[name=state]").append('<option value="' + v
                                    .id + '">' + v.name + '</option>')
                            });
                        },
                        fail: function() {
                            jQuery("select[name=state]").html(
                            '<option selected disabled></option>');
                        }
                    });
                });

                var num = 1;


                jQuery.validator.addMethod("notEqualTo", function(value, element, options) {
                    // get all the elements passed here with the same class
                    var elems = $(element).parents('form').find(options[0]);
                    // the value of the current element
                    var valueToCompare = value;
                    // count
                    var matchesFound = 0;
                    // loop each element and compare its value with the current value
                    // and increase the count every time we find one
                    jQuery.each(elems, function() {
                        thisVal = $(this).val();
                        if (thisVal == valueToCompare) {
                            matchesFound++;
                        }
                    });
                    // count should be either 0 or 1 max
                    if (this.optional(element) || matchesFound <= 1) {
                        //elems.removeClass('error');
                        return true;
                    } else {
                        //elems.addClass('error');
                    }
                }, "")
                jQuery('#formParentInvitation').validate({
                    errorElement: 'span',
                    errorClass: 'help-block error-help-block',

                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length ||
                            element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            error.insertAfter(element.parent());
                            // else just place the validation message immediatly after the input
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    highlight: function(element) {
                        $(element).closest('.form-group').removeClass('has-success').addClass(
                        'has-error'); // add the Bootstrap error class to the control group
                    },
                    rules: {
                        'email[1]': {
                            required: true,
                            email: true,
                            notEqualTo: ['input[type=email]'],
                        },
                        'relation[1]': 'required',
                    },
                    messages: {
                        'email[1]': {
                            notEqualTo: "Email already entered!",
                        },

                    }
                })

                // jQuery(document).on('change','#gender', function() {
                // jQuery('#gender').on('change', function() {
                //     if (this.value == 'Other') {
                //         jQuery("#edit_other_gender").removeClass('d-none');
                //     } else {
                //         jQuery("#edit_other_gender").addClass('d-none');
                //         jQuery('#form-personal .other_gender').val('');
                //     }
                // });
                jQuery('#form-personal').validate({
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        fullname: 'required',
                        email: {
                            required: true,
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $("input[name='email']").val();
                                    },
                                    id: {{ Auth::id() }},
                                },
                            }
                        },
                        school: 'required',
                        year: 'required',
                        country: 'required',
                        state: 'required',
                        postcode: 'required',
                        stage: 'required',
            
                        // other_gender: {
                        //     required: function() {
                        //         return (jQuery('#gender').val() == 'Other');
                        //     }
                        // },
                    },
                    messages: {
                        email: {
                            remote: "Email already in use!"
                        },
                    }
                })

            });
        </script>
    @endpush
@endsection
