@push('stylesheets')
<link media="screen" type="text/css" rel="stylesheet"
    href="{{asset('assets/plugins/switchery/css/switchery.min.css')}}">
@endpush
<div class="modal fade fill-in" id="modalStaffEdit" role="dialog" aria-hidden="true">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        <i class="pg-close">
        </i>
    </button>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-left p-b-5">
                    <span class="semi-bold">
                        Edit Staff
                    </span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class=" col-lg-12 ">
                        <!-- START card -->
                        <div class="card card-transparent">
                            <div class="card-block">
                                <form method="POST" action="" id="form-staffedit" autocomplete="off">
                                    {{ csrf_field() }}
                                    <input type="hidden" name="_method" value="PUT">
                                    <input type="hidden" name="id" id="id">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>First Name</label>
                                                <input type="text" id="firstname" class="form-control"
                                                    name="firstname" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Last Name</label>
                                                <input type="text" id="lastname" class="form-control" name="lastname" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="@if(Auth::user()->isAdmin()) col-md-6 @else col-md-12 @endif">
                                            <div class="form-group form-group-default required">
                                                <label>Email</label>
                                                <input type="email" id="email" class="form-control" name="email" />
                                            </div>
                                        </div>
                                        @if(Auth::user()->isAdmin())
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Password</label>
                                                <input type="password" id="password" class="form-control"
                                                    name="password" placeholder="Enter only if you want to change" />
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Access level</label>
                                                <select class="full-width" id="access" name="access"
                                                    data-init-plugin="select2" data-placeholder="Select.." required>
                                                    <option value=""></option>
                                                    <option value="Full"
                                                        title="Manager access + can manage subscription details, teacher accounts, student accounts and access to program features.">
                                                        Administrator</option>
                                                    <option value="Manager"
                                                        title="Content access + can view student details and provide feedback on submitted work. Can access group reports.">
                                                        Manager</option>
                                                    <option value="Content"
                                                        title="Can view content and teaching resources. Cannot access individual student information.">
                                                        Content</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Country</label>
                                                <select class="full-width" id="country" name="country"
                                                    data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    @foreach($countries as $country)
                                                    <option value="{{ $country->id }}"> {{ $country->name }} </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="staff-edit-state-container" style="display:none;">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>State</label>
                                                <select id="state_id" class="full-width" name="state"
                                                    data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Postcode</label>
                                                <input type="number" id="postcode" placeholder="" class="form-control"
                                                    name="postcode">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        @if (Auth::user()->role->name == 'Admin')
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Organisation</label>
                                                <select class="full-width" id="organisation_id" name="organisation"
                                                    data-init-plugin="select2" data-placeholder="Select...">
                                                    <option value="">Select...</option>
                                                    @foreach ($organisations as $key => $organisation)
                                                    <option value="{{$organisation->id}}">{{$organisation->name}}
                                                    </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        @endif
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Position</label>
                                                <input type="text" id="teacher_position" class="form-control"
                                                    name="position" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row clearfix" id="campus-edit" style="display:none;">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Campuses</label>
                                                <select class="full-width" id="campusIds" name="campuses[]"
                                                    data-init-plugin="select2" data-placeholder="Select.." multiple>
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- <div class="row">
                                        <div class="col-md-6">
                                            <label for="" class="p-r-10">Subscribe to newsletter </label>
                                            <input type="hidden" class="" id="" name="newsletter" value="0" />
                                            <input type="checkbox" class="" id="newsletter" name="newsletter"
                                                value="1" />
                                        </div>
                                    </div> --}}
                                    <div class="clearfix text-right">
                                        <button class="btn btn-primary" type="submit">
                                            Update
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- END card -->
                    </div>
                </div>
            </div>
            <div class="modal-footer"></div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
@push('scripts')
<script>
    jQuery(document).ready(function () {
    //  var newsletterSwitch = document.querySelector('#modalStaffEdit #newsletter');
    // Helper to populate state dropdown and show/hide container
    function populateStates(countryId, selectedStateId) {
        var stateSelect = jQuery("#form-staffedit select[name=state]");
        stateSelect.html('<option value=""></option>');
        if (!countryId) {
            jQuery("#staff-edit-state-container").hide();
            return;
        }
        jQuery.ajax({
            type: "get",
            url: "{{route('getStates')}}",
            data: { id: countryId },
            success: function(response) {
                        jQuery("select[name=state]").html('<option selected disabled></option>');
                        // New logic for states - START
                        if (!response || response.length == 0) {
                            jQuery("#staff-edit-state-container").hide();
                            return;
                        }
                        jQuery("#staff-edit-state-container").show();
                        // New logic for states - END

                        jQuery("#form-staffedit select[name=state]").html('<option value=""></option>');
                        jQuery.each(response, function(i, v) {
                            var selected = v.id == selectedStateId ? 'selected' : '';
                            jQuery("#form-staffedit select[name=state]").append('<option value="' + v.id + '" ' + selected + '>' + v.name + '</option>');
                        });
                        jQuery("#form-staffedit select[name=state]").trigger('change.select2');

                        // Clear selectedStateId after use
                        selectedStateId = null;
                        },
                        fail: function() {
                             // New logic for states - START
                                jQuery("#staff-edit-state-container").hide();
                             // New logic for states - END
                            jQuery("#form-staffedit select[name=state]").html('<option value=""></option>');
                        }
        });
    }

    // On country change
    jQuery("#form-staffedit select[name=country]").change(function() {
        var countryId = jQuery(this).val();
        populateStates(countryId, null);
    });

     $('#form-staffedit select[name=organisation]').change(function () {
        jQuery('#form-staffedit select[name="campuses[]"]').html('<option value=""></option>');
        jQuery.ajax({
            type: "get",
            url: "{{route('getOrganisationCampuses')}}",
            data: {
                id: jQuery(this).val()
            },
            dataType: 'json',
            success: function (response) {
                if(Object.keys(response).length) {
                    jQuery.each(response, function (id, name) {
                        jQuery('#form-staffedit select[name="campuses[]"]').append('<option value="'+id+'">'+name+'</option>')
                    });
                    jQuery('#campus-edit').show();
                    jQuery('#form-staffedit select[name="campuses[]"]').rules('add', 'required');
                } else {
                    jQuery('#campus-edit').hide();
                    jQuery('#form-staffedit select[name="campuses[]"]').rules('remove', 'required');
                }
            },
        });
    });

     jQuery(document).on('hide.bs.modal', '#modalStaffEdit', function () {
        jQuery('#campus-edit').hide();
        jQuery("#form-staffedit input:not([type=hidden]):not([type=submit]), #form-staffedit select:not([type=hidden]").val('');
        jQuery('#form-staffedit select').trigger("change.select2");
    });

     var $trigger;
     jQuery('#modalStaffEdit').on('show.bs.modal', function (e) {
        jQuery("#form-staffedit input:not([type=hidden]):not([type=submit]), #form-staffedit select:not([type=hidden]").val('');
        $trigger = $(e.relatedTarget);
        jQuery("#form-staffedit").attr("action", "staff/" + $trigger.data('id'));
        // Get staff data first
        jQuery.ajax({
            url: "staff/" + $trigger.data('id'),
            dataType: 'json',
            success: function (data) {
                // Set form fields as before
                var selectedStateId = data.state_id || '';
                jQuery.each(data, function (i, v) {
                    if (i == 'state_id' || i == 'campusIds') {
                        var timer;
                        timer = setInterval(function(){
                            jQuery('#modalStaffEdit #' + i).val(v);
                            jQuery('#modalStaffEdit #' + i).trigger("change.select2");
                            clearTimeout(timer);
                        }, 1000);
                    } else {
                        jQuery('#modalStaffEdit #' + i).val(v);
                        if (jQuery('#modalStaffEdit #' + i).hasClass('select2-hidden-accessible')) {
                            jQuery('#modalStaffEdit #' + i).trigger("change.select2");
                        }
                    }
                });
                // Populate state dropdown for the country and select the correct state
                populateStates(data.country, selectedStateId);
            }
        });
    });
     jQuery('#position_id').select2({
        tags: true
    });
     jQuery('#modalStaffEdit').on('shown.bs.modal', function (e) {
        jQuery('#form-staffedit').validate({
            errorElement: 'span',
            errorClass: 'help-block error-help-block',

            errorPlacement: function (error, element) {
                if (element.parent('.input-group').length ||
                    element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                    error.insertAfter(element.parent());
                        // else just place the validation message immediatly after the input
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function (element) {
                    jQuery(element).closest('.form-group').removeClass('has-success').addClass('has-error'); // add the Bootstrap error class to the control group
                },

                focusInvalid: false, // do not focus the last invalid input
                rules: {
                    firstname: 'required',
                    lastname: 'required',
                    email: {
                        required: true,
                        email: true,
                        remote: {
                            url: "/checkEmailOnUpdate",
                            type: "get",
                            data: {
                                email: function () {
                                    return jQuery("#email").val();
                                },
                                id: function () {
                                    return jQuery("#id").val();
                                },
                            },
                        },
                    },
                    country:'required',
                    state:{
                         required: function() {
                            return (jQuery('select[name=state] option[value]').length > 0);
                        }
                    },
                    password: {
                        required: false,
                        minlength: 5,
                    },
                    access: 'required',
                    organisation: 'required',
                    position: 'required',
                },
                messages: {
                    email: {
                        remote: 'This email is already in use!'
                    }
                }
            })
    });
 });

</script>
{{-- {!! JsValidator::formRequest('App\Http\Requests\UpdateTeacher')->selector('#form-staffedit') !!} --}}
@endpush