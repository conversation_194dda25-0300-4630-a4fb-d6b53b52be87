import { defineStore } from "pinia";
export let useRegisterStore = defineStore('RegisterStore', {
    state: () => ({
      email: '',
      isNew: false,
      underUniversity: false,
      /** @type {string[]} */
      instituteDomain: [],
      showPostcode:true,
      accountType:'student',
      currentStage:0,
      privacyLink: '',
      studentDetail:{
        email:'',
        inSchool: "inschool",
        schoolUnavailable:false,
        school:{
          id:'',
          name:'',
          logo:'',
          campuses:[],
          years:[]
        },
        // domain:'',
        schoolName:'',
        schoolPassword:'',
        schoolCampus:'',
        schoolCampuses:[],
        firstName:"",
        lastName:"",
        password:"",
        password_confirmation:"",
        state:"",
        country:"",
        postcode:"",
        gender:"",
        genderOther:"",
        year:"",
        gradYear:"",
        parent:{
          firstname:'',
          lastname:'',
          email:''
        }
      },
      parentDetail:{
        email:'',
        plan : 'limited',
        children:[],
        parentEmail:"",
        childEmail:"",
        childPlan:"",
        firstname:"",
        lastname:"",
        password:"",
        confirm_password:"",
        state:"",
        country:"",
        postcode:"",
      },
      teacherDetail:{}
    }),
    persist: true,

    actions: {

    },

    getters: {

    }
  });
