@extends('layouts.auth')
@push('styles')
    <style>
        .select2-container--default .select2-results__option[aria-disabled=true] {
            display: none;
        }
    </style>
@endpush
@section('content')
<div class="register-bg">
    <div class="register-container full-height sm-p-t-30">
        <div class="card no-border">
            <div class="card-block">
                <div class="d-flex justify-content-center flex-column full-height ">
                    <a href="/"><img src="{{asset('images/favicon.png')}}" alt="logo" data-src="{{asset('images/favicon.png')}}" data-src-retina="{{asset('images/favicon.png')}}" width="60" height=60></a>
                    <h3 class="oswald ls-3 bold uppercase">Register With Us</h3>
                    <p>
                        Create a careers account.
                    </p>
                    <form class="" id="form-register" role="form" method="POST" action="{{ route('createaccount') }}">
                        {{ csrf_field() }}
                        <input type="hidden" name="user_id" value="{{ $student->id }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required @if(($student->profile->firstname)) disabled @endif">
                                    <label>First Name</label>
                                    <input id="firstname" type="text" class="form-control" placeholder="Enter first name " name="firstname" value="{{ $student->profile->firstname }}" required  @if(($student->profile->firstname)) readonly="readonly" @endif>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required @if(($student->profile->lastname)) disabled @endif">
                                    <label>Last Name</label>
                                    <input id="lastname" type="text" class="form-control" placeholder="Enter last name " name="lastname" value="{{ $student->profile->lastname }}" required @if(($student->profile->lastname)) readonly="readonly" @endif>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group form-group-default required @if($student->email) disabled @endif">
                                    <label>Email</label>
                                    <input id="email" type="email" class="form-control" placeholder="Enter email-address " name="email" value="{{ $student->email }}" required @if(($student->email)) disabled @endif>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Password</label>
                                    <input id="password" type="password" placeholder="Enter password" class="form-control" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Confirm Password</label>
                                    <input id="password-confirm" type="password" placeholder="Confirm password" class="form-control" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-12">
                                <div class="form-group form-group-default form-group-default-select2 required @if($student->state) disabled @endif">
                                    <label>Country</label>
                                    <select class="full-width" name="country" data-init-plugin="select2" data-placeholder="Select.." @if($student->state) disabled @endif>
                                        <option value=""></option>
                                        @foreach($countries as $country)
                                            <option value="{{ $country->id }}" @if (($student->state) || $student->country_id == $country->id) selected @endif> {{ $country->name }} </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required @if($student->state) disabled @endif">
                                    <label>State</label>
                                    <select id="state"  class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select.." @if($student->state) disabled @endif required>
                                        <option value=""></option>
                                        @if ($student->state)
                                        <option value="{{$student->state_id}}" selected>{{$student->state->name}}</option>
                                        @else
                                        @foreach($states as $state)
                                            <option value="{{ $state->id }}" data-country="{{ $state->country_id }}" disabled> {{ $state->name }} </option>
                                        @endforeach
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required @if($student->postcode) disabled @endif">
                                    <label>Postcode</label>
                                    <input id="postcode" type="number" placeholder="Enter postcode" class="form-control" name="postcode" value="{{$student->postcode}}" @if($student->postcode) disabled @endif required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required @if($student->profile->gender) disabled @endif">
                                    <label>Gender</label>
                                    <select class="form-control" id="gender" name="gender" data-init-plugin="select2" data-placeholder="Select.." @if($student->profile->gender) disabled @endif>
                                        <option value="" selected disabled>Select...</option>
                                        <option value="M" @if($student->profile->gender == 'M') selected @endif >Male</option>
                                        <option value="F" @if($student->profile->gender == 'F') selected @endif >Female</option>
                                        <option value="Other" @if($student->profile->gender != 'M' && $student->profile->gender != 'F' && $student->profile->gender != null) selected @endif >Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 @if($student->profile->gender == null || $student->profile->gender == 'M' || $student->profile->gender == 'F') d-none @endif" id="other_gender">
                                <div class="form-group form-group-default required">
                                    <label>Other Gender</label>
                                    <input type="text" class="form-control other_gender" name="other_gender" @if ($student->profile->gender != 'M' && $student->profile->gender != 'F') value="{{ $student->profile->gender }}" @endif />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required @if(($student->profile->standard_id)) disabled @endif">
                                    <label>Year</label>
                                    <select class="form-control" name="year" data-init-plugin="select2" @if(($student->profile->standard_id)) disabled @endif data-placeholder="Select..">
                                        <option value="" disabled selected>Any</option>
                                        @foreach ($years as $key => $year)
                                        <option value="{{$year->id}}" @if( $student->profile->standard_id == $year->id) selected @endif >{{$year->title}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row m-t-10">
                            <div class="col-lg-6">
                                <p>
                                    <small>
                                        I agree to the
                                        <a href="/terms" target="_blank" class="text-info">Terms</a> and
                                        <a href="/privacy" target="_blank" class="text-info">Privacy</a>.
                                    </small>
                                </p>
                            </div>
                            <div class="col-lg-6 text-right">
                                <a href="#" data-toggle="modal" data-target="#contactModal" class="text-info small">Help? Contact Support</a>{{--  |
                                <a href="/faq" class="text-info" target="_blank">FAQ</a> --}}
                            </div>
                        </div>
                        <button class="btn btn-primary btn-cons m-t-10" type="submit">Create a new account</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@push('modals')
    @include('partials.contactmodal')
@endpush
@push('scriptslib')
    <script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
    <!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.17.0/jquery.validate.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.17.0/additional-methods.js"></script>-->
@endpush

@push('scripts')
    <script>
        $(document).ready(function () {

            jQuery('#gender').on('change', function() {
                if (this.value == 'Other') {
                    jQuery("#other_gender").removeClass('d-none');
                } else {
                    jQuery("#other_gender").addClass('d-none');
                    jQuery('#form-register .other_gender').val('');
                }
            });


            jQuery("select[name=country]").change(function() {
                jQuery("select[name=state] option").prop('disabled', true);
                jQuery("select[name=state]").val('').select2();

                jQuery("select[name=state] option[data-country="+jQuery(this).val()+"]").prop('disabled', false)
                jQuery("select[name=state]").select2();
                // jQuery.ajax({
                //     type: "get",
                //     url: "{{route('getStates')}}",
                //     data: {
                //         id: jQuery(this).val()
                //     },
                //     success: function (response) {
                //         jQuery("select[name=state]").html('<option value=""></option>');
                //         jQuery.each(response, function (i, v) {
                //             jQuery("select[name=state]").append('<option value="'+v.id+'">'+v.name+'</option>')
                //         });
                //     },
                //     fail: function() {
                //         jQuery("select[name=state]").html('<option value=""></option>');
                //     }
                // });
            });

            $("#form-register").validate({
                rules: {
                    firstname: {
                        required: true
                    },
                    lastname: {
                        required: true
                    },
                    email: {
                        required: true,
                        email: true,
                        remote: {
                            url: "/checkUserName",
                            type: "get",
                            data: {
                                email: function () {
                                    return $("input[name='email']").val();
                                }
                            },
                        }
                    },
                    gender: {
                        required: true
                    },
                    other_gender: {
                        required: function() {
                            return (jQuery('#gender').val() == 'Other');
                        }
                    },
                    password: {
                        required: true,
                        minlength: 5
                    },
                    password_confirmation: {
                        equalTo: "#password"
                    },
                    country: 'required',
                    state: 'required',
                    postcode: 'required',
                    year: {
                        required: true
                    }
                },
                messages: {
                    firstname: {
                        required: "Please enter first name here"
                    },
                    lastname: {
                        required: "Please enter last name here"
                    },
                    email: {
                        required: "Please enter a valid email address",
                        remote: "Email already exists! Please enter new email"
                    },
                    gender: {
                        required: "Please select"
                    },
                    password: {
                        required: "Please enter a strong password"
                    },
                    password_confirmation: {
                        equalTo: "Please enter a same password again"
                    },
                    year: {
                        required: "Please select"
                    },
                },
                submitHandler: function(form) {
                    //this runs when the form validated successfully
                    jQuery(form).find(":submit").attr('disabled', 'disabled');
                    form.submit();
                }
            });
        });
    </script>
@endpush
@endsection
