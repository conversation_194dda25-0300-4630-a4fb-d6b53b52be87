@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('staff'))
@section('content')
    <style>
        .tooltip-inner {
            text-align: left;
        }
    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Search
                    </div>
                </div>
                <div class="card-block">
                    <form class="" action="" method="post" id="search-form">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-3">
                                <div class="form-group form-group-default">
                                    <label>Staff name</label>
                                    <input type="text" class="form-control" name="name" />
                                </div>
                            </div>
                            @if (Auth::user()->role->name == 'Admin')
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Organisation</label>
                                        <select class="full-width" name="organisation" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($organisations as $key => $organisation)
                                                <option value="{{ $organisation->id }}">{{ $organisation->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Organisation State</label>
                                        <select class="full-width" name="state" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($states as $state)
                                                <option value="{{ $state->id }}"> {{ $state->name }} </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            {{-- @if (Auth::user()->role->name == 'Admin') --}}
                            <div class="col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Organisation Type</label>
                                    <select class="full-width" name="type" data-init-plugin="select2">
                                        <option value="" selected>Any</option>
                                        <option value="Independent"> Independent </option>
                                        <option value="Catholic"> Catholic </option>
                                        <option value="Government"> Government </option>
                                    </select>
                                </div>
                            </div>
                            {{-- @endif --}}
                        </div>
                        <div class="row clearfix">
                            @if (Auth::user()->role->name == 'Admin')
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Organisation Gender</label>
                                        <select class="full-width" name="gender" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            <option value="Boy">Boy</option>
                                            <option value="Girl">Girl</option>
                                            <option value="Coed">Coed</option>
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <div class="form-group form-group-default form-group-default-select2">
                                    <label>Active/Inactive</label>
                                    <select class="full-width" name="active_inactive" data-init-plugin="select2">
                                        <option value="" selected>Any</option>
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <br>
                                <button class="btn btn-primary" type="submit">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Staff
                    </div>
                    <div class="card-controls">
                        <ul>
                            {{-- <li> --}}
                            <!-- <a data-target="#modalTeacherAdd" data-toggle="modal" id="btnFillSizeToggler" class="" href="#">
                                    <i class="fs-14 pg-plus"></i>
                                </a> -->
                            {{-- <button data-target="#modalTeachersImport" data-toggle="modal" id="btnTeachersImport"
                            class="btn btn-primary btn-cons">
                            <i class="fa fa-upload"></i> Bulk Import
                        </button> --}}
                            {{-- </li> --}}
                            <li>
                                <button data-target="#modalStaffAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                                    <i class="fa fa-plus"></i> Add New
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-block">
                    <table class="table table-hover custom-datatable no-footer" id="teachers-table">
                        <thead>
                            <tr>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Access</th>
                                <th>Country</th>
                                <th>State</th>
                                <th>Position</th>
                                <th>Organisation</th>
                                <th>Organisation State</th>
                                <th>Organisation Type</th>
                                <th>Organisation Gender</th>
                                <th>Registration Date</th>
                                <th>Subscription Ending On</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @include ('staff.modals.add')
        @include ('staff.modals.edit')
        {{-- @include ('staff.modals.import') --}}
    @endpush
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                $.fn.dataTable.ext.errMode = 'none';
                var oTable = $('#teachers-table').DataTable({
                    "order": [
                        [0, "asc"]
                    ],
                    processing: true,
                    serverSide: true,
                    stateSave: true,
                    dom: "<'row'<'col-md-12'lB>>" + "<'table-responsive'rt><'row'<'col-md-12'pi>>",
                    "autoWidth": false,
                    "lengthMenu": [
                        [10, 25, 50, 100 /* , -1 */ ],
                        [10, 25, 50, 100 /* , "All" */ ]
                    ],
                    buttons: [{
                        extend: 'csv',
                        text: '<i class="fa fa-download"></i> CSV',
                        exportOptions: {
                            columns: 'th:not(:last-child)'
                        }
                    }],
                    ajax: {
                        url: 'staff/getdata',
                        data: function(d) {
                            d.name = $('input[name=name]').val();
                            d.position = $('select[name=position]').val();
                            d.organisation = $('select[name=organisation]').val();
                            d.state = $('select[name=state]').val();
                            d.type = $('select[name=type]').val();
                            d.gender = $('select[name=gender]').val();
                            d.active_inactive = $('select[name=active_inactive]').val();
                            return d;
                        }
                    },
                    columns: [{
                            data: 'firstname',
                            name: 'firstname',
                            // orderable: false,
                        },
                        {
                            data: 'lastname',
                            name: 'lastname',
                            // orderable: false,
                        },
                        {
                            data: 'email',
                        },
                        {
                            data: 'access_level',
                        },
                        {
                            // data: 'state.country.code',
                            data:'country_code'
                        },
                        {
                            data: 'state.code',
                        },
                        {
                            data: 'position',
                        },
                        {
                            data: 'organisation.name',
                            orderable: false,
                        },
                        {
                            data: 'organisation.state.code',
                            orderable: false,
                        },
                        {
                            data: 'organisation.detail.type',
                            orderable: false,
                        },
                        {
                            data: 'organisation.detail.gender',
                            orderable: false,
                        },
                        {
                            data: 'registration_date',
                        },
                        {
                            data: 'subscription_end_date',
                            orderable: false,
                        },
                        {
                            data: 'action',
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    "drawCallback": function(settings) {
                        laravel.initialize();
                    }
                });
                $('#search-form').on('submit', function(e) {
                    oTable.draw();
                    e.preventDefault();
                });
            });
        </script>
    @endpush
@endsection
