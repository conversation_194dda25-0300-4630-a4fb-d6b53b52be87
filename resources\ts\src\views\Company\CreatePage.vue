<template>
    <div class="main">
        <router-link :to="{ name: 'company', query: { tab: 'CompanyPages' } }" class="btn btn-link">
            <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_295_1445)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M13.125 8C13.125 8.11604 13.0789 8.22732 12.9969 8.30936C12.9148 8.39141 12.8035 8.4375 12.6875 8.4375L2.36862 8.4375L5.12225 11.1903C5.2044 11.2724 5.25055 11.3838 5.25055 11.5C5.25055 11.6162 5.2044 11.7276 5.12225 11.8098C5.0401 11.8919 4.92868 11.9381 4.8125 11.9381C4.69632 11.9381 4.5849 11.8919 4.50275 11.8098L1.00275 8.30975C0.962007 8.26911 0.929683 8.22083 0.907627 8.16768C0.885571 8.11453 0.874218 8.05755 0.874218 8C0.874218 7.94246 0.885571 7.88548 0.907627 7.83232C0.929683 7.77917 0.962007 7.73089 1.00275 7.69025L4.50275 4.19025C4.5849 4.1081 4.69632 4.06195 4.8125 4.06195C4.92868 4.06195 5.0401 4.1081 5.12225 4.19025C5.2044 4.2724 5.25055 4.38382 5.25055 4.5C5.25055 4.61618 5.2044 4.7276 5.12225 4.80975L2.36862 7.5625L12.6875 7.5625C12.8035 7.5625 12.9148 7.6086 12.9969 7.69064C13.0789 7.77269 13.125 7.88397 13.125 8Z" fill="black" />
                </g>

            </svg>
            Back to Company Pages
        </router-link>
        <div class="tophead mt-10 d-flex align-items-center justify-content-between">
            <h2>
                <span v-if="editId">Edit Page</span>
                <span v-else-if="parentId">Create Sub Page for "{{ parentTitle }}"</span>
                <span v-else>Create Page</span>
            </h2>
            <div style="display: flex; gap: 10px;">
                <button v-if="editId" type="button" class="btn btn-secondary text-dark py-4  " style="background-color: #F4F3F3; border:none;">
                    <i class="fa-solid fa-eye"></i>
                    View Preview
                </button>
                <button type="button" class="btn btn-secondary  px-18 py-4 " data-kt-menu-trigger="click" @click="savePage" data-kt-menu-placement="bottom-end">
                    Save
                </button>
            </div>
        </div>
        <div class="row mt-5">
            <div class="col-lg-9 ">
                <div class="card mb-5 border  ">
                    <div class="card-header ">
                        <p class=" card-title">Details</p>
                    </div>

                    <div class="card-body">



                        <div v-if="parentId" class="row d-flex flex-column">
                            <div class="col-md-6 mb-6">
                                <label class="form-label ">Parent Page</label>

                                <!-- Readonly field -->
                                <input type="text" class="form-control" :value="parentTitle" readonly>

                            </div>
                        </div>
                        <form>



                            <div class="row d-flex flex-column ">
                                <div class="col-md-6 mb-6">
                                    <label class="form-label required">Page Title</label>
                                    <input type="text" class="form-control" placeholder="Enter Page Title" v-model="createpage.title" @focus="clearTitleError" @input="clearTitleError">
                                    <span v-if="errors.title" class="text-danger">{{ errors.title }}</span>
                                </div>
                            </div>
                            <label class="form-label">Page Content</label>
                            <div id="app">
                                <froala :tag="'textarea'" v-model="createpage.content" :config="config"></froala>
                            </div>

                        </form>
                    </div>
                </div>


                <div class="card">
                    <div class="card-header ">
                        <div class="card-title">
                            Links
                        </div>
                    </div>
                    <div class="card-body bg-white">
                        <div v-for="(button, index) in createpage.buttons" :key="index" class="row g-3 mb-3">
                            <div class="col-md-5">
                                <label class="form-label">Button Copy</label>
                                <input type="text" class="form-control" v-model="button.name">
                            </div>
                            <div class="col-md-5">
                                <label class="form-label">Button URL</label>
                                <input type="text" class="form-control" v-model="button.url">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button v-if="index > 0" type="button" class="btn btn-danger" @click="removeButton(index)">Remove</button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary d-flex align-items-center gap-2 mt-5 py-5 py-4 px-16" @click="addButton">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 2C8.13261 2 8.25979 2.05268 8.35355 2.14645C8.44732 2.24021 8.5 2.36739 8.5 2.5V7.5H13.5C13.6326 7.5 13.7598 7.55268 13.8536 7.64645C13.9473 7.74021 14 7.86739 14 8C14 8.13261 13.9473 8.25979 13.8536 8.35355C13.7598 8.44732 13.6326 8.5 13.5 8.5H8.5V13.5C8.5 13.6326 8.44732 13.7598 8.35355 13.8536C8.25979 13.9473 8.13261 14 8 14C7.86739 14 7.74021 13.9473 7.64645 13.8536C7.55268 13.7598 7.5 13.6326 7.5 13.5V8.5H2.5C2.36739 8.5 2.24021 8.44732 2.14645 8.35355C2.05268 8.25979 2 8.13261 2 8C2 7.86739 2.05268 7.74021 2.14645 7.64645C2.24021 7.55268 2.36739 7.5 2.5 7.5H7.5V2.5C7.5 2.36739 7.55268 2.24021 7.64645 2.14645C7.74021 2.05268 7.86739 2 8 2Z" fill="#606060" />
                                </svg>
                                Add Another Button
                            </button>
                        </div>
                    </div>

                </div>
            </div>


            <div class="col-lg-3 ">
                <div class="card border  ">
                    <div>
                        <div class="card card-flush ">
                            <div class="card-header">
                                <p class="card-title required">Status</p>
                                <div class="card-toolbar">
                                    <div class="rounded-circle  w-15px h-15px" :class="createpage.status === 'published' ? 'bg-success' : 'bg-warning'" id="kt_ecommerce_add_category_status"></div>
                                    <span v-if="errors.status" class="text-danger">{{ errors.status }}</span>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <Multiselect
                                    v-model="createpage.status"
                                    :options="[
                                        { value: 'draft', label: 'Draft' },
                                        { value: 'published', label: 'Published' },
                                    ]"
                                    :can-clear="false"
                                    :required="true"
                                    mode="single"
                                    placeholder="Select status"
                                    class="multiselect-lg"
                                    >
                                </Multiselect>
                                <!-- <select class="form-select mb-2" v-model="createpage.status" data-control="select2"
                                    data-hide-search="true" data-placeholder="Select an option"
                                    id="kt_ecommerce_add_category_status_select">
                                    <option value="published">Published</option>
                                    <option value="draft">Draft</option>
                                </select> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent, ref, onMounted } from "vue";
    import { VueFroala } from "froala-wysiwyg-vue3";
    import { useRoute, useRouter } from "vue-router";
    import axios from 'axios';
    import Multiselect from '@vueform/multiselect'

    export default defineComponent({
        name: "CreatePage",
        components: {
            froala: VueFroala,
            Multiselect
        },
        setup() {
            const route = useRoute();
            const router = useRouter();
            const parentId = ref<string | null>(route.query.parentId as string | null);
            const editId = ref<string | null>(route.query.editId as string | null);
            const parentTitle = ref<string>('');
            const isLoading = ref(false);

            const createpage = ref({
                id: null as number | null,
                title: '',
                content: '',
                status: 'published' as 'published' | 'draft',
                buttons: [{ name: '', url: '' }],
                parent_id: parentId.value ? Number(parentId.value) : null,
            });

            const errors = ref<Record<string, string>>({
                title: '',
                status: '',
                content: '',
            });

            const fetchParentTitle = async () => {
                if (!parentId.value) return;
                try {
                    const response = await axios.get(`/api/company-pages/${parentId.value}`);
                    if (response.data.success && response.data.data) {
                        parentTitle.value = response.data.data.title;
                    } else {
                        parentTitle.value = 'Unknown Parent';
                        console.error('Parent page not found');
                    }
                } catch (error) {
                    parentTitle.value = 'Error fetching parent';
                    console.error('Error fetching parent title:', error);
                }
            };

            const fetchPageData = async () => {
                if (!editId.value) return;
                isLoading.value = true;
                try {
                    const response = await axios.get(`/api/company-pages/${editId.value}`);
                    if (response.data.success && response.data.data) {
                        const page = response.data.data;
                        createpage.value = {
                            id: page.id,
                            title: page.title,
                            content: page.content || '',
                            status: page.status.toLowerCase() as 'published' | 'draft',
                            buttons: page.buttons && page.buttons.length > 0 ? page.buttons : [{ name: '', url: '' }],
                            parent_id: page.parent_id || null,
                        };
                        if (page.parent_id) {
                            parentId.value = page.parent_id.toString();
                            await fetchParentTitle();
                        }
                    } else {
                        console.error('Page not found');
                    }
                } catch (error) {
                    console.error('Error fetching page data:', error);
                } finally {
                    isLoading.value = false;
                }
            };

            const addButton = () => {
                createpage.value.buttons.push({ name: "", url: "" });
            };

            const removeButton = (index: number) => {
                if (createpage.value.buttons.length > 1) {
                    createpage.value.buttons.splice(index, 1);
                    // Clear errors for removed button
                    delete errors.value[`buttons.${index}.name`];
                    delete errors.value[`buttons.${index}.url`];
                }
            };

            const clearTitleError = () => {
                errors.value.title = '';
            };

            const clearStatusError = () => {
                errors.value.status = '';
            };

            const clearButtonError = (index: number, field: 'name' | 'url') => {
                errors.value[`buttons.${index}.${field}`] = '';
            };

            const savePage = async () => {
                // Reset all errors
                errors.value = { title: '', status: '', content: '' };
                createpage.value.buttons.forEach((_, index) => {
                    delete errors.value[`buttons.${index}.name`];
                    delete errors.value[`buttons.${index}.url`];
                });

                try {
                    const payload = {
                        title: createpage.value.title,
                        content: createpage.value.content,
                        status: createpage.value.status,
                        buttons: createpage.value.buttons,
                        parent_id: createpage.value.parent_id,
                    };

                    let response;
                    if (editId.value && createpage.value.id) {
                        response = await axios.put(`/api/company-pages/${createpage.value.id}`, payload, {
                            headers: { 'Content-Type': 'application/json' },
                        });
                        console.log('Page updated successfully:', response.data);
                    } else {
                        response = await axios.post(`/api/company-pages`, payload, {
                            headers: { 'Content-Type': 'application/json' },
                        });
                        console.log('Page created successfully:', response.data);
                    }

                    router.push({ name: 'company', query: { tab: 'CompanyPages' } });
                    return response.data;
                } catch (error: any) {
                    if (error.response && error.response.status === 422) {
                        const serverErrors = error.response.data.errors;
                        Object.keys(serverErrors).forEach(key => {
                            errors.value[key] = serverErrors[key][0];
                        });
                        console.error('Validation errors:', serverErrors);
                    } else {
                        console.error('Error saving page:', error.response?.data || error.message);
                    }
                    throw error;
                }
            };

            onMounted(() => {
                if (editId.value) {
                    fetchPageData();
                } else if (parentId.value) {
                    fetchParentTitle();
                }
            });

            return {
                createpage,
                addButton,
                removeButton,
                errors,
                savePage,
                parentTitle,
                parentId,
                editId,
                isLoading,
                clearTitleError,
                clearStatusError,
                clearButtonError,
                config: {
                    key: "hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==",
                    height: 300,
                    attribution: false,
                },
            };
        },
    });
</script>

<style scoped>
    .form-label {
        color: #000000 !important;
    }

    .form-control {
        color: #000000 !important;
    }
</style>