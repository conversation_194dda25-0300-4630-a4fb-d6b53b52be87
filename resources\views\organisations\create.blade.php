@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('create-organisation'))
@section('content')
@if(session()->has('message'))
<div class="alert alert-success text-center">
    <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
</div>
@endif
@push('stylesheets')
<link media="screen" type="text/css" rel="stylesheet"
    href="{{asset('assets/plugins/switchery/css/switchery.min.css')}}">
@endpush
@push('styles')
<style>
    .custom-input:focus {
        box-shadow: none;
    }

    .bg-grey-lightest {
        background-color: #f1f1f1;
        padding: 15px 20px;
    }

    .plan-table th {
        font-weight: bold;
    }

    .plan-table th,
    .plan-table td {
        padding: 0 10px;
    }

    .plan-table tr>th:nth-child(4),
    .plan-table tr>th:nth-child(5),
    .plan-table tr>td:nth-child(4),
    .plan-table tr>td:nth-child(5) {
        text-align: center;
    }

    .custom-black-radio {
        margin: 0.5rem;
    }

    .custom-black-radio input[type="checkbox"] {
        position: absolute;
        opacity: 0;
    }

    .custom-black-radio input[type="checkbox"]+label:before {
        content: '';
        background: #f4f4f4;
        border-radius: 100%;
        border: 2px solid #000;
        display: inline-block;
        width: 18px;
        height: 18px;
        position: relative;
        top: -0.2em;
        margin-right: 1em;
        vertical-align: top;
        cursor: pointer;
        text-align: center;
        transition: all 250ms ease;
        font-size: 0px;
    }

    .custom-black-radio input[type="checkbox"]:checked+label:before {
        background-color: #000;
        box-shadow: inset 0 0 0 2px #f4f4f4;
    }

    .custom-black-radio input[type="checkbox"]:focus+label:before {
        outline: none;
        border-color: #000;
    }

    .custom-black-radio input[type="checkbox"]:disabled+label:before {
        box-shadow: inset 0 0 0 2px #f4f4f4;
        border-color: #b4b4b4;
        background: #b4b4b4;
    }

    .custom-black-radio input[type="checkbox"]+label:empty:before {
        margin-right: 0;
    }

    /* Select2 coustomization */

    .custom-select2+.select2-container {
        width: 100% !important;
    }

    .custom-select2+.select2-container.select2-container--focus .select2-selection,
    .custom-select2+.select2-container .select2-selection {
        border: none !important;
        cursor: pointer;
        padding: 0
    }

    .custom-select2+.select2-container .select2-selection::after {
        position: absolute;
        left: 50%;
        top: 50%;
        content: "\f107";
        font-family: FontAwesome;
        font-size: 24px;
        line-height: 20px;
        transform: translate(-50%, -50%);
    }

    .hide-caret::after {
        display: none;
    }

    .select2-container.select2-container--open .select2-dropdown {
        border: 1px solid #aaa;
        /* width: auto !important; */
        min-width: 100px;
        border-radius: 0;
    }

    .select2-results__option {
        color: #000;
    }

    .select2-container--default .select2-results__option[aria-disabled=true] {
        color: #c0c0c0;
    }

    .select2-container--default .select2-results__option[aria-selected=true]:not(.select2-results__option--highlighted) {
        background-color: #fff;
    }

    .select2-container--default .select2-results__option[aria-selected=true]::after {
        content: '\f00c';
        font-family: FontAwesome;
        position: absolute;
        right: 10px;
        color: #070af8;
    }

    .custom-select2+.select2-container ul.select2-selection__rendered {
        white-space: normal;
        position: relative;
        width: auto;
        padding-left: 0;
    }

    .hide-caret>ul.select2-selection__rendered {
        right: 0 !important;
    }

    .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice {
        border: none;
        background-color: transparent !important;
        padding: 0;
        margin: 4px 0 0 0;
        cursor: pointer;
    }

    .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-search {
        display: none;
    }

    .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice+li.select2-selection__choice::before {
        content: ',';
        margin-right: 1px;
    }

    .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice>span.select2-selection__choice__remove {
        display: none;
    }
</style>
@endpush

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header separator">
                <div class="card-title">Create organisation</div>
            </div>
            <div class="card-block">
                <form id="order-add" method="POST" role="form" autocomplete="off" enctype="multipart/form-data" action="{{route('organisations.store')}}">
                    {{ csrf_field() }}
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Organisation</label>
                                <input type="text" class="form-control" name="school_name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default  form-group-default-select2 ">
                                <label> Organisation Type </label>
                                <select class="full-width" name="type" id="type" data-placeholder="Select.." data-init-plugin="select2">
                                    <option value=""></option>
                                    <option value="Independent">Independent</option>
                                    <option value="Catholic">Catholic</option>
                                    <option value="Government">Government</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-12">
                            <div class="form-group form-group-default">
                                <label>Logo</label>
                                <input type="file" class="form-control" name="logo">
                            </div>
                        </div>
                    </div>
                    <div class="card" id="campuses">
                        <div class="card-header card-separator">
                            <div class="card-title">Campuses</div>
                        </div>
                        <div class="card-block">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default">
                                        <label>#<span class="number">1</span></label>
                                        <input type="text" class="form-control" name="campuses[]" value="">
                                    </div>
                                </div>
                            </div>
                            <div class="m-b-10 clearfix text-right">
                                <button class="btn btn-primary btn-custom-sm" type="button" id="btnAddCampus"> <i class="fa fa-plus" aria-hidden="true"></i> Add Another Campus </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group form-group-default form-group-default-select2 ">
                                <label> Organisation Gender </label>
                                <select class="full-width" id="gender" name="gender" data-placeholder="Select.."
                                    data-init-plugin="select2">
                                    <option value=""></option>
                                    <option value="Coed"> Coed </option>
                                    <option value="Boys"> Boys </option>
                                    <option value="Girls"> Girls </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Password</label>
                                <input type="text" class="form-control" id="password" name="password">
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default form-group-default-select2 required">
                                <label>Country</label>
                                <select class="full-width" name="country" data-init-plugin="select2" data-placeholder="Select..">
                                    <option value="" selected></option>
                                    @foreach ($countries as $country)
                                    <option value="{{$country->id}}">{{$country->name}}</option>
                                    @endforeach
                                </select>
                                <div id="country-required-alert" class="text-danger mt-1"
                                    style="font-size: 12px; margin-left:12px;">Please select a country.</div>
                            </div>
                        </div>
                        <div class="col-md-6" id="org-state-container" style="display: none;">
                            <div class="form-group form-group-default form-group-default-select2 required">
                                <label>State</label>
                                <select class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                     <option value=""></option>
                                        {{-- @foreach ($states as $state)
                                            <option value="{{ $state->id }}">{{ $state->name }}</option>
                                        @endforeach --}}
                                </select>
                                <div id="state-required-alert" class="text-danger mt-1"
                                    style="font-size: 12px; margin-left:12px;">Please select a state.</div>
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default">
                                <label> Suburb </label>
                                <input type="text" class="form-control" id="suburb" name="suburb"  />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Postcode</label>
                                <input type="text" class="form-control" name="postcode" >
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Organisation contact person</label>
                                <input type="text" class="form-control" name="contactperson">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Position</label>
                                <input type="text" class="form-control" name="position" >
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Email</label>
                                <input type="email" class="form-control" name="email" >
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required">
                                <label>Phone</label>
                                <input type="text" class="form-control" name="phone" >
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default input-group date required" id="myDatepicker">
                                <div class="form-input-group">
                                    <label class="fade">Subscription End Date</label>
                                    <input type="text" class="form-control" placeholder="Pick a date" name="subscription_enddate">
                                </div>
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default">
                                <label>Account Limit</label>
                                <input type="number" class="form-control" name="account_limit" />
                            </div>
                        </div>
                        {{-- <div class="col-md-6 d-none"></div> --}}
                    </div>
                    {{-- <div class="row mt-2">
                        <div class="col-12">
                            <label for="" class="p-r-10">Livechat </label>
                            <input type="hidden" class="" id="" name="livechat" value="0" />
                            <input type="checkbox" class="switchery" id="livechat" name="livechat" value="1" />
                        </div>
                    </div> --}}
                    <div class="row clearfix my-5">
                        <div class="col-md-6">
                            <div class="bg-master-lightest padding-15">
                                <div class="oswald-heading">STANDARD</div>
                                <p>&nbsp;</p>
                                <table class="full-width plan-table">
                                    <thead>
                                        <tr>
                                            <th>No. of<br />students</th>
                                            <th>Value</th>
                                            <th>Cost</th>
                                            <th>Years</th>
                                            <th>Tick box</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($basicPlans as $plan)
                                        <tr>
                                            <td>{{$plan->name}}</td>
                                            <td>
                                                @if ($plan->discount) <span class="line-through">${{$plan->value}}</span>
                                                 <span class="text-danger uppercase">{{$plan->discount}}% off</span>
                                                @else
                                                {{ $plan->value ? '$'.$plan->value : '' }}
                                                @endif
                                            </td>
                                            <td>{{ $plan->cost ? '$'.$plan->cost : 'POA' }}</td>
                                            <td>
                                                <select class="basic-years custom-select2" data-value="plan{{$plan->id}}" multiple>
                                                    @foreach($years as $year)
                                                    <option value="{{ $year->id }}" > {{ $year->title }} </option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                <div class="custom-black-radio">
                                                    <input type="checkbox" name="basic_plan" value="{{$plan->id}}" id="plan{{$plan->id}}">
                                                    <label for="plan{{$plan->id}}"></label>
                                                    <input type="hidden" class="cost" value="{{$plan->cost}}">
                                                </div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="bg-master-lightest padding-15">
                                <div class="oswald-heading">PREMIUM</div>
                                <p>Includes Live Chat and Virtual Work Experience</p>
                                <table class="full-width plan-table">
                                    <thead>
                                        <tr>
                                            <th>No. of<br />students</th>
                                            <th>Value</th>
                                            <th>Cost</th>
                                            <th>Years</th>
                                            <th>Tick box</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($proPlans as $plan)
                                        <tr>
                                            <td>{{$plan->name}}</td>
                                            @if ($plan->max_students)
                                            <td>
                                                @if ($plan->discount)
                                                    <span class="line-through">${{$plan->value}}</span>
                                                    <span class="text-danger uppercase">{{$plan->discount}}% discount</span>
                                                @else
                                                    {{ $plan->value ? '$'.$plan->value : '' }}
                                                @endif
                                            </td>
                                            <td>{{ $plan->cost ? '$'.$plan->cost : 'POA' }}</td>
                                            @else
                                            <td colspan="2">@if ($plan->discount)<span class="text-danger fs-14">{{$plan->discount}}% DISCOUNT on each student over 800</span>@endif</td>
                                            @endif
                                            <td>
                                                <select class="pro-years custom-select2" name="" data-value="plan{{$plan->id}}" multiple>
                                                    @foreach($years as $year)
                                                    <option value="{{ $year->id }}" > {{ $year->title }} </option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                <div class="custom-black-radio">
                                                    <input type="checkbox" name="pro_plan" value="{{$plan->id}}" id="plan{{$plan->id}}" >
                                                    <label for="plan{{$plan->id}}"></label>
                                                    <input type="hidden" class="cost" value="{{$plan->cost}}">
                                                </div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <p class="text-center"><b>Total subscription cost will be:</b> <span id="totalCost"></span></p>

                    <div class="checkbox check-primary text-center mb-5">
                        <input type="hidden" name="confirm" value="0" id="checkbox2">
                        <input type="checkbox" name="confirm" value="1" id="confirm">
                        <label for="confirm">Confirm</label>
                    </div>
                    <div class="text-center">
                        <button class="btn btn-primary">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scriptslib')
<script src="{{asset('vendor/jsvalidation/js/additional-methods.min.js')}}"></script>
<script src="https://cdn.ckeditor.com/4.14.1/standard/ckeditor.js"></script>
<script type="text/javascript" src="{{asset('assets/plugins/switchery/js/switchery.min.js')}}"></script>
@endpush
@push('scripts')
<script>
    function resetNumbering() {
        jQuery('.number').each(function (i) {
            i++;
            jQuery(this).text(i);
        });
    }

    jQuery('#country-required-alert').hide();
    jQuery('#state-required-alert').hide();

    jQuery(document).ready(function () {
        jQuery("select[name=country]").change(function() {
            jQuery.ajax({
                type: "get",
                url: "{{route('getStates')}}",
                data: {
                    id: jQuery(this).val(),
                },
                success: function (response) {
                        // jQuery("#order-add select[name=state]").html('<option value=""></option>');
                        jQuery("select[name=state]").html('<option selected disabled></option>');
                        // New logic for states - START
                            if (!response || response.length == 0) {
                                jQuery("#org-state-container").hide();
                                return;
                            }
                            jQuery("#org-state-container").show();
                            jQuery.each(response, function (i, v) {
                                jQuery("#order-add select[name=state]").append('<option value="'+v.id+'">'+v.name+'</option>');
                            });
                        jQuery("#order-add select[name=state]").trigger("change.select2");
                        // Newlogic for states - END

                    },
                    fail: function() {
                        jQuery("#order-add select[name=state]").html('<option value="" disabled></option>');
                        jQuery("#org-state-container").hide();
                    }
            });
        });
        resetNumbering();
        var max = 2
        jQuery(document).on('click', '#btnAddCampus', function () {
            var row = jQuery("#campuses .card-block>.row:last");
            if (row.find(".col-md-6").length >= max) {
                jQuery(row).after('<div class="row">');
            }
            jQuery('#campuses .card-block>.row:last').append('<div class="col-md-6"> <div class="form-group form-group-default"> <label>#<span class="number"></span> <i role="button" class="fa fa-times text-danger float-right remove-campus"></i></label> <input type="text" class="form-control" name="campuses[]" value=""> </div> </div>');
            resetNumbering();
        });

        jQuery(document).on('click', '.remove-campus', function () {
            var colLen = jQuery(this).closest(".row").find(".col-md-6").length;
            // if (jQuery('.remove-campus').length > 1) {
                if (colLen < 2) {
                    jQuery(this).closest('.row').remove();
                } else {
                    jQuery(this).closest('.col-md-6').remove();
                }
            // } else {
            //     jQuery(this).closest('.col-md-6').find('input').val('');
            // }

            resetNumbering();
        })


        CKEDITOR.config.allowedContent = true;
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        // Success color: #10CFBD
        elems.forEach(function (html, ) {
            var switchery = new Switchery(html, {
                color: '#000',
                size: 'small',
            });
        });
        var today = new Date();
        jQuery('#myDatepicker').datepicker({
            startDate: today,
            format: 'yyyy-mm-dd'
        });

        jQuery('.custom-select2').select2({
            closeOnSelect: false
        });


        if (jQuery('[name=basic_plan]:checked').length) {
            val = jQuery('[name=basic_plan]:checked').val();
            jQuery("[data-value=plan" + val + "]").attr("name", "basic_years[]");
        };

        disableSelectedOption('.basic-years', '.pro-years')

        if (jQuery('[name=pro_plan]:checked').length) {
            val = jQuery('[name=pro_plan]:checked').val();
            jQuery("[data-value=plan" + val + "]").attr("name", "pro_years[]");
        }

        disableSelectedOption('.pro-years', '.basic-years');

        $amountCal = amountCal();
        $totalCost = 'POA';

        if ($amountCal != 0) {
            $totalCost = '$' + $amountCal + ' + GST'
        }
        jQuery('#totalCost').text($totalCost);

        // jQuery('#totalCost').text('$' + amountCal() + ' + GST');

        function amountCal() {
            var basic = 0;
            var pro = 0;
            if (jQuery('input[name=basic_plan]:checked').val()) {
                basic = jQuery('input[name=basic_plan]:checked').siblings('.cost').val();
            }
            if (jQuery('input[name=pro_plan]:checked').val()) {
                pro = jQuery('input[name=pro_plan]:checked').siblings('.cost').val()
            }
            if (parseInt(basic) != 0 && parseInt(pro) != 0) {
                return parseInt(basic) + parseInt(pro);
            }
            return 0
        }

        function disableSelectedOption(current, other) {
            selected = [];
            jQuery(current).each(function () {
                val = jQuery(this).val();
                jQuery.each(val, function (i, v) {
                    selected.push(v);
                });
            })

            jQuery(other + '>option').prop('disabled', false);
            jQuery.each(selected, function (i, v) {
                jQuery(other + '>option[value=' + v + ']').prop('disabled', true);
            });
            jQuery(other).select2({
                closeOnSelect: false
            });

            jQuery('.basic-years, .pro-years').each(function () {
                $this = jQuery(this);
                if ($this.val().length != 0) {
                    $this.next('.select2-container').find('.select2-selection').addClass('hide-caret');
                } else {
                    $this.next('.select2-container').find('.select2-selection').removeClass('hide-caret');
                }
            })
        }

        function onPlanSelect(selected, $this) {
            var val = $this.val();
            var checked = $this.is(':checked');
            jQuery('.' + selected + '-years').next('.select2-container').find('.select2-selection').removeClass('hide-caret')
            jQuery("input[name=" + selected + "_plan]").prop('checked', false);
            jQuery("[name='" + selected + "_years[]']").removeAttr('name');
            jQuery("." + selected + "-years:not([data-value=plan" + val + "])").val('').trigger('change.select2');
            if (checked) {
                jQuery("[data-value=plan" + val + "]").attr("name", selected + "_years[]");
                $this.prop('checked', true);
            } else {
                jQuery("." + selected + "-years").val('').trigger('change.select2');
            }
            $amountCal = amountCal();
            $totalCost = 'POA';

            if ($amountCal != 0) {
                $totalCost = '$' + $amountCal + ' + GST'
            }
            jQuery('#totalCost').text($totalCost);

            // jQuery('#totalCost').text('$' + amountCal() + ' + GST');
        }

        jQuery(document).on('change', '.basic-years', function () {
            $this = jQuery(this);

            if ($this.val().length != 0) {
                $this.parent().next('td').find('input[name=basic_plan]').prop('checked', true);
            } else {
                $this.parent().next('td').find('input[name=basic_plan]').prop('checked', false);
            }

            onPlanSelect('basic', $this.parent().next('td').find('input[name=basic_plan]'));
            disableSelectedOption('.basic-years', '.pro-years');
        });

        jQuery(document).on('change', '.pro-years', function () {
            $this = jQuery(this);

            if ($this.val().length != 0) {
                $this.parent().next('td').find('input[name=pro_plan]').prop('checked', true);
            } else {
                $this.parent().next('td').find('input[name=pro_plan]').prop('checked', false);
            }
            onPlanSelect('pro', $this.parent().next('td').find('input[name=pro_plan]'));
            disableSelectedOption('.pro-years', '.basic-years');
        });


        jQuery("input[name=basic_plan]").change(function () {
            onPlanSelect('basic', jQuery(this));
            disableSelectedOption('.basic-years', '.pro-years');
        });

        jQuery("input[name=pro_plan]").change(function () {
            onPlanSelect('pro', jQuery(this));
            disableSelectedOption('.pro-years', '.basic-years');
        });

        jQuery("#order-add").validate({
            errorPlacement: function (error, element) {
                if (element.parent('.input-group').length || element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                    if (element.attr('name') == 'basic_plan' || element.attr('name') == 'pro_plan') {
                        error.insertBefore(element.closest('table'));
                    } else {
                        error.insertAfter(element.parent());
                    }
                } else if (element.hasClass('select2-hidden-accessible')) {
                    // error.insertAfter(element.next('span'));
                    element.closest('table').find('thead th:nth-child(4)').append(error);

                } else {
                    // else just place the validation message immediatly after the input
                    error.insertAfter(element);
                }
            },
            // invalidHandler: function(form, validator) {

            //     if (!validator.numberOfInvalids())
            //         return;

            //     $('html, body').animate({
            //         scrollTop: $(validator.errorList[0].element).offset().top - 100
            //     }, 800);

            // },
            // highlight: function (element) {
            //     jQuery(element).parent('.custom-form-group').addClass('has-error'); // add the Bootstrap error class to the control group
            // },
            // unhighlight: function (element) {
            //     jQuery(element).parent('.custom-form-group').removeClass('has-error'); // add the Bootstrap error class to the control group
            // },
            ignore: [],
            rules: {
                school_name: {
                    required: true,
                    remote: {
                        url: "/checkOrganisationName",
                        type: "get",
                    }
                },
                country: 'required',
                state: {
                  // new logic for states - START
                    required: function() {
                        // Count the number of state options (excluding the empty option)
                         var stateOptions = jQuery('select[name=state] option').filter(function() {
                                    return jQuery(this).val() !== '';
                                });
                         var isRequired = stateOptions.length > 0;
                                // Show/hide the alert message under the state field
                            if (isRequired) {
                                    jQuery('#state-required-alert').show();
                            } else {
                                    jQuery('#state-required-alert').hide();
                            }
                            return isRequired;
                        }
                    // new logic for states - END      
                 },
                postcode: 'required',
                contactperson: 'required',
                position: 'required',
                email: {
                    required: true,
                    email: true,
                },
                phone: 'required',
                password: {
                    required: function () {
                        return (jQuery('input[name=confirm]').is(":checked"));
                    }
                },
                // confirm_password: {
                //     equalTo: "#password",
                // },
                basic_plan: {
                    required: function () {
                        return !(jQuery('input[name=pro_plan]').is(":checked"));
                    }
                },
                pro_plan: {
                    required: function () {
                        return !(jQuery('input[name=basic_plan]').is(":checked"));
                    }
                },
                'basic_years[]': 'required',
                'pro_years[]': 'required',
            },
            messages: {
                school_name: {
                    remote: 'This school is already registered!'
                },
                basic_plan: {
                    required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                },
                pro_plan: {
                    required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                },
                'basic_years[]': {
                    required: 'Please select the years',
                },
                'pro_years[]': {
                    required: 'Please select the years',
                }
            },
            invalidHandler: function(event, validator) {
                        // Show/hide country alert
                        if (validator.errorMap && validator.errorMap.country) {
                            jQuery('#country-required-alert').show();
                        } else {
                            jQuery('#country-required-alert').hide();
                        }
                        // Show/hide state alert (for safety)
                        if (validator.errorMap && validator.errorMap.state) {
                            jQuery('#state-required-alert').show();
                        } else {
                            jQuery('#state-required-alert').hide();
                        }
                    },
        });
    });
</script>

@endpush
@endsection