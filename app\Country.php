<?php

namespace App;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

use Illuminate\Database\Eloquent\Model;

class Country extends Model {
	// use Cachable;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'updated_at',
        'created_at',
    ];

	public function states() {
		return $this->hasMany(State::class);
	}

    public function users()
    {
        return $this->hasMany(User::class);
    }
}
