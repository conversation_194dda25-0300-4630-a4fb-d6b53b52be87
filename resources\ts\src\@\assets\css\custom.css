/* [custom scrollbar css starts] */

.custom-scrollbar,
.fancybox-inner {
    scrollbar-width: thin;
    scrollbar-color: #999 #595959;
}

/* width */
.custom-scrollbar::-webkit-scrollbar {
    width: 10px;
}

/* Track */
.custom-scrollbar::-webkit-scrollbar-track {
    background: #595959;
}

/* Handle */
.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #999;
    border-radius: 100px;
    border: none;
}

/* Handle on hover */
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}

/* [custom scrollbar css ends] */

/* [hidden scrollbar css starts] */

/* Hide scrollbar for Chrome, Safari and Opera */
.hidden-scrollbar::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    background: transparent;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hidden-scrollbar {
    -ms-overflow-style: none !important;
    /* IE and Edge */
    scrollbar-width: none !important;
    /* Firefox */
    overflow: -moz-scrollbars-none;
}

/* [hidden scrollbar css ends] */

.iframe-container {
    position: relative;
    width: calc(100% + 60px);
    min-height: 100vh;
    overflow: hidden;
    margin: 0 -30px;
}

.iframe-container>iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    overflow: auto;
}

.modal {
    z-index: 9999 !important;
    /* to come over sidebar */
}

.overflow-x-hidden {
    overflow-x: hidden !important;
}

.overflow-y-hidden {
    overflow-y: hidden !important;
}

.rotate-45 {
    transform: rotate(45deg);
}

i.bi,
i[class^=fonticon-],
i[class*=" fonticon-"],
i[class^=fa-],
i[class*=" fa-"],
i[class^=la-],
i[class*=" la-"] {
    color: var(--kt-text-gray-700);
    /* override default color --kt-text-muted */
}

.multiselect {
    border: 1px solid var(--kt-input-border-color);
    background-color: var(--kt-input-bg);
    box-shadow: none !important;
}

.multiselect.is-active, .multiselect-dropdown {
    box-shadow: none !important;
    color: var(--kt-input-focus-color);
    background-color: var(--kt-input-focus-bg);
    border-color: var(--kt-input-focus-border-color);
}

.multiselect-dropdown {
    border-top-color: var(--kt-input-border-color);
}

.multiselect-tags {
    margin: 0;
}

.multiselect-tag {
    background: var(--kt-gray-200);
    color: var(--kt-text-dark);
    padding: 0.2rem 0 0.2rem 0.5rem;
    letter-spacing: 0.02rem;
}

.multiselect-option.is-pointed {
    background: var(--kt-gray-100);
    color: var(--kt-text-dark);
}

.multiselect-option.is-selected.is-pointed,
.multiselect-option.is-selected {
    background: var(--kt-gray-300);
    color: var(--kt-text-dark);
}

@media (max-width: 991px) {
    .iframe-container {
        width: calc(100% + 50px);
    }
}