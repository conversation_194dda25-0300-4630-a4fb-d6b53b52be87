<div class="modal fade fill-in" id="modalStudentEdit" role="dialog" aria-hidden="true">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        <i class="pg-close">
        </i>
    </button>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-left p-b-5">
                    <span class="semi-bold">
                        Edit Student
                    </span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class=" col-lg-12 ">
                        <!-- START card -->
                        <div class="card card-transparent">
                            <div class="card-block">
                                <form method="POST" action="" id="form-studentedit" role="form" autocomplete="off">
                                    {{ csrf_field() }}
                                    <input type="hidden" name="_method" value="PUT">
                                    <input type="hidden" name="id" id="id">
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>First Name</label>
                                                <input type="text" id="firstname" class="form-control" name="firstname" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Last Name</label>
                                                <input type="text" id="lastname" class="form-control" name="lastname" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Email</label>
                                                <input type="email" id="email" class="form-control" name="email" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Password</label>
                                                <input type="password" id="password" class="form-control" name="password" placeholder="Enter only if you want to change" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Gender</label>
                                                <select class="full-width" id="gender" name="gender" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    <option value="M">Male</option>
                                                    <option value="F">Female</option>
                                                    <option value="O">Other / Prefer not to say</option>
                                                </select>
                                            </div>
                                        </div>
                                         {{-- <div class="col-md-6" id="edit_other_gender">
                                            <div class="form-group form-group-default required">
                                                <label>Other Gender</label>
                                                <input type="text" class="form-control other_gender" name="other_gender" />
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Country</label>
                                                <select class="full-width" id="country" name="country" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    @foreach ($countries as $country)
                                                        <option value="{{ $country->id }}"> {{ $country->name }} </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="edit-state-container" style="display: none;">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>State</label>
                                                <select id="state_id" class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Postcode</label>
                                                <input id="postcode" type="number" placeholder="" class="form-control" name="postcode" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        @if (Auth::user()->role->name == 'Admin')
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>School</label>
                                                    <select class="full-width" id="school_id" name="school" {{-- data-placeholder="Select.." --}}>
                                                        <option value="">Select</option>
                                                        @foreach ($schools as $key => $school)
                                                            <option value="{{ $school->id }}">{{ $school->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        @endif
                                        @if (Auth::user()->role->name != 'Staff')
                                            <div class="col-md-6" id="campus-edit" style="display:none;">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Campus</label>
                                                    <select class="full-width" id="campus_id" name="campus" data-init-plugin="select2" data-placeholder="Select..">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="row">
                                        @if (Auth::user()->role->name == 'Admin')
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Organisation</label>
                                                    <select class="full-width" id="organisation_id" name="organisation" data-init-plugin="select2">
                                                        <option value="">Select</option>
                                                        @foreach ($organisations as $key => $organisation)
                                                            <option value="{{ $organisation->id }}">{{ $organisation->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        @endif
                                        @if (Auth::user()->role->name != 'Teacher')
                                            <div class="col-md-6" id="orgCampus-edit" style="display:none;">
                                                <div class="col-md-12">
                                                    <div class="form-group form-group-default form-group-default-select2">
                                                        <label>Organisation Campus</label>
                                                        <select class="full-width" id="orgCampus_id" name="orgCampus" data-init-plugin="select2" data-placeholder="Select..">
                                                            <option value=""></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Year</label>
                                                <select class="full-width" id="year" name="year" data-placeholder="Select.." data-init-plugin="select2">
                                                    <option value=""></option>
                                                    @foreach ($years as $key => $year)
                                                        <option value="{{ $year->id }}">{{ $year->title }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="graduated_year">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Graduated Year</label>
                                                <select class="full-width" name="graduate_year" data-init-plugin="select2" data-placeholder="Select.." id="graduate_year">
                                                    <option value="" selected></option>
                                                    @for ($i = 2018; $i <= date('Y'); $i++)
                                                        <option>{{ $i }}</option>
                                                    @endfor
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    @if (Auth::user()->isAdmin())
                                        <div class="col-12" id="active_inactive">
                                            <div class="radio radio-primary">
                                                <input type="radio" value="0" name="removed" id="activeEdit">
                                                <label for="activeEdit">Active</label>
                                                <input type="radio" value="1" name="removed" id="inactiveEdit">
                                                <label for="inactiveEdit">Inactive</label>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="clearfix"></div>
                                    <button class="btn btn-primary pull-right" type="submit">
                                        Update
                                    </button>
                                </form>
                            </div>
                        </div>
                        <!-- END card -->
                    </div>
                </div>
                <!-- END card -->
            </div>
        </div>
    </div>
    <div class="modal-footer"></div>
</div>
<!-- /.modal-content -->
</div>
<!-- /.modal-dialog -->
</div>
@push('scripts')
    <script>
        jQuery(document).ready(function() {
            $(`#school_id`).select2({
                dropdownParent: $('#modalStudentEdit'),
            });

            // Store the state_id for later use
            var selectedStateId = null;

            jQuery("#form-studentedit select[name=country]").change(function() {
                var countryId = jQuery(this).val();
                // if (!countryId) {
                //     jQuery("#form-studentedit select[name=state]").html('<option value=""></option>');
                //     return;
                // }

                // New logic for states - START
                    jQuery("#form-studentedit select[name=state]").html('<option value=""></option>');
                    if (!countryId) {
                        jQuery("#edit-state-container").hide();
                        return;
                    }
                // New logic for states - END

                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getStates') }}",
                    data: {
                        id: countryId,
                    },
                    success: function(response) {
                        jQuery("select[name=state]").html('<option selected disabled></option>');
                        // New logic for states - START
                        if (!response || response.length == 0) {
                            jQuery("#edit-state-container").hide();
                            return;
                        }
                        jQuery("#edit-state-container").show();
                        // New logic for states - END

                        jQuery("#form-studentedit select[name=state]").html('<option value=""></option>');
                        jQuery.each(response, function(i, v) {
                            var selected = v.id == selectedStateId ? 'selected' : '';
                            jQuery("#form-studentedit select[name=state]").append('<option value="' + v.id + '" ' + selected + '>' + v.name + '</option>');
                        });
                        jQuery("#form-studentedit select[name=state]").trigger('change.select2');

                        // Clear selectedStateId after use
                        selectedStateId = null;
                        },
                        fail: function() {
                             // New logic for states - START
                                jQuery("#edit-state-container").hide();
                             // New logic for states - END
                            jQuery("#form-studentedit select[name=state]").html('<option value=""></option>');
                        }
                });
            });

            // New logic for states - START
            jQuery("#form-studentedit select[name=country]").trigger("change");
            // New logic for states - END

            // Store the campus_id for later use
            var selectedCampusId = null;

            $('#form-studentedit select[name=school]').change(function() {
                jQuery('#form-studentedit select[name="campus[]"]').html('<option value=""></option>');
                var schoolId = jQuery(this).val();

                if (!schoolId) {
                    jQuery('#campus-edit').hide();
                    return;
                }

                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getSchoolCampuses') }}",
                    data: {
                        id: schoolId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (Object.keys(response).length) {
                            jQuery.each(response, function(id, name) {
                                jQuery('#form-studentedit select[name="campus"]').append('<option value="' + id + '">' + name + '</option>')
                            });
                            jQuery('#campus-edit').show();

                            // If we have a stored campus_id and it's a valid option, select it
                            if (selectedCampusId) {
                                jQuery('#form-studentedit select[name="campus"]').val(selectedCampusId).trigger('change.select2');
                                selectedCampusId = null; // Clear it after use
                            }
                        } else {
                            jQuery('#campus-edit').hide();
                        }
                    },
                });
            });

            // Store the orgCampus_id for later use
            var selectedOrgCampusId = null;

            $('#form-studentedit select[name=organisation]').change(function() {
                jQuery('#form-studentedit select[name="orgCampus[]"]').html('<option value=""></option>');
                var organisationId = jQuery(this).val();

                if (!organisationId) {
                    jQuery('#orgCampus-edit').hide();
                    return;
                }

                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getOrganisationCampuses') }}",
                    data: {
                        id: organisationId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (Object.keys(response).length) {
                            jQuery.each(response, function(id, name) {
                                jQuery('#form-studentedit select[name="orgCampus"]').append('<option value="' + id + '">' + name + '</option>')
                            });
                            jQuery('#orgCampus-edit').show();
                            

                            // If we have a stored orgCampus_id and it's a valid option, select it
                            if (selectedOrgCampusId) {
                                jQuery('#form-studentedit select[name="orgCampus"]').val(selectedOrgCampusId).trigger('change.select2');
                                selectedOrgCampusId = null; // Clear it after use
                            }
                        } else {
                            jQuery('#orgCampus-edit').hide();
                        }
                    },
                });
            });


            jQuery('#modalStudentEdit').on('show.bs.modal', function(e) {
                jQuery("#form-studentedit input:not([type=hidden]):not([type=submit]):not([type=radio]), #form-studentedit select:not([type=hidden]").val('');
                jQuery("#form-studentedit select[name=state]").html('<option value=""></option>');
                var $trigger = $(e.relatedTarget);
                jQuery("#form-studentedit").attr("action", "students/" + $trigger.data('id'));
                jQuery.ajax({
                    url: "students/" + $trigger.data('id'),
                    dataType: 'json',
                    success: function(data) {
                        // Store campus IDs and state_id before setting other fields
                        if (data.campus_id) {
                            selectedCampusId = data.campus_id;
                        }
                        if (data.orgCampus_id) {
                            selectedOrgCampusId = data.orgCampus_id;
                        }
                        if (data.state_id) {
                            selectedStateId = data.state_id;
                        }

                        jQuery.each(data, function(i, v) {
                            if (i == 'state_id') {
                                var timer;
                                timer = setInterval(function() {
                                    jQuery('#modalStudentEdit #' + i).val(v);
                                    jQuery('#modalStudentEdit #' + i).trigger("change.select2");
                                    clearTimeout(timer);
                                }, 1000);
                            } else if (i == 'campus_id' || i == 'orgCampus_id') {
                                // We'll handle these separately after school/organisation is selected
                            } else if (i == 'gender') {
                                if(v != 'M' && v != 'F'){
                                  jQuery("#edit_other_gender").show();
                                  jQuery('#modalStudentEdit .other_gender').val(v);
                                  jQuery('#modalStudentEdit #' + i).val('O').change();
                                }else{
                                  jQuery("#edit_other_gender").hide();
                                  jQuery('#modalStudentEdit #' + i).val(v).change();
                                }

                            }else if (i == 'removed') {
                                jQuery('#modalStudentEdit input[name=removed][value=' + v + ']').prop('checked', true);
                            } else {
                                jQuery('#modalStudentEdit #' + i).val(v);
                                if (jQuery('#modalStudentEdit #' + i).hasClass('select2-hidden-accessible')) {
                                    // jQuery('#modalStudentEdit #' + i).trigger("change.select2"); // this do not triggers jQuery change event
                                    jQuery('#modalStudentEdit #' + i).trigger("change"); // this triggers jQuery change event
                                }
                            }
                        });
                    }
                });
            });

            // jQuery(document).on('change','#gender', function() {
            // jQuery('#gender').on('change', function() {
            //     if (this.value == 'Other') {
            //         jQuery("#edit_other_gender").show();
            //     } else {
            //         jQuery("#edit_other_gender").hide();
            //         jQuery('#modalStudentEdit .other_gender').val('');
            //     }
            // });

            jQuery('#modalStudentEdit').on('shown.bs.modal', function(e) {
                jQuery('#form-studentedit').validate({
                    errorElement: 'span',
                    errorClass: 'help-block error-help-block',

                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length ||
                            element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            error.insertAfter(element.parent());
                            // else just place the validation message immediatly after the input
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    highlight: function(element) {
                        jQuery(element).closest('.form-group').removeClass('has-success').addClass('has-error'); // add the Bootstrap error class to the control group
                    },

                    focusInvalid: false, // do not focus the last invalid input
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: {
                            email: true,
                            required: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return jQuery("#email").val();
                                    },
                                    id: function() {
                                        return jQuery("#id").val();
                                    },
                                },
                            },
                        },
                        // other_gender: {
                        //     required: function() {
                        //         return (jQuery('#gender').val() == 'Other');
                        //     }
                        // },
                        password: {
                            minlength: 5,
                        },
                        school: 'required',
                        // gender: 'required',
                        // state: 'required',
                        state: {
                        // new logic for states - START
                        required: function() {
                            return (jQuery('select[name=state] option[value]').length > 0);
                        }
                        // new logic for states - START
                        },
                        // postcode: 'required',
                        // year: 'required',
                        school: {
                            required: function(element) {
                                return $("#organisation_id").val() == '';
                            }
                        },
                        organisation: {
                            required: function(element) {
                                return $("#school_id").val() == '';
                            }
                        },
                        graduate_year: {
                            required: function() {
                                return (jQuery('#year').val() == 7);
                            }
                        }
                    },
                    messages: {
                        email: {
                            remote: 'This email is already in use!'
                        },
                        school: {
                            required: 'The school field is required when organisation is not present.'
                        },
                        organisation: {
                            required: 'The organisation field is required when school is not present.'
                        }
                    }
                })
            });

            jQuery('#year').on('change', function() {
                if (this.value == 7) {
                    jQuery("#graduated_year").show();
                    jQuery(this).parent().parent().removeClass('col-md-12').addClass('col-md-6');
                } else {
                    jQuery("#graduated_year").hide();
                    jQuery("#graduated_year select").val('').change();
                    jQuery(this).parent().parent().removeClass('col-md-6').addClass('col-md-12');

                }
            });

            jQuery("#modalStudentEdit").on('hidden.bs.modal', function() {
                jQuery('#campus-edit').hide();
                jQuery('#orgCampus-edit').hide();
                jQuery('#edit-state-container').hide();
                jQuery('#campus_id').html('<option value=""></option>').trigger("change.select2");
                jQuery('#orgCampus_id').html('<option value=""></option>').trigger("change.select2");
                jQuery("#form-studentedit input:not([type=hidden]):not([type=submit]):not([type=radio]), #form-studentedit select:not([type=hidden]").val('');
                jQuery("#form-studentedit select").trigger("change.select2");
                // validator.destroy()
            });
        });
    </script>
    {{-- {!! JsValidator::formRequest('App\Http\Requests\UpdateStudent')->selector('#form-studentedit') !!} --}}
@endpush
