const DocMenuConfig = [
    {
        pages: [
            {
                heading: "Home",
                route: "/dashboard",
                isExternal: false,
                svgIcon: "media/icons/sidebar/home-solid.svg",
                fontIcon: "bi-app-indicator",
            },
            {
                heading: "Profile",
                isExternal: true,
                route: "/profiles/edit",
                svgIcon: "media/icons/sidebar/user-solid.svg",
                fontIcon: "bi-person",
            },
            {
                sectionTitle: "Explore",
                route: "/explore",
                svgIcon: "media/icons/sidebar/explore.svg",
                fontIcon: "bi-person",
                sub: [
                    {
                        heading: "Industries",
                        route: "/explore/industries",
                        isExternal: false,
                    },
                    {
                        heading: "Employers",
                        // route: "https://au.indeed.com/",
                        route: "/explore/employers",
                        isExternal: false,
                    },
                    //   {
                    //     heading: "Industries",
                    //     route: "/exploreindustries",
                    //     isExternal:true,
                    //   },
                    {
                        heading: "e-Magazine",
                        route: "e-magazines/editions",
                        isExternal: true,
                    },
                ],
            },

            {
                sectionTitle: "Tasks",
                route: "/tasks",
                svgIcon: "media/icons/sidebar/laptop-solid.svg",
                fontIcon: "bi-stack",
                sub: [
                    {
                        heading: "My Paths",
                        route: "/my-path",
                        isExternal: true
                    },
                    {
                        heading: "Lessons",
                        route: "/tasks/lessons",
                        isExternal: false,
                    },
                    {
                        heading: "Virtual Work Experience",
                        route: "/tasks/vwe",
                        isExternal: false,
                    },
                    //   {
                    //     heading: "Virtual Work Experience",
                    //     route: "/exploreworkexperience",
                    //     isExternal:true,
                    //   },
                    {
                        heading: "Skills Training",
                        route: "/tasks/skillstraining",
                        isExternal: false,
                    },
                    //   {
                    //     heading: "Skills Training",
                    //     route: "/wew/skillstraining",
                    //     isExternal:true,
                    //   },
                    {
                        heading: "Work, Health & Safety",
                        route: "/wew/workhealthsafety",
                        isExternal: true,
                    }
                ],
            },
            {
                sectionTitle: "Tools",
                route: "/tools",
                svgIcon: "media/icons/sidebar/toolbox-solid.svg",
                fontIcon: "bi-bar-chart",
                isExternal: true,
                sub: [
                    {
                        heading: "Job Finder",
                        // route: "https://au.indeed.com/",
                        route: "tools/jobfinder",
                        isExternal: false,
                    },
                    {
                        heading: "Scholarship Finder",
                        route: "/tools/scholarshipsfinder",
                        // isExternal: true,
                    },
                    {
                        heading: "Resume Builder",
                        route: "/cvs",
                        isExternal: true,
                    },
                    {
                        heading: "Course Finder",
                        route: "/tools/coursefinder",
                        // isExternal: true,
                    },
                    {
                        heading: "ePortfolio",
                        route: "/eportfolio",
                        isExternal: true,
                    },
                    {
                        heading: "Subject Selections",
                        route: "/subjects-selection",
                        isExternal: true,
                    },
                    {
                        heading: "Career Profiling",
                        route: "/profiler",
                        isExternal: true,
                    },
                    {
                        heading: "Video Profiling",
                        route: "career/profiling",
                        isExternal: true,
                    },
                ]
            },
            {
                sectionTitle: "Support",
                route: "/support",
                svgIcon: "media/icons/sidebar/support.svg",
                fontIcon: "bi-node-minus",
                svgClass: "connect-icon",
                isExternal: true,
                sub: [
                    // {
                    //     heading: "Noticeboard",
                    //     route: "/noticeboard",
                    //     isExternal: true,
                    // }
                    {
                        heading: "Help Centre",
                        route: "https://help.thecareersdepartment.com/en/ ",
                        isExternal: true,
                        inNewTab: true,
                    }
                ]
            },

        ],
    },


];

export default DocMenuConfig;
