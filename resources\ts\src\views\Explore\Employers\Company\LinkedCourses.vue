<template>
    <div class="card">
        <div class="card-header border-0 px-xl-0">
            <div class="card-title fs-1">
               Linked Courses
            </div>
        </div>
        <div class="card-body fs-4 px-xl-0 p-xl-0">
            <p>Browse all courses associated with this company.</p>
        </div>
    </div>
    <div class="card">
        <div class="card-body px-xl-0 pt-0">
            <div class="row justify-content-start align-items-center p-0">
                <div class="col-12 col-md-6 col-lg-3 mt-2 position-relative">
                    <span class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute top-50 translate-middle ms-6">
                        <svg width="10" height="10" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M11.7429 10.3431C12.7112 9.02181 13.1449 7.38361 12.9572 5.75627C12.7695 4.12893 11.9743 2.63246 10.7307 1.56625C9.48701 0.500045 7.88665 -0.0572725 6.24973 0.00580065C4.61282 0.0688738 3.06008 0.747686 1.90217 1.90643C0.744249 3.06518 0.0665484 4.6184 0.00464653 6.25536C-0.0572553 7.89231 0.501207 9.49228 1.56831 10.7352C2.6354 11.9781 4.13244 12.7722 5.75992 12.9587C7.38739 13.1452 9.02528 12.7104 10.3459 11.7411H10.3449C10.3742 11.7811 10.4069 11.8195 10.4429 11.8561L14.2929 15.7061C14.4804 15.8938 14.7348 15.9992 15 15.9993C15.2653 15.9994 15.5198 15.8941 15.7074 15.7066C15.895 15.5191 16.0005 15.2647 16.0006 14.9995C16.0007 14.7342 15.8954 14.4798 15.7079 14.2921L11.8579 10.4421C11.8222 10.4059 11.7837 10.3735 11.7429 10.3431ZM12.0009 6.49912C12.0009 7.22139 11.8586 7.93659 11.5822 8.60388C11.3058 9.27117 10.9007 9.87749 10.39 10.3882C9.87926 10.8989 9.27295 11.3041 8.60566 11.5805C7.93837 11.8569 7.22317 11.9991 6.5009 11.9991C5.77863 11.9991 5.06343 11.8569 4.39614 11.5805C3.72885 11.3041 3.12253 10.8989 2.61181 10.3882C2.10109 9.87749 1.69596 9.27117 1.41956 8.60388C1.14316 7.93659 1.0009 7.22139 1.0009 6.49912C1.0009 5.04043 1.58036 3.64149 2.61181 2.61004C3.64326 1.57859 5.04221 0.999124 6.5009 0.999124C7.95959 0.999124 9.35853 1.57859 10.39 2.61004C11.4214 3.64149 12.0009 5.04043 12.0009 6.49912Z"
                                fill="#606060" />
                        </svg>
                    </span>
                    <input v-model="searchQuery"
                        class="form-control form-control-solid ps-10 py-4" type="text" placeholder="Search keywords" />
                </div>
                <div class="col-12 col-md-6 col-lg-2 mt-2">
                    <button type="button"
                        class="btn btn-light d-flex justify-content-center align-items-center py-4 w-100"
                        @click="onSearch">
                       Search
                    </button>
                </div>
            </div>
             <p class="fw-bold fs-4 mb-5 mt-7 mx-2">{{total || courses.length }} {{ (total ?? courses.length) == 1 ? "item" : "items"}} Found</p>
            <div class="row mt-10" v-if="courses.length > 0">
                <div class="col-12 col-xl-6" v-for="course in courses" :key="course.id">
                    <div class="border border-primary-subtle p-4 rounded d-flex mb-4 position-relative">
                        <div class="flex-shrink-0 me-4">
                            <img :src="course.tileimage_fullpath" alt="Course Image" class="img-fluid rounded"
                                style="width: 130px; height: 130px; object-fit: cover; background-color: #e0e0e0;" />
                        </div>
                        <div class="flex-grow-1 d-flex flex-column">
                            <h5 class="fw-bold mb-3">{{ course.title }}</h5>
                            <div class="mt-auto mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-12 col-lg-6">
                                        <span :class="course.progress.badgeClass">
                                            {{ course.progress.text }}
                                        </span>
                                    </div>
                                    <div class="col-12 col-lg-6">
                                        <i v-if="course.level" class="bi bi-bar-chart-fill me-2 text-dark"></i>
                                        <span class="text-dark">{{ course.level }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <p class="text-center fs-4 text-gray-600">No courses found.</p>
            </div>
            <div class="d-flex justify-content-end py-10" v-if="!loading && total > 0">
                <TablePagination 
                    :total-pages="lastPage" 
                    :total="total"
                    :per-page="perPage" 
                    :current-page="currentPage"
                    @page-change="onPageChange" />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch } from 'vue';
import TablePagination from '@/components/kt-datatable/table-partials/table-content/table-footer/TablePagination.vue';
import { debounce } from "lodash";

export default defineComponent({
    components: {
        TablePagination,
    },
    props: {
        linkedCourses: {
            type: Object,
            default: () => ({
                data: [],
                meta: {
                    total: 0,
                    current_page: 1,
                    per_page: 6,
                    last_page: 1,
                    from: 1,
                    to: 4,
                },
            }),
        },
    },
    emits: ['page-change', 'search'],

    setup(props, { emit }) {
        const searchQuery = ref('');
        const loading = ref(false);

        // Computed properties for pagination
        const total = computed(() => props.linkedCourses?.meta?.total || 0);
        const lastPage = computed(() => props.linkedCourses?.meta?.last_page || 1);
        const perPage = computed(() => props.linkedCourses?.meta?.per_page || 4);
        const currentPage = computed(() => props.linkedCourses?.meta?.current_page || 1);

        // Map dynamic data and remove duplicates
        const courses = computed(() => {
            if (!props.linkedCourses.data || !Array.isArray(props.linkedCourses.data)) {
                return [];
            }
            // Remove duplicates based on id
            const uniqueCourses = Array.from(
                new Map(props.linkedCourses.data.map(item => [item.module_id, {
                    id: item.module_id,
                    title: item.title,
                    level: item.level,
                    progress: {
                        text: getProgressText(item.status || 'not_started'),
                        badgeClass: getBadgeClass(item.status || 'not_started'),
                    },
                    tileimage_fullpath: item.tileimage_fullpath,
                }])).values());

            return uniqueCourses;
        });

      const getProgressText = (status: string): string => {
            switch (status.toLowerCase()) {
                case 'completed':
                    return 'Completed';
                case 'in progress':
                    return 'In Progress';
                case 'not_started':
                default:
                    return 'Not Started';
            }
        };

        // Function to determine badge class based on status
        const getBadgeClass = (status: string): string => {
            const baseClasses = 'badge rounded-pill badge-module-status';
            switch (status.toLowerCase()) {
                case 'completed':
                    return `${baseClasses} badge-faded-success`;
                case 'in progress':
                    return `${baseClasses} badge-in-progress`;
                case 'not_started':
                default:
                    return `${baseClasses} badge-not-started`;
            }
        };

        // Handle search
        const onSearch = () => {
            emit('search', searchQuery.value);
        };

        // Handle page change
        const onPageChange = (page: number) => {
            if (page >= 1 && page <= lastPage.value) {
                emit('page-change', page);
            }
        };

        const debouncedSearch = debounce(() => {
            if (searchQuery.value === '') {
                emit('search', ''); 
            }
        }, 500); 

        watch(searchQuery, () => {
            debouncedSearch();
        });

        return {
            searchQuery,
            courses,
            onSearch,
            onPageChange,
            loading,
            total,
            lastPage,
            perPage,
            currentPage,
        };
    },
});
</script>

<style scoped>

.badge-faded-success {
    background: rgba(47, 192, 82, 0.13);
    color: #2FC052;
}

.badge-in-progress{
    color: #2861F6;
    background: rgba(40, 97, 246, 0.13);
}

.badge-not-started {
    background: #F8F8F8;
    color: #000;
}

</style>