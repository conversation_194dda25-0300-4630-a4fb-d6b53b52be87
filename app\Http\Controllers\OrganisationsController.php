<?php

namespace App\Http\Controllers;

use App\Campus;
use App\Country;
use App\Events\LicenseRenewed;
use App\Exports\SchoolsExport;
use App\Organisation;
use App\Role;
use App\SchoolPlan;
use App\Standard;
use App\SchoolDetail;
use App\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use Excel;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use DB;

class OrganisationsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $schools = Organisation::confirmed()->paginate(10);
        $states = Country::find(1)->states;
        return view('organisations.index', compact('schools', 'states'));
    }

    public function serverSide(Request $request)
    {
        $schooldetails = SchoolDetail::whereHas('organisation')->with(['organisation', 'organisation.plans'])->whereOrderConfirmed('1');
        $datatable = Datatables::of($schooldetails)
            ->filter(function ($query) {
                if ($organisation = request('organisation')) {
                    $query->where('name', 'like', "%{$organisation}%");
                }
                if ($type = request('type')) {
                    $query->where('type', $type);
                }
                if ($gender = request('gender')) {
                    $query->where('gender', $gender);
                }
                if ($state = request('state')) {
                    $query->whereHas('organisation', function ($q) use ($state) {
                        $q->whereStateId($state);
                    });
                }
                if (request('active_inactive') == 'Active') {
                    $query->whereDate('subscription_ending_on', '>=', Carbon::now());
                } elseif (request('active_inactive') == 'Inactive') {
                    $query->whereDate('subscription_ending_on', '<=', Carbon::now());
                }
            }, true);

        return $datatable->addColumn('password', function ($schooldetail) {
            return $schooldetail->organisation->password;
        })
            ->addColumn('logo', function ($schooldetail) {
                if ($schooldetail->logo) {
                    return '<img src="' . Storage::url($schooldetail->logo) . '" class="img-fluid" alt="">';
                }
                return '';
            })

            ->addColumn('state', function ($schooldetail) {
                return $schooldetail->organisation?->state?->code;
            })
            ->addColumn('postcode', function ($schooldetail) {
                return $schooldetail->organisation->postcode;
            })
            // ->addColumn('accountcreated', function ($schooldetail) {
            //     return $schooldetail->organisation->accountCreated();
            // })
            ->addColumn('accountcreated', function ($schooldetail) {
                // $this->fixcount($schooldetail->organisation, $schooldetail->total_students);
                // return "In Progress";
                $account_limit = $schooldetail->organisation->account_limit;
                $total_students = $schooldetail->total_students;
                if (($total_students >= $account_limit)) {
                    return '<span class="label label-danger fs-14">' . $total_students . '/' . $account_limit . '</span>';
                } elseif ((($account_limit - $total_students) < 20)) {
                    return '<span class="label label-warning fs-14">' . $total_students . '/' . $account_limit . '</span>';
                } else {
                    $total = ($account_limit != INF) ? $account_limit : '&infin;';
                    return '<span class="label fs-14">' . $total_students . '/' . $total . '</span>';
                }
            })
            ->addColumn('graduated_students', function ($schooldetail) {
                return $schooldetail->total_graduated_students;
            })
            ->addColumn('inactive_students', function ($schooldetail) {
                return $schooldetail->total_inactive_students;
            })
            ->addColumn('plans', function ($schooldetail) {
                return $schooldetail->organisation->plans->implode('type', ' | ');
            })
            ->addColumn('subscription_size', function ($schooldetail) {
                return $schooldetail->organisation->plans->implode('name', ' | ');
            })
            ->addColumn('years', function ($schooldetail) {
                return implode(', ', $schooldetail->organisation->plan_years);
            })
            ->addColumn('subscription_end_date', function ($schooldetail) {
                return Carbon::parse($schooldetail->subscription_ending_on)->toFormattedDateString();
            })
            ->addColumn('action', '<a href="{{route("organisations.edit", $school_id)}}" class="m-r-10"><i class="fa fa-edit"></i></a> <a  data-id="{{$school_id}}"  id="schoolImport" class="m-r-10" href="/studentimport/{{$school_id}}"><i class="fa fa-upload"></i></a> <a href="organisations/{{$school_id}}" data-method="delete" data-token="{{csrf_token()}}" data-confirm="Are you sure?"> <i class="fa fa-trash-o text-danger"></i> </a>')
            ->rawColumns(['logo', 'accountcreated', 'action',])
            ->make(true);
    }

    public function export()
    {
        return Excel::download(new SchoolsExport, 'schools.xlsx');
    }


    // public function ordered()
    // {
    //     $schools = School::ordered()->paginate(10);
    //     return view('schools.ordered', compact('schools'));
    // }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function fixcount($school, $count)
    {
        SchoolDetail::where("school_id", $school->id)->update(["student_count" => $count]);
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $countries = Country::All();
        $years = Standard::all();
        $schoolPlans = SchoolPlan::all();
        $basicPlans = $schoolPlans->where('type', 'Standard');
        $proPlans = $schoolPlans->where('type', 'Premium');
        return view('organisations.create', compact('countries', 'years', 'basicPlans', 'proPlans'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $school = new Organisation;
        $school->name = $request->school_name;
        $school->role_id = Role::whereName('Organisation')->value('id');
        $school->email = Str::random(20);
        $school->password = $request->password;
        $school->country_id = $request->country;
        $school->state_id = $request->state;
        $school->postcode = $request->postcode;

        $detail = new SchoolDetail();

        if ($request->type) {
            $detail->type = $request->type;
        }
        if ($request->gender) {
            $detail->gender = $request->gender;
        }
        if ($request->suburb) {
            $detail->suburb = $request->suburb;
        }
        $detail->name = $request->school_name;

        if ($request->logo) {
            $logo = $request->logo->store('attachments/schools', ['visibility' => 'public']);
            $detail->logo = $logo;
        }
        $detail->contact_person = $request->contactperson;
        $detail->position = $request->position;
        $detail->email = $request->email;
        $detail->phone = $request->phone;
        $detail->order_confirmed = $request->confirm;
        if ($request->confirm == '1' && !$detail->confirmed_at) {
            $detail->confirmed_at = Carbon::now();
        }
        $detail->subscription_ending_on = $request->subscription_enddate;
        $detail->account_limit = $request->account_limit;
        $detail->livechat = $request->livechat;


        $school->save();
        $school->detail()->save($detail);

        $campuses = $request->campuses;

        if (!empty($campuses)) {
            foreach ($campuses as $key => $name) {
                if ($name) {
                    $campus = new Campus();
                    $campus->name = $name;
                    $school->campuses()->save($campus);
                }
            }
        }

        $school->plans()->detach();

        if ($request->basic_plan) {
            foreach ($request->basic_years as $year) {
                $basicYears['year_' . $year] = '1';
            }
            $school->plans()->attach($request->basic_plan, $basicYears);
        }
        if ($request->pro_plan) {
            foreach ($request->pro_years as $year) {
                $proYears['year_' . $year] = '1';
            }
            $school->plans()->attach($request->pro_plan, $proYears);
        }

        return redirect()->route('organisations.index')->with('message', 'Organistion created successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function show(School $school)
    {
        // if (request()->wantsJson()) {
        //     $school->detail->state_id = $school->state_id;
        //     $school->detail->postcode = $school->postcode;
        //     return $school->detail;
        // }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $school = Organisation::with('detail', 'plans')->findOrFail($id);
        $countries = Country::All();
        $years = Standard::all();
        $schoolPlans = SchoolPlan::all();
        $basicPlans = $schoolPlans->where('type', 'Standard');
        $proPlans = $schoolPlans->where('type', 'Premium');
        return view('organisations.edit', compact('school', 'countries', 'years', 'basicPlans', 'proPlans'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $school = Organisation::findOrFail($id);

        $school_had_campuses = $school->campuses()->pluck('id');

        $school->update([
            'name' => $request->school_name,
            'password' => $request->password,
            'country_id' => $request->country,
            'state_id' => $request->state,
            'postcode' => $request->postcode,
        ]);

        $detail = $school->detail;

        // if ($request->current_logo == "" && Storage::exists($detail->logo)) {
        //     Storage::delete($detail->logo);
        // }
        // if ($request->new_logo) {
        //     $logo = $request->new_logo->store('attachments/schools', ['visibility' => 'public']);
        //     if ($detail->logo && Storage::exists($detail->logo)) {
        //         Storage::delete($detail->logo);
        //     }
        // } else {
        //     $logo = $request->current_logo;
        // }

        if ($request->type) {
            $detail->type = $request->type;
        }
        if ($request->gender) {
            $detail->gender = $request->gender;
        }
        if ($request->suburb) {
            $detail->suburb = $request->suburb;
        }
        $detail->name = $request->school_name;
        // $detail->logo = $logo;
        $detail->contact_person = $request->contactperson;
        $detail->position = $request->position;
        $detail->email = $request->email;
        $detail->phone = $request->phone;
        $detail->order_confirmed = $request->confirm;
        if ($request->confirm == '1' && !$detail->confirmed_at) {
            $detail->confirmed_at = Carbon::now();
        }
        if ($request->subscription_start_date) {
            $detail->subscription_start_date = $request->subscription_start_date;
        }
        $olddate = $detail->subscription_ending_on;
        $detail->subscription_ending_on = $request->subscription_ending_on;
        $detail->account_limit = $request->account_limit;
        $detail->livechat = $request->livechat;


        $school->detail()->save($detail);

        $campusIds = $request->campusids;
        $campuses = $request->campuses;
        $diff = $school->campuses()->pluck('id')->diff($campusIds);
        Campus::find($diff)->each->delete();

        if (!empty($campuses)) {
            foreach ($campuses as $key => $name) {
                if ($name) {
                    if (isset($campusIds[$key])) {
                        $campus = Campus::find($campusIds[$key]);
                        $campus->name = $name;
                        $campus->save();
                    } else {
                        $campus = new Campus();
                        $campus->name = $name;
                        $school->campuses()->save($campus);
                    }
                }
            }
        }

        if (!$school_had_campuses && $school->campuses()->exists()) {
            $teachers = $school->teachers()->get();
            if ($teachers) {
                foreach ($teachers as $teacher) {
                    $teacher->campuses()->sync($school->campuses()->pluck('id'));
                }
            }
        } elseif (($school_had_campuses && $school->campuses()->doesntExist()) || $diff) {
            $users = $school->users()->pluck('id');
            DB::table('campus_user')->whereNotIn('campus_id', $school->campuses()->pluck('id'))->whereIn('user_id', $users)->delete();
        }

        $school->plans()->detach();

        if ($request->basic_plan) {
            foreach ($request->basic_years as $year) {
                $basicYears['year_' . $year] = '1';
            }
            $school->plans()->attach($request->basic_plan, $basicYears);
        }
        if ($request->pro_plan) {
            foreach ($request->pro_years as $year) {
                $proYears['year_' . $year] = '1';
            }
            $school->plans()->attach($request->pro_plan, $proYears);
        }

        if ($olddate != $detail->subscription_ending_on) {
            $students = $school->students()->with('parents')->get();
            foreach ($students as $student) {
                \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));
                if ($student->parents) {
                    foreach ($student->parents as $parent) {
                        \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
                    }
                }
            }

            foreach ($school->staff as $teacher) {
                \Event::dispatch(new LicenseRenewed(User::where("id", $teacher->id)->first()));
            }
        }

        Cache::tags(['userhasProAccess', 'school.' . $school->id])->flush();
        Cache::forget('orgPlans' . $school->id);
        Cache::forget('standards' . $school->id);
        foreach (Standard::all() as $key => $year) {
            Cache::forget('wewmenuY' . $year->id . 'S' . $school->id);
        }


        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Order updated successfully!');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\School  $school
     * @return \Illuminate\Http\Response
     */
    public function destroy(Organisation $organisation)
    {
        // $this->authorize('update', $school);
        // dd($school);
        $organisation->delete();

        if (request()->wantsJson()) {
            return response([], 204);
        }

        // return redirect('/schools');
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Organisation has been deleted successfully!');
    }

    public function orgToSchool($id)
    {
        $organisation = Organisation::find($id);

        $staff = $organisation->staff()->get();
        foreach ($staff as $key => $staf) {
            $staf->school_id = $id;
            $staf->role_id = Role::whereName('Teacher')->value('id');
            $staf->organisation_id = NULL;
            $staf->save();
        }

        $students = $organisation->students()->get();
        foreach ($students as $key => $student) {
            $student->school_id = $id;
            $student->organisation_id = NULL;
            $student->save();
        }

        $organisation->role_id = Role::whereName('School')->value('id');
        $organisation->save();

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Organisation changed to School!');
    }
}
