@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('add-schools'))
@section('content')
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    @push('stylesheets')
        <link media="screen" type="text/css" rel="stylesheet" href="{{ asset('assets/plugins/switchery/css/switchery.min.css') }}">
    @endpush
    @push('styles')
        <style>
            .custom-input:focus {
                box-shadow: none;
            }

            .bg-grey-lightest {
                background-color: #f1f1f1;
                padding: 15px 20px;
            }

            .plan-table th {
                font-weight: bold;
            }

            .plan-table th,
            .plan-table td {
                padding: 0 10px;
            }

            .plan-table tr>th:nth-child(4),
            .plan-table tr>th:nth-child(5),
            .plan-table tr>td[colspan="2"]+td:nth-child(3),
            .plan-table tr>td:nth-child(4),
            .plan-table tr>td:nth-child(5) {
                text-align: center;
            }

            .custom-select2+label.error {
                margin-bottom: -5px;
            }

            .custom-black-radio {
                margin: 0.5rem;
            }

            .custom-black-radio input[type="checkbox"] {
                position: absolute;
                opacity: 0;
            }

            .custom-black-radio input[type="checkbox"]+label:before {
                content: '';
                background: #f4f4f4;
                border-radius: 100%;
                border: 2px solid #000;
                display: inline-block;
                width: 18px;
                height: 18px;
                position: relative;
                top: -0.2em;
                margin-right: 1em;
                vertical-align: top;
                cursor: pointer;
                text-align: center;
                transition: all 250ms ease;
                font-size: 0px;
            }

            .custom-black-radio input[type="checkbox"]:checked+label:before {
                background-color: #000;
                box-shadow: inset 0 0 0 2px #f4f4f4;
            }

            .custom-black-radio input[type="checkbox"]:focus+label:before {
                outline: none;
                border-color: #000;
            }

            .custom-black-radio input[type="checkbox"]:disabled+label:before {
                box-shadow: inset 0 0 0 2px #f4f4f4;
                border-color: #b4b4b4;
                background: #b4b4b4;
            }

            .custom-black-radio input[type="checkbox"]+label:empty:before {
                margin-right: 0;
            }

            /* Select2 coustomization */

            .custom-select2+.select2-container,
            .custom-select2+label+.select2-container {
                width: 100% !important;
            }

            .custom-select2+.select2-container.select2-container--focus .select2-selection,
            .custom-select2+.select2-container .select2-selection,
            .custom-select2+label+.select2-container.select2-container--focus .select2-selection,
            .custom-select2+label+.select2-container .select2-selection {
                border: none !important;
                cursor: pointer;
                padding: 0
            }

            .custom-select2+.select2-container .select2-selection::after,
            .custom-select2+label+.select2-container .select2-selection::after {
                position: absolute;
                left: 50%;
                top: 50%;
                content: "\f107";
                font-family: FontAwesome;
                font-size: 24px;
                line-height: 20px;
                transform: translate(-50%, -50%);
            }

            .hide-caret::after {
                display: none;
            }

            .select2-container.select2-container--open .select2-dropdown {
                border: 1px solid #aaa;
                /* width: auto !important; */
                min-width: 100px;
                border-radius: 0;
            }

            .select2-results__option {
                color: #000;
            }

            .select2-container--default .select2-results__option[aria-disabled=true] {
                color: #c0c0c0;
            }

            .select2-container--default .select2-results__option[aria-selected=true]:not(.select2-results__option--highlighted) {
                background-color: #fff;
            }

            .select2-container--default .select2-results__option[aria-selected=true]::after {
                content: '\f00c';
                font-family: FontAwesome;
                position: absolute;
                right: 10px;
                color: #070af8;
            }

            .custom-select2+.select2-container ul.select2-selection__rendered,
            .custom-select2+label+.select2-container ul.select2-selection__rendered {
                white-space: normal;
                position: relative;
                width: auto;
                padding-left: 0;
            }

            .hide-caret>ul.select2-selection__rendered {
                right: 0 !important;
            }

            .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice,
            .custom-select2+label+.select2-container ul.select2-selection__rendered>li.select2-selection__choice {
                border: none;
                background-color: transparent !important;
                padding: 0;
                margin: 4px 0 0 0;
                cursor: pointer;
            }

            .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-search,
            .custom-select2+label+.select2-container ul.select2-selection__rendered>li.select2-search {
                display: none;
            }

            .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice+li.select2-selection__choice::before,
            .custom-select2+label+.select2-container ul.select2-selection__rendered>li.select2-selection__choice+li.select2-selection__choice::before {
                content: ',';
                margin-right: 1px;
            }

            .custom-select2+.select2-container ul.select2-selection__rendered>li.select2-selection__choice>span.select2-selection__choice__remove,
            .custom-select2+label+.select2-container ul.select2-selection__rendered>li.select2-selection__choice>span.select2-selection__choice__remove {
                display: none;
            }

            .menu-table thead tr th {
                color: #000;
                font-size: 16px;
                font-weight: 700;
            }

            .menu-table tr[data-toggle="collapsible"] {
                text-transform: uppercase;
                cursor: pointer;
            }

            .menu-table tr[data-toggle="collapsible"]>td:first-child {
                text-align: right;
                padding-left: 2px;
                padding-right: 2px;
                /* padding: 20px 2px; */
            }

            .menu-table tr[data-toggle="collapsible"]>td:first-child:before {
                content: "\f054";
                display: inline-block;
                font-size: 8px;
                top: -1px;
                position: relative;
                margin-right: 8px;
                font-family: 'FontAwesome';
                -webkit-transition: all 0.12s linear;
                transition: all 0.12s linear;
            }

            .menu-table tr.shown[data-toggle="collapsible"]>td:first-child:before {
                -webkit-transform: rotate(90deg);
                -ms-transform: rotate(90deg);
                transform: rotate(90deg);
            }

            .menu-table tr[data-toggle="collapsible"]>td {
                background-color: #f1f1f1 !important;
            }
        </style>
    @endpush

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header separator">
                    <div class="card-title">Edit order</div>
                </div>
                <div class="card-block">
                    <form id="order-add" method="POST" role="form" autocomplete="off" enctype="multipart/form-data"
                        action="{{ route('schools.store') }}">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-4">
                                <div class="form-group form-group-default required">
                                    <label>School</label>
                                    <input type="text" class="form-control" name="school_name" value="">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group form-group-default  form-group-default-select2 ">
                                    <label> School Type </label>
                                    <select class="full-width" name="type" id="type" data-placeholder="Select.."
                                        data-init-plugin="select2">
                                        <option value=""></option>
                                        <option value="Independent">Independent</option>
                                        <option value="Catholic">Catholic</option>
                                        <option value="Government">Government</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group form-group-default  form-group-default-select2 ">
                                    <label> Institute Type </label>
                                    <select class="full-width" name="institute_type" id="institute_type"
                                        data-placeholder="Select.." data-init-plugin="select2">
                                        <option value=""></option>
                                        @foreach ($instituteTypes as $key => $value)
                                            <option value="{{ $key }}" {{ $value === 'School' ? 'selected' : '' }}>
                                                {{ $value }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-4">
                                <div class="form-group form-group-default">
                                    <label>Logo</label>
                                    <input type="file" class="form-control" name="new_logo" id="new_logo">
                                    <input type="hidden" class="form-control" name="current_logo" id="current-logopath"
                                        value="">
                                </div>
                            </div>
                            <div class="col-md-4 d-none">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Verified Domains</label>
                                    <select class="form-control full-width" name="verified_domains[]" id="verified_domains"
                                        data-init-plugin="select2" multiple>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 d-none">
                                <div class="form-group form-group-default required">
                                    <label>Privacy Link</label>
                                    <input type="text" class="form-control" name="privacy_link" id="privacy_link">
                                </div>
                            </div>
                        </div>
                        <div class="card" id="campuses">
                            <div class="card-header card-separator">
                                <div class="card-title">Campuses</div>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group form-group-default">
                                            <label>#<span class="number">1</span></label>
                                            <input type="text" class="form-control" name="campuses[]" value="">
                                        </div>
                                    </div>
                                </div>
                                <div class="m-b-10 clearfix text-right">
                                    <button class="btn btn-primary btn-custom-sm" type="button" id="btnAddCampus"> <i
                                            class="fa fa-plus" aria-hidden="true"></i> Add Another Campus </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 ">
                                    <label> School Gender </label>
                                    <select class="full-width" id="gender" name="gender" data-placeholder="Select.."
                                        data-init-plugin="select2">
                                        <option value=""></option>
                                        <option value="Coed"> Coed </option>
                                        <option value="Boys"> Boys </option>
                                        <option value="Girls"> Girls </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Password</label>
                                    <input type="text" class="form-control" id="password" name="password"
                                        value="">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Country</label>
                                    <select class="full-width" name="country"  data-init-plugin="select2">
                                        <option value=""></option>
                                        @foreach ($countries as $country)
                                            <option value="{{ $country->id }}">{{ $country->name }}</option>
                                        @endforeach
                                    </select>
                                    <div id="country-required-alert" class="text-danger mt-1"
                                    style="font-size: 12px; margin-left:12px;">Please select a country.</div>
                                </div>
                            </div>
                            <div class="col-md-6" id="school-state-container" style="display: none;">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>State</label>
                                    <select class="full-width" name="state_id" id="state_id" data-init-plugin="select2">
                                        @foreach ($states as $state)
                                            <option value="{{ $state->id }}">{{ $state->name }}</option>
                                        @endforeach
                                    </select>
                                    <div id="state-required-alert" class="text-danger mt-1"
                                    style="font-size: 12px; margin-left:12px;">Please select a state.</div>
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default">
                                    <label> Suburb </label>
                                    <input type="text" class="form-control" id="suburb" name="suburb"
                                        value="" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Postcode</label>
                                    <input type="text" class="form-control" name="postcode" value="">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>School contact person</label>
                                    <input type="text" class="form-control" name="contactperson" value="">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Position</label>
                                    <input type="text" class="form-control" name="position" value="">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" value="">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Phone</label>
                                    <input type="text" class="form-control" name="phone" value="">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default input-group date required">
                                    <div class="form-input-group">
                                        <label class="fade">Subscription Start Date</label>
                                        <input type="text" class="form-control" placeholder="Pick a date"
                                            name="subscription_start_date" value="">
                                    </div>
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default input-group date required" id="myDatepicker">
                                    <div class="form-input-group">
                                        <label class="fade">Subscription End Date</label>
                                        <input type="text" class="form-control" placeholder="Pick a date"
                                            name="subscription_ending_on" value="">
                                    </div>
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="form-group form-group-default">
                                    <label>Account Limit</label>
                                    <input type="number" class="form-control" name="account_limit" value="">
                                </div>
                            </div>
                            <div class="col-md-3 pt-3">
                                <label for="" class="p-r-10">Livechat </label>
                                <input type="hidden" class="" id="" name="livechat" value="0" />
                                <input type="checkbox" class="switchery" id="livechat" name="livechat"
                                    value="1" />
                            </div>
                            <div class="col-md-3 pt-3">
                                <label for="" class="p-r-10">Show Postcode </label>
                                <input type="hidden" class="" id="" name="show_postcode"
                                    value="0" />
                                <input type="checkbox" class="switchery" id="show_postcode" name="show_postcode"
                                    value="1" checked />
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6" id="schoolPlans">
                                <div class="form-group form-group-checkbox required">
                                    <label> School Access For </label>
                                    <div class="checkbox check-primary no-margin">
                                        <input type="checkbox" value="1" id="secondary_section"
                                            name="secondary_section" class="require-one-edit">
                                        <label for="secondary_section" class="no-margin">Secondary</label>
                                    </div>
                                    <div class="checkbox check-primary no-margin">
                                        <input type="checkbox" value="1" id="primary_section"
                                            name="primary_section" class="require-one-edit">
                                        <label for="primary_section" class="no-margin">Primary</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 d-none" id="tertiaryPlans">
                                <div class="card">
                                    <div class="card-header separator text-center">
                                        <div class="card-title oswald fs-20 bold">
                                            Tertiary Plan
                                        </div>
                                    </div>
                                    <div class="card-block">
                                        <table class="menu-table table table-hover dataTable no-footer" cellspacing="0">
                                            <thead>
                                                <tr>
                                                    <th width="5"></th>
                                                    <th width="400"></th>
                                                    <th>Access</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            <tbody class="labels">
                                                <tr class="shown" data-toggle="collapsible">
                                                    <td></td>
                                                    <td colspan="2">
                                                        Explore
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="child-menu">
                                                <tr>
                                                    <td></td>
                                                    <td>Industries</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-industriesAll"
                                                                name="tertiary_menus[industries]">
                                                            <label for="tertiary-menu-industriesAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>eMagazine</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-emagazineAll"
                                                                name="tertiary_menus[emagazine]">
                                                            <label for="tertiary-menu-emagazineAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tbody class="labels">
                                                <tr data-toggle="collapsible">
                                                    <td></td>
                                                    <td colspan="2">
                                                        Tasks
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="child-menu" style="display: none;">
                                                <tr>
                                                    <td></td>
                                                    <td>Paths</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-myPathAll"
                                                                name="tertiary_menus[myPath]">
                                                            <label for="tertiary-menu-myPathAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Game Plan</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-gameplanAll"
                                                                name="tertiary_menus[gameplan]">
                                                            <label for="tertiary-menu-gameplanAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Profiling</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-profilingAll"
                                                                name="tertiary_menus[profiling]">
                                                            <label for="tertiary-menu-profilingAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Lessons</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-lessonsAll"
                                                                name="tertiary_menus[lessons]">
                                                            <label for="tertiary-menu-lessonsAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Virtual Work Experience</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-vweAll" name="tertiary_menus[vwe]">
                                                            <label for="tertiary-menu-vweAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Skills Training</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-stAll" name="tertiary_menus[st]">
                                                            <label for="tertiary-menu-stAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Work, Health & Safety</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-whsAll" name="tertiary_menus[whs]">
                                                            <label for="tertiary-menu-whsAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tbody class="labels">
                                                <tr data-toggle="collapsible">
                                                    <td></td>
                                                    <td colspan="2">
                                                        Tools
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="child-menu" style="display: none;">
                                                <tr>
                                                    <td></td>
                                                    <td>Job Finder</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-jobAll" name="tertiary_menus[job]">
                                                            <label for="tertiary-menu-jobAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Scholarship Finder</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-scholarshipAll"
                                                                name="tertiary_menus[scholarship]">
                                                            <label for="tertiary-menu-scholarshipAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Resume Builder</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-resumeAll"
                                                                name="tertiary_menus[resume]">
                                                            <label for="tertiary-menu-resumeAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Course Finder</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-courseAll"
                                                                name="tertiary_menus[course]">
                                                            <label for="tertiary-menu-courseAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>ePortfolio</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-eportfolioAll"
                                                                name="tertiary_menus[eportfolio]">
                                                            <label for="tertiary-menu-eportfolioAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Subject Selections</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-subjectAll"
                                                                name="tertiary_menus[subject]">
                                                            <label for="tertiary-menu-subjectAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Career Profiling</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-careerProfilingAll"
                                                                name="tertiary_menus[careerProfiling]">
                                                            <label for="tertiary-menu-careerProfilingAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>Video Profiling</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-videoProfilingAll"
                                                                name="tertiary_menus[videoProfiling]">
                                                            <label for="tertiary-menu-videoProfilingAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>

                                            <tbody class="labels">
                                                <tr class="shown" data-toggle="collapsible">
                                                    <td></td>
                                                    <td colspan="2">
                                                        Connect
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="child-menu">
                                                <tr>
                                                    <td></td>
                                                    <td>Noticeboard</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll" checked="checked"
                                                                id="tertiary-menu-noticeboardAll"
                                                                name="tertiary_menus[noticeboard]">
                                                            <label for="tertiary-menu-noticeboardAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="labels">
                                                <tr class="shown" data-toggle="collapsible">
                                                    <td></td>
                                                    <td colspan="2">
                                                        Profile Visibility
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tbody class="child-menu">
                                                <tr>
                                                    <td></td>
                                                    <td>Show Visibility Option</td>
                                                    <td>
                                                        <div class="checkbox check-primary">
                                                            <input type="checkbox" class="checkAll"
                                                                id="tertiary-menu-visibilityAll"
                                                                name="tertiary_menus[visibility]">
                                                            <label for="tertiary-menu-visibilityAll"></label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="primaryPlan" class="card" style="display: none;">
                            <div class="card-header separator text-center">
                                <div class="card-title oswald fs-20 bold">
                                    Primary Plan
                                </div>
                            </div>
                            <div class="card-block">
                                <div class="row clearfix my-3">
                                    <div class="col-md-6">
                                        <div class="bg-master-lightest padding-15">
                                            <div class="oswald-heading">STANDARD</div>
                                            <p>&nbsp;</p>
                                            <table class="full-width plan-table">
                                                <thead>
                                                    <tr>
                                                        <th>No. of<br />students</th>
                                                        <th>Value</th>
                                                        <th>Cost</th>
                                                        <th>Years</th>
                                                        <th>Tick box</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $plan = $basicPlans->whereNull('max_students')->first();
                                                    @endphp
                                                    <tr>
                                                        <td>{{ $plan->name }}</td>
                                                        <td>
                                                            @if ($plan->discount)
                                                                <span class="line-through">${{ $plan->value }}</span>
                                                                <span class="text-danger uppercase">{{ $plan->discount }}%
                                                                    off</span>
                                                            @else
                                                                {{ $plan->value ? "$" . $plan->value : '' }}
                                                            @endif
                                                        </td>
                                                        <td>{{ $plan->cost ? "$" . $plan->cost : 'POA' }}</td>
                                                        <td>
                                                            <select class="primary_basic-years custom-select2"
                                                                data-value="primaryplan{{ $plan->id }}" multiple>
                                                                @foreach ($primaryYears as $year)
                                                                    <option value="{{ $year->id }}">
                                                                        {{ $year->title }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="custom-black-radio">
                                                                <input type="checkbox" name="primary_basic_plan"
                                                                    value="{{ $plan->id }}"
                                                                    id="primary_plan{{ $plan->id }}">
                                                                <label for="primary_plan{{ $plan->id }}"></label>
                                                                <input type="hidden" class="cost"
                                                                    value="{{ $plan->cost }}">
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="bg-master-lightest padding-15">
                                            <div class="oswald-heading">PREMIUM</div>
                                            <p>Includes Live Chat and Virtual Work Experience</p>
                                            <table class="full-width plan-table">
                                                <thead>
                                                    <tr>
                                                        <th>No. of<br />students</th>
                                                        <th>Value</th>
                                                        <th>Cost</th>
                                                        <th>Years</th>
                                                        <th>Tick box</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                        $plan = $proPlans->whereNull('max_students')->first();
                                                    @endphp
                                                    <tr>
                                                        <td>{{ $plan->name }}</td>
                                                        @if ($plan->max_students)
                                                            <td>
                                                                @if ($plan->discount)
                                                                    <span class="line-through">${{ $plan->value }}</span>
                                                                    <span
                                                                        class="text-danger uppercase">{{ $plan->discount }}%
                                                                        discount</span>
                                                                @else
                                                                    {{ $plan->value ? "$" . $plan->value : '' }}
                                                                @endif
                                                            </td>
                                                            <td>{{ $plan->cost ? "$" . $plan->cost : 'POA' }}</td>
                                                        @else
                                                            <td colspan="2">
                                                                @if ($plan->discount)
                                                                    <span class="text-danger fs-14">{{ $plan->discount }}%
                                                                        DISCOUNT
                                                                        on each student over 800</span>
                                                                @endif
                                                            </td>
                                                        @endif
                                                        <td>
                                                            <select class="primary_pro-years custom-select2"
                                                                data-value="primaryplan{{ $plan->id }}" multiple>
                                                                @foreach ($primaryYears as $year)
                                                                    <option value="{{ $year->id }}">
                                                                        {{ $year->title }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <div class="custom-black-radio">
                                                                <input type="checkbox" name="primary_pro_plan"
                                                                    value="{{ $plan->id }}"
                                                                    id="primary_plan{{ $plan->id }}">
                                                                <label for="primary_plan{{ $plan->id }}"></label>
                                                                <input type="hidden" class="cost"
                                                                    value="{{ $plan->cost }}">
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="secondaryPlan" class="card" style="display: none;">
                            <div class="card-header separator text-center">
                                <div class="card-title oswald fs-20 bold">
                                    Secondary Plan
                                </div>
                            </div>
                            <div class="card-block">
                                <table class="menu-table table table-hover dataTable  no-footer" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th width="5"></th>
                                            <th width="400"></th>
                                            <th>All</th>
                                            @foreach ($secondaryYears as $year)
                                                <th>{{ $year->title }}</th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <tbody>

                                    <tbody class="labels">
                                        <tr class="shown" data-toggle="collapsible">
                                            <td></td>
                                            <td colspan="8">
                                                Explore
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody class="child-menu">
                                        <tr>
                                            <td></td>
                                            <td>Industries</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-industriesAll">
                                                    <label for="menu-industriesAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[industries][]" value="{{ $year->id }}"
                                                            id="menu-industry{{ $year->title }}">
                                                        <label for="menu-industry{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>eMagazine</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-emagazineAll">
                                                    <label for="menu-emagazineAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[emagazine][]" value="{{ $year->id }}"
                                                            id="menu-emagazine{{ $year->title }}">
                                                        <label for="menu-emagazine{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                    </tbody>

                                    <tbody class="labels">
                                        <tr data-toggle="collapsible">
                                            <td></td>
                                            <td colspan="8">
                                                Tasks
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody class="child-menu" style="display: none;">
                                        <tr>
                                            <td></td>
                                            <td>My Path</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-myPathAll">
                                                    <label for="menu-myPathAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[myPath][]" value="{{ $year->id }}"
                                                            id="menu-myPath{{ $year->title }}">
                                                        <label for="menu-myPath{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>

                                        <tr>
                                            <td></td>
                                            <td>Game Plan</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-gameplanAll">
                                                    <label for="menu-gameplanAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[gameplan][]" value="{{ $year->id }}"
                                                            id="menu-gameplan{{ $year->title }}">
                                                        <label for="menu-gameplan{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Profiling</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" @checked(true) class="checkAll"
                                                        checked="checked" id="menu-profilingAll">
                                                    <label for="menu-profilingAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[profiling][]" value="{{ $year->id }}"
                                                            id="menu-profiling{{ $year->title }}">
                                                        <label for="menu-profiling{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Lessons</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" @checked(true) class="checkAll"
                                                        checked="checked" id="menu-lessonsAll">
                                                    <label for="menu-lessonsAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[lessons][]" value="{{ $year->id }}"
                                                            id="menu-lessons{{ $year->title }}">
                                                        <label for="menu-lessons{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Virtual Work Experience</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-vweAll">
                                                    <label for="menu-vweAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true) class=""
                                                            name="menus[vwe][]" value="{{ $year->id }}"
                                                            id="menu-vwe{{ $year->title }}">
                                                        <label for="menu-vwe{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Skills Training</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-stAll">
                                                    <label for="menu-stAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true) class=""
                                                            name="menus[st][]" value="{{ $year->id }}"
                                                            id="menu-st{{ $year->title }}">
                                                        <label for="menu-st{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Work, Health & Safety</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-whsAll">
                                                    <label for="menu-whsAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true) class=""
                                                            name="menus[whs][]" value="{{ $year->id }}"
                                                            id="menu-whs{{ $year->title }}">
                                                        <label for="menu-whs{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                    </tbody>

                                    <tbody class="labels">
                                        <tr data-toggle="collapsible">
                                            <td></td>
                                            <td colspan="8">
                                                Tools
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody class="child-menu" style="display: none;">
                                        <tr>
                                            <td></td>
                                            <td>Job Finder</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-jobAll">
                                                    <label for="menu-jobAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[job][]" value="{{ $year->id }}"
                                                            id="menu-job{{ $year->title }}">
                                                        <label for="menu-job{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Scholarship Finder</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-scholarshipAll">
                                                    <label for="menu-scholarshipAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[scholarship][]" value="{{ $year->id }}"
                                                            id="menu-scholarship{{ $year->title }}">
                                                        <label for="menu-scholarship{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Resume Builder</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-resumeAll">
                                                    <label for="menu-resumeAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            class="" name="menus[resume][]"
                                                            value="{{ $year->id }}"
                                                            id="menu-resume{{ $year->title }}">
                                                        <label for="menu-resume{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Course Finder</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-courseAll">
                                                    <label for="menu-courseAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            class="" name="menus[course][]"
                                                            value="{{ $year->id }}"
                                                            id="menu-course{{ $year->title }}">
                                                        <label for="menu-course{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>ePortfolio</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-eportfolioAll">
                                                    <label for="menu-eportfolioAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            class="" name="menus[eportfolio][]"
                                                            value="{{ $year->id }}"
                                                            id="menu-eportfolio{{ $year->title }}">
                                                        <label for="menu-eportfolio{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td>Subject Selections</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-subjectAll">
                                                    <label for="menu-subjectAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            class="" name="menus[subject][]"
                                                            value="{{ $year->id }}"
                                                            id="menu-subject{{ $year->title }}">
                                                        <label for="menu-subject{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                    </tbody>

                                    <tbody class="labels">
                                        <tr class="shown" data-toggle="collapsible">
                                            <td></td>
                                            <td colspan="8">
                                                Connect
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody class="child-menu">
                                        <tr>
                                            <td></td>
                                            <td>Noticeboard</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll" checked="checked"
                                                        id="menu-noticeboardAll">
                                                    <label for="menu-noticeboardAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox" @checked(true)
                                                            name="menus[noticeboard][]" value="{{ $year->id }}"
                                                            id="menu-noticeboard{{ $year->title }}">
                                                        <label for="menu-noticeboard{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                    </tbody>
                                    <tbody class="labels">
                                        <tr class="shown" data-toggle="collapsible">
                                            <td></td>
                                            <td colspan="8">
                                                Profile Visibility
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody class="child-menu">
                                        <tr>
                                            <td></td>
                                            <td>Show Visibility Option</td>
                                            <td>
                                                <div class="checkbox check-primary">
                                                    <input type="checkbox" class="checkAll"
                                                        id="menu-visibilityAll">
                                                    <label for="menu-visibilityAll"></label>
                                                </div>

                                            </td>
                                            @foreach ($secondaryYears as $year)
                                                <td>
                                                    <div class="checkbox check-primary">
                                                        <input type="checkbox"
                                                            name="menus[visibility][]" value="{{ $year->id }}"
                                                            id="menu-visibility{{ $year->title }}">
                                                        <label for="menu-visibility{{ $year->title }}"></label>
                                                    </div>
                                                </td>
                                            @endforeach
                                        </tr>
                                    </tbody>
                                    </tbody>
                                </table>
                                {{-- <div class="row clearfix my-3">
                                <div class="col-md-6">
                                    <div class="bg-master-lightest padding-15">
                                        <div class="oswald-heading">STANDARD</div>
                                        <p>&nbsp;</p>
                                        <table class="full-width plan-table">
                                            <thead>
                                                <tr>
                                                    <th>No. of<br />students</th>
                                                    <th>Value</th>
                                                    <th>Cost</th>
                                                    <th>Years</th>
                                                    <th>Tick box</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($basicPlans as $plan)
                                                <tr>
                                                    <td>{{ $plan->name }}</td>
                                                    <td>
                                                        @if ($plan->discount) <span class="line-through">${{
                                                            $plan->value }}</span>
                                                        <span class="text-danger uppercase">{{ $plan->discount }}%
                                                            off</span>
                                                        @else
                                                        {{ $plan->value ? "$" . $plan->value : '' }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $plan->cost ? "$" . $plan->cost : 'POA' }}</td>
                                                    <td>
                                                        <select class="basic-years custom-select2"
                                                            data-value="plan{{ $plan->id }}" multiple>
                                                            @foreach ($secondaryYears as $year)
                                                            <option value="{{ $year->id }}" @foreach ($school->plans as $p) @if ($plan->id == $p->id) @foreach ($p->pivot->toArray() as $key => $value) @if (strpos($key, 'year_') !== false && $value == '1' && str_replace('year_', '', $key) == $year->id) selected
                                                                @endif @endforeach
                                                                @endif
                                                                @endforeach > {{ $year->title }} </option>
                                                            @endforeach
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <div class="custom-black-radio">
                                                            <input type="checkbox" name="basic_plan"
                                                                value="{{ $plan->id }}" id="menu-plan{{ $plan->id }}"
                                                                @foreach ($school->plans as $p) @if ($plan->id == $p->id) checked @endif @endforeach>
                                                            <label for="plan{{ $plan->id }}"></label>
                                                            <input type="hidden" class="cost" value="{{ $plan->cost }}">
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="bg-master-lightest padding-15">
                                        <div class="oswald-heading">PREMIUM</div>
                                        <p>Includes Live Chat and Virtual Work Experience</p>
                                        <table class="full-width plan-table">
                                            <thead>
                                                <tr>
                                                    <th>No. of<br />students</th>
                                                    <th>Value</th>
                                                    <th>Cost</th>
                                                    <th>Years</th>
                                                    <th>Tick box</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($proPlans as $plan)
                                                <tr>
                                                    <td>{{ $plan->name }}</td>
                                                    @if ($plan->max_students)
                                                    <td>
                                                        @if ($plan->discount)
                                                        <span class="line-through">${{ $plan->value }}</span>
                                                        <span class="text-danger uppercase">{{ $plan->discount }}%
                                                            discount</span>
                                                        @else
                                                        {{ $plan->value ? "$" . $plan->value : '' }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $plan->cost ? "$" . $plan->cost : 'POA' }}</td>
                                                    @else
                                                    <td colspan="2">
                                                        @if ($plan->discount)
                                                        <span class="text-danger fs-14">{{ $plan->discount }}% DISCOUNT
                                                            on each student over 800</span>
                                                        @endif
                                                    </td>
                                                    @endif
                                                    <td>
                                                        <select class="pro-years custom-select2" name=""
                                                            data-value="plan{{ $plan->id }}" multiple>
                                                            @foreach ($secondaryYears as $year)
                                                            <option value="{{ $year->id }}" @foreach ($school->plans as $p) @if ($plan->id == $p->id) @foreach ($p->pivot->toArray() as $key => $value) @if (strpos($key, 'year_') !== false && $value == '1' && str_replace('year_', '', $key) == $year->id) selected
                                                                @endif @endforeach
                                                                @endif
                                                                @endforeach > {{ $year->title }} </option>
                                                            @endforeach
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <div class="custom-black-radio">
                                                            <input type="checkbox" name="pro_plan"
                                                                value="{{ $plan->id }}" id="plan{{ $plan->id }}"
                                                                @foreach ($school->plans as $p) @if ($plan->id == $p->id) checked @endif @endforeach>
                                                            <label for="plan{{ $plan->id }}"></label>
                                                            <input type="hidden" class="cost" value="{{ $plan->cost }}">
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div> --}}
                            </div>
                        </div>
                        {{-- <p class="text-center"><b>Total subscription cost will be:</b> <span id="totalCost"></span></p>
                    --}}


                        <div class="checkbox check-primary text-center mb-5">
                            <input type="hidden" name="confirm" value="0" id="checkbox2">
                            <input type="checkbox" name="confirm" value="1" id="confirm">
                            <label for="confirm">Confirm</label>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @push('scriptslib')
        <script src="{{ asset('vendor/jsvalidation/js/additional-methods.min.js') }}"></script>
        <script src="https://cdn.ckeditor.com/4.14.1/standard/ckeditor.js"></script>
        <script type="text/javascript" src="{{ asset('assets/plugins/switchery/js/switchery.min.js') }}"></script>
    @endpush
    @push('scripts')
        <script>
            function resetNumbering() {
                jQuery('.number').each(function(i) {
                    i++;
                    jQuery(this).text(i);
                });
            }

            function showPlan() {
                if (jQuery('input[name=primary_section]').is(":checked")) {
                    jQuery('#primaryPlan').show('fast');
                } else {
                    jQuery('#primaryPlan').hide('fast');
                }
                if (jQuery('input[name=secondary_section]').is(":checked")) {
                    jQuery('#secondaryPlan').show('fast');
                } else {
                    jQuery('#secondaryPlan').hide('fast');
                }
            }
            jQuery(document).ready(function() {
                resetNumbering();
                showPlan();
                var max = 2
                jQuery(document).on('click', '#btnAddCampus', function() {
                    var row = jQuery("#campuses .card-block>.row:last");
                    if (row.find(".col-md-6").length >= max) {
                        jQuery(row).after('<div class="row">');
                    }
                    jQuery('#campuses .card-block>.row:last').append(
                        '<div class="col-md-6"> <div class="form-group form-group-default"> <label>#<span class="number"></span> <i role="button" class="fa fa-times text-danger float-right remove-campus"></i></label> <input type="text" class="form-control" name="campuses[]" value=""> </div> </div>'
                        );
                    resetNumbering();
                });

                jQuery(document).on('click', '.remove-campus', function() {
                    var colLen = jQuery(this).closest(".row").find(".col-md-6").length;
                    // if (jQuery('.remove-campus').length > 1) {
                    if (colLen < 2) {
                        jQuery(this).closest('.row').remove();
                    } else {
                        jQuery(this).closest('.col-md-6').remove();
                    }
                    // } else {
                    //     jQuery(this).closest('.col-md-6').find('input').val('');
                    // }

                    resetNumbering();
                })


                CKEDITOR.config.allowedContent = true;
                var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
                // Success color: #10CFBD
                elems.forEach(function(html, ) {
                    var switchery = new Switchery(html, {
                        color: '#000',
                        size: 'small',
                    });
                });
                var today = new Date();
                jQuery('.date').datepicker({
                    // startDate: today,
                    format: 'yyyy-mm-dd'
                });

                jQuery('.custom-select2').select2({
                    closeOnSelect: false
                });


                if (jQuery('[name=primary_basic_plan]:checked').length) {
                    val = jQuery('[name=primary_basic_plan]:checked').val();
                    jQuery("[data-value=primaryplan" + val + "]").attr("name", "primary_basic_years[]");
                };

                disableSelectedOption('.primary_basic-years', '.primary_pro-years')

                if (jQuery('[name=primary_pro_plan]:checked').length) {
                    val = jQuery('[name=primary_pro_plan]:checked').val();
                    jQuery("[data-value=primaryplan" + val + "]").attr("name", "primary_pro_years[]");
                }

                disableSelectedOption('.primary_pro-years', '.primary_basic-years');


                if (jQuery('[name=basic_plan]:checked').length) {
                    val = jQuery('[name=basic_plan]:checked').val();
                    jQuery("[data-value=plan" + val + "]").attr("name", "basic_years[]");
                };

                disableSelectedOption('.basic-years', '.pro-years')

                if (jQuery('[name=pro_plan]:checked').length) {
                    val = jQuery('[name=pro_plan]:checked').val();
                    jQuery("[data-value=plan" + val + "]").attr("name", "pro_years[]");
                }

                disableSelectedOption('.pro-years', '.basic-years');

                $amountCal = amountCal();
                $totalCost = 'POA';

                if ($amountCal != 0) {
                    $totalCost = "$" + $amountCal + ' + GST'
                }
                jQuery('#totalCost').text($totalCost);

                // jQuery('#totalCost').text("$" + amountCal() + ' + GST');

                function amountCal() {
                    var basic = pro = p_basic = p_pro = 0;

                    if (jQuery('input[name=primary_section]').is(":checked")) {
                        return 0

                        if (jQuery('input[name=primary_basic_plan]:checked').val()) {
                            p_basic = jQuery('input[name=primary_basic_plan]:checked').siblings('.cost').val();
                        }
                        if (jQuery('input[name=primary_pro_plan]:checked').val()) {
                            p_pro = jQuery('input[name=primary_pro_plan]:checked').siblings('.cost').val()
                        }
                    }

                    if (jQuery('input[name=secondary_section]').is(":checked")) {

                        if (jQuery('input[name=basic_plan]:checked').val()) {
                            basic = jQuery('input[name=basic_plan]:checked').siblings('.cost').val();
                        }
                        if (jQuery('input[name=pro_plan]:checked').val()) {
                            pro = jQuery('input[name=pro_plan]:checked').siblings('.cost').val();
                        }
                    }
                    if (parseInt(basic) != 0 && parseInt(pro) != 0) {
                        return parseInt(basic) + parseInt(pro);
                    }
                    return 0
                }

                jQuery('.require-one-edit').change(function(e) {
                    showPlan();

                    $amountCal = amountCal();
                    $totalCost = 'POA';

                    if ($amountCal != 0) {
                        $totalCost = "$" + $amountCal + ' + GST'
                    }
                    jQuery('#totalCost').text($totalCost);

                });

                function disableSelectedOption(current, other) {
                    selected = [];
                    jQuery(current).each(function() {
                        val = jQuery(this).val();
                        jQuery.each(val, function(i, v) {
                            selected.push(v);
                        });
                    })

                    jQuery(other + '>option').prop('disabled', false);
                    jQuery.each(selected, function(i, v) {
                        jQuery(other + '>option[value=' + v + ']').prop('disabled', true);
                    });
                    jQuery(other).select2({
                        closeOnSelect: false
                    });

                    jQuery(current + ',' + other).each(function() {
                        $this = jQuery(this);
                        if ($this.val().length != 0) {
                            $this.nextAll('.select2-container').find('.select2-selection').addClass(
                                'hide-caret');
                        } else {
                            $this.nextAll('.select2-container').find('.select2-selection').removeClass(
                                'hide-caret');
                        }
                    })
                }

                function onPlanSelect(selected, $this, primary = '') {
                    var val = $this.val();
                    var checked = $this.is(':checked');
                    jQuery('.' + selected + '-years').nextAll('.select2-container').find('.select2-selection')
                        .removeClass('hide-caret')
                    jQuery("input[name=" + selected + "_plan]").prop('checked', false);
                    jQuery("[name='" + selected + "_years[]']").removeAttr('name');
                    jQuery("." + selected + "-years:not([data-value=" + primary + "plan" + val + "])").val('').trigger(
                        'change.select2');
                    if (checked) {
                        jQuery("[data-value=" + primary + "plan" + val + "]").attr("name", selected + "_years[]");
                        $this.prop('checked', true);
                    } else {
                        jQuery("." + selected + "-years").val('').trigger('change.select2');
                    }
                    $amountCal = amountCal();
                    $totalCost = 'POA';

                    if ($amountCal != 0) {
                        $totalCost = "$" + $amountCal + ' + GST'
                    }
                    jQuery('#totalCost').text($totalCost);

                    // jQuery('#totalCost').text("$" + amountCal() + ' + GST');
                }

                //primary
                jQuery(document).on('change', '.primary_basic-years', function() {
                    $this = jQuery(this);

                    if ($this.val().length != 0) {
                        $this.parent().next('td').find('input[name=primary_basic_plan]').prop('checked', true);
                    } else {
                        $this.parent().next('td').find('input[name=primary_basic_plan]').prop('checked', false);
                    }

                    onPlanSelect('primary_basic', $this.parent().next('td').find(
                        'input[name=primary_basic_plan]'), 'primary');
                    disableSelectedOption('.primary_basic-years', '.primary_pro-years');
                });

                jQuery(document).on('change', '.primary_pro-years', function() {
                    $this = jQuery(this);

                    if ($this.val().length != 0) {
                        $this.parent().next('td').find('input[name=primary_pro_plan]').prop('checked', true);
                    } else {
                        $this.parent().next('td').find('input[name=primary_pro_plan]').prop('checked', false);
                    }
                    onPlanSelect('primary_pro', $this.parent().next('td').find('input[name=primary_pro_plan]'),
                        'primary');
                    disableSelectedOption('.primary_pro-years', '.primary_basic-years');
                });

                jQuery("input[name=primary_basic_plan]").change(function() {
                    onPlanSelect('primary_basic', jQuery(this), 'primary');
                    disableSelectedOption('.primary_basic-years', '.primary_pro-years');
                });

                jQuery("input[name=primary_pro_plan]").change(function() {
                    onPlanSelect('primary_pro', jQuery(this), 'primary');
                    disableSelectedOption('.primary_pro-years', '.primary_basic-years');
                });



                // Seconday

                jQuery(document).on('change', '.basic-years', function() {
                    $this = jQuery(this);

                    if ($this.val().length != 0) {
                        $this.parent().next('td').find('input[name=basic_plan]').prop('checked', true);
                    } else {
                        $this.parent().next('td').find('input[name=basic_plan]').prop('checked', false);
                    }

                    onPlanSelect('basic', $this.parent().next('td').find('input[name=basic_plan]'));
                    disableSelectedOption('.basic-years', '.pro-years');
                });

                jQuery(document).on('change', '.pro-years', function() {
                    $this = jQuery(this);

                    if ($this.val().length != 0) {
                        $this.parent().next('td').find('input[name=pro_plan]').prop('checked', true);
                    } else {
                        $this.parent().next('td').find('input[name=pro_plan]').prop('checked', false);
                    }
                    onPlanSelect('pro', $this.parent().next('td').find('input[name=pro_plan]'));
                    disableSelectedOption('.pro-years', '.basic-years');
                });

                jQuery("input[name=basic_plan]").change(function() {
                    onPlanSelect('basic', jQuery(this));
                    disableSelectedOption('.basic-years', '.pro-years');
                });

                jQuery("input[name=pro_plan]").change(function() {
                    onPlanSelect('pro', jQuery(this));
                    disableSelectedOption('.pro-years', '.basic-years');
                });


                $('[data-toggle="collapsible"]').click(function() {
                    if ($(this).parent().next(".child-menu").is(':visible')) {
                        $(this).parent().next('.child-menu').hide(300);
                        // $(".plusminus" + $(this).children().children().attr("id")).text('+');
                    } else {
                        $(this).parent().next('.child-menu').show(300);
                        // $(".plusminus" + $(this).children().children().attr("id")).text('- ');
                    }
                    $(this).toggleClass('shown');
                });

                jQuery(document).on('click', '.menu-table input[type=checkbox]:not(.checkAll)', function() {
                    toggleAllCheckbox($(this));
                });

                jQuery(".menu-table .checkAll").each(function(index, element) {
                    toggleAllCheckbox($(this));
                });


                $(".menu-table .checkAll").on("change", function() {
                    const isChecked = $(this).prop("checked");
                    $(this).closest("tr").find("input[type='checkbox']").prop("checked", isChecked);
                });


                $.validator.addMethod('require-one-edit', function(value) {
                    if (jQuery('#institute_type').val() === 'school') {
                        return $('.require-one-edit:checked').length > 0;
                    }
                    return true;
                }, 'Please check at least one box.');

                var checkboxes = $('.require-one-edit');
                var checkbox_names = $.map(checkboxes, function(e, i) {
                    return $(e).attr("name")
                }).join(" ");

                jQuery('#verified_domains').select2({
                    tags: true,
                    placeholder: {
                        id: '',
                        text: 'Type a domain like demo.com and press Enter...'
                    },
                    allowClear: true,
                    tokenSeparators: [',', ' '],
                    createTag: function(params) {
                        var term = jQuery.trim(params.term);
                        var domainRegex =
                            /^(?!:\/\/)([a-zA-Z0-9-_]+\.)*[a-zA-Z0-9][a-zA-Z0-9-_]+\.[a-zA-Z]{2,11}?$/;
                        if (term === '' || !domainRegex.test(term)) {
                            return null;
                        }
                        return {
                            id: term,
                            text: term,
                            newTag: true
                        };
                    }
                });

                jQuery('#verified_domains').on('select2:selecting', function (e) {
                    if (e.params.args.originalEvent && e.params.args.originalEvent.keyCode === 13) {
                        e.preventDefault(); // Prevent form submission
                    }
                });

                jQuery("#order-add").validate({
                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length || element.prop('type') === 'checkbox' ||
                            element.prop('type') === 'radio') {
                            if (element.attr('name') == 'basic_plan' || element.attr('name') ==
                                'pro_plan' || element.attr('name') == 'primary_basic_plan' || element.attr(
                                    'name') == 'primary_pro_plan') {
                                error.insertBefore(element.closest('table'));
                            } else {
                                error.insertBefore(element.parent());
                            }
                        } else if (element.hasClass('select2-hidden-accessible')) {
                            element.closest('table').find('thead th:nth-child(4)').append(error);

                        } else {
                            error.insertAfter(element);
                        }
                    },

                    ignore: [],
                    rules: {
                        school_name: {
                            required: true,
                            remote: {
                                url: "/checkSchoolNameOnUpdate",
                                type: "get",
                                data: {
                                    'id': function() {
                                        return jQuery('input[name=id]').val();
                                    },
                                    'school_name': function() {
                                        return jQuery('input[name=school_name]').val();
                                    }
                                }
                            }
                        },
                        country:'required',
                        state_id: {
                        // new logic for states - START
                            required: function() {
                                // Count the number of state options (excluding the empty option)
                                var stateOptions = jQuery('select[name=state_id] option').filter(function() {
                                    return jQuery(this).val() !== '';
                                });
                                var isRequired = stateOptions.length > 0;
                                // Show/hide the alert message under the state field
                                if (isRequired) {
                                    jQuery('#state-required-alert').show();
                                } else {
                                    jQuery('#state-required-alert').hide();
                                }
                                return isRequired;
                            }
                        // new logic for states - END
                        },
                        postcode: 'required',
                        contactperson: 'required',
                        position: 'required',
                        email: {
                            required: true,
                            email: true,
                        },
                        phone: 'required',
                        password: {
                            required: function() {
                                return (jQuery('input[name=confirm]').is(":checked"));
                            }
                        },
                        // confirm_password: {
                        //     equalTo: "#password",
                        // },
                        primary_basic_plan: {
                            required: function() {
                                return (jQuery('input[name=primary_section]').is(":checked") && !(jQuery(
                                    'input[name=primary_pro_plan]').is(":checked")));
                            }
                        },
                        primary_pro_plan: {
                            required: function() {
                                return (jQuery('input[name=primary_section]').is(":checked") && !(jQuery(
                                    'input[name=primary_basic_plan]').is(":checked")));
                            }
                        },
                        'primary_basic_years[]': {
                            required: function() {
                                return jQuery('input[name=primary_section]').is(":checked");
                            }
                        },
                        'primary_pro_years[]': {
                            required: function() {
                                return jQuery('input[name=primary_section]').is(":checked");
                            }
                        },
                        basic_plan: {
                            required: function() {
                                return (jQuery('input[name=secondary_section]').is(":checked") && !(jQuery(
                                    'input[name=pro_plan]').is(":checked")));
                            }
                        },
                        pro_plan: {
                            required: function() {
                                return (jQuery('input[name=secondary_section]').is(":checked") && !(jQuery(
                                    'input[name=basic_plan]').is(":checked")));
                            }
                        },
                        'basic_years[]': {
                            required: function() {
                                return jQuery('input[name=secondary_section]').is(":checked");
                            }
                        },
                        'pro_years[]': {
                            required: function() {
                                return jQuery('input[name=secondary_section]').is(":checked");
                            }
                        },
                        'verified_domains[]': {
                            required: function() {
                                return jQuery('#institute_type').val() === 'tertiary';
                            }
                        },
                    },
                    groups: {
                        checks: checkbox_names
                    },
                    messages: {
                        school_name: {
                            remote: 'This school is already registered!'
                        },
                        primary_basic_plan: {
                            required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                        },
                        primary_pro_plan: {
                            required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                        },
                        'primary_basic_years[]': {
                            required: 'Please select the years',
                        },
                        'primary_pro_years[]': {
                            required: 'Please select the years',
                        },
                        basic_plan: {
                            required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                        },
                        pro_plan: {
                            required: 'Please select at least one plan from STANDARD or from PREMIUM.',
                        },
                        'basic_years[]': {
                            required: 'Please select the years',
                        },
                        'pro_years[]': {
                            required: 'Please select the years',
                        }
                    },
                    invalidHandler: function(event, validator) {
                        // Show/hide country alert
                        if (validator.errorMap && validator.errorMap.country) {
                            jQuery('#country-required-alert').show();
                        } else {
                            jQuery('#country-required-alert').hide();
                        }
                        // Show/hide state alert (for safety)
                        if (validator.errorMap && validator.errorMap.state_id) {
                            jQuery('#state-required-alert').show();
                        } else {
                            jQuery('#state-required-alert').hide();
                        }
                    },
                });
                jQuery(document).on('click', '#removeLogo', function() {
                    var answer = confirm('Are you sure you want to remove this?');
                    if (answer) {
                        jQuery('#LogoCol').remove();
                        jQuery('#current-logopath').val('');
                    } else {
                        //do nothing
                    }
                });
            });

            function toggleAllCheckbox($elem) {
                if ($elem.closest("tr").find("input[type='checkbox']:not(.checkAll):checked").length == $elem.closest("tr")
                    .find("input[type='checkbox']:not(.checkAll)").length) {
                    $elem.closest("tr").find('.checkAll').prop('checked', true);
                } else {
                    $elem.closest("tr").find('.checkAll').prop('checked', false);
                }
            }

            jQuery(document).ready(function() {
                jQuery('#institute_type').on('change', function() {
                    const selectedType = jQuery(this).val();

                    if (selectedType === 'tertiary') {
                        jQuery('#primary_section').prop('checked', false);
                        jQuery('#secondary_section').prop('checked', false);
                        jQuery('#campuses').hide('fast');
                        jQuery('#secondaryPlan').hide('fast');
                        jQuery('#primaryPlan').hide('fast');
                        jQuery('#schoolPlans').addClass('d-none');
                        jQuery('#tertiaryPlans').removeClass('d-none');
                        jQuery('#verified_domains').closest('.col-md-4').removeClass('d-none');
                        jQuery('#privacy_link').closest('.col-md-4').removeClass('d-none');
                        jQuery('#new_logo').closest('.col-md-12').removeClass('col-md-12').addClass('col-md-4');

                    } else {
                        jQuery('#campuses').show('fast');
                        jQuery('#schoolPlans').removeClass('d-none');
                        jQuery('#tertiaryPlans').addClass('d-none');
                        jQuery('#verified_domains').closest('.col-md-4').addClass('d-none');
                        jQuery('#privacy_link').closest('.col-md-4').addClass('d-none');
                        jQuery('#new_logo').closest('.col-md-4').removeClass('col-md-4').addClass('col-md-12');
                    }

                });
                jQuery('#order-add').on('submit', function() {
                    return jQuery('#order-add').valid();
                });

                // jQuery('#institute_type').trigger('change');
            });

            
            jQuery('#country-required-alert').hide();
            jQuery('#state-required-alert').hide();

            // Handle country change to fetch states dynamically
            jQuery("#order-add select[name=country]").change(function() {
            // Hide country alert on change if value is selected
            jQuery(this).on('change', function() {
                if (jQuery(this).val()) {
                    jQuery('#country-required-alert').hide();
                }
            });
                var countryId = jQuery(this).val();
                // if (!countryId) {
                //     jQuery("#order-add select[name=state]").html('<option value=""></option>');
                //     return;
                // }

                // New logic for states - START
                    jQuery("#order-add select[name=state_id]").html('<option value=""></option>');
                    if (!countryId) {
                        jQuery("#school-state-container").hide();
                        return;
                    }
                // New logic for states - END

                jQuery.ajax({
                    type: "get",
                    url: "{{ route('getStates') }}",
                    data: {
                        id: countryId,
                    },
                    success: function(response) {
                        jQuery("select[name=state_id]").html('<option selected disabled></option>');
                        // New logic for states - START
                        if (!response || response.length == 0) {
                            jQuery("#school-state-container").hide();
                            return;
                        }
                        jQuery("#school-state-container").show();
                        // New logic for states - END

                        jQuery("#order-add select[name=state_id]").html('<option value=""></option>');
                        jQuery.each(response, function(i, v) {
                            // var selected = v.id == selectedStateId ? 'selected' : '';
                            jQuery("#order-add select[name=state_id]").append('<option value="' + v.id + '" ' + '>' + v.name + '</option>');
                        });
                        jQuery("#order-add select[name=state_id]").trigger('change.select2');

                        // Clear selectedStateId after use
                        // selectedStateId = null;
                        },
                        fail: function() {
                             // New logic for states - START
                                jQuery("#school-state-container").hide();
                             // New logic for states - END
                            jQuery("#order-add select[name=state_id]").html('<option value=""></option>');
                        }
                });
            });

            $(document).ready(function() {
                $('select[name="country"]').select2({
                    placeholder: "Select a country",
                });

                $('#select2-country-1m-results li').each(function() {
                    if ($(this).text().trim() === '' || $(this).attr('aria-selected') === 'true') {
                        $(this).attr('aria-selected', 'false');
                    }
                });

                $('select[name="state_id"]').select2({
                    placeholder: "Select a state",
                 });

                $('#select2-state_id-results li').each(function() {
                    if ($(this).text().trim() === '' || $(this).attr('aria-selected') === 'true') {
                        $(this).attr('aria-selected', 'false');
                    }
                });   
            });
        </script>
    @endpush
@endsection
