<template>
    <div class="w-100">
        <div class="mb-10 text-center">
            <p class="fs-2x fw-bold p-4 m-0 text-dark">Your Details</p>
            <p class="text-gray-400 fw-semobold fs-6">
                Enter your details below to finalise your account.
            </p>
        </div>
        <div class="row pb-3">
            <div class="col-12">
                <div class="form-floating text-muted" >
                    <!-- <Field id="email" class="form-control form-control-lg bg-none rounded-0" type="email" placeholder="Your email" name="email"  autocomplete="off" v-slot="{ handleChange }" /> -->
                    <Field name="email" v-slot="{ field }">
                        <input id="email" type="email" class="form-control form-control-lg rounded-0" placeholder="Enter you school's password" @change="() => false" v-model="field.value" />
                    </Field>
                    <label for="email">Your email</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="email" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row py-3">
            <div class="col-6">
                <div class="form-floating">
                    <Field id="fname" class="form-control form-control-lg bg-none rounded-0" type="text" placeholder="First Name" name="firstname" autocomplete="off" />
                    <label for="fname">First Name</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="firstname" />
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="form-floating">
                    <Field id="lname" class="form-control form-control-lg bg-none rounded-0" type="text" placeholder="Last Name" name="lastname" autocomplete="off" />
                    <label for="lname">Last Name</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="lastname" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row py-3">
            <div class="col-12">
                <div class="form-floating">
                    <Field id="password" class="form-control form-control-lg bg-none rounded-0" type="password" placeholder="Password" name="password" autocomplete="off" />
                    <label for="password">Password</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="password" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row py-3">
            <div class="col-12">
                <div class="form-floating">
                    <Field id="confirm_password" class="form-control form-control-lg bg-none rounded-0" type="password" placeholder="Confirm Password" name="confirm_password" autocomplete="off" />
                    <label for="confirm_password">Confirm Password</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="confirm_password" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row fv-row py-3">
            <div class="col-xl-12">
                <Field type="text" name="country" v-slot="{ field }">
                    <Multiselect
                        class="rounded-0  form-control"
                        v-bind="field"
                        :searchable="true"
                        placeholder="Country"
                        noOptionsText="Type a character to search your country"
                        :resolve-on-load="false"
                        :options="countrieslist"
                        v-model="formData.country"
                    ></Multiselect>
                </Field>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="country" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row pt-3">
            <div class="col-6" v-if="showStates">
                <Field type="text" name="state" v-slot="{ field }">
                    <Multiselect class="rounded-0  form-control" v-bind="field" :searchable="false" placeholder="State" noOptionsText="Type a character to search your state" :resolve-on-load="false" :options="stateslist" @change="() => false"></Multiselect>
                </Field>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="state" />
                    </div>
                </div>
            </div>
            <div :class="showStates ? 'col-xl-6' : 'col-xl-12'">
                <div class="form-floating">
                    <Field id="postcode" class="form-control form-control-lg bg-none rounded-0" type="text" placeholder="Postcode" name="postcode" autocomplete="off" />
                    <label for="state">Postcode</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="postcode" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
    import { defineComponent, ref, onMounted, nextTick, computed, watch } from "vue";
    import { Field, ErrorMessage, configure } from "vee-validate";
    import Multiselect from '@vueform/multiselect';
    export default defineComponent({
        name: "step-2",
        components: {
            Field,
            ErrorMessage,
            Multiselect
        },
        props: ['formData'],
        setup(props) {

            onMounted(() => {
                fetchCountries();
                fetchStates();
                // fetchYears();
            });
            const stateslist = ref();
            // const yearslist = ref();
            const genderlist = [
                { value: "M", label: "Male" },
                { value: "F", label: "Female" },
                { value: "other", label: "Other" }
            ]
            const fetchStates = async () => {
                const response = await fetch(
                    'states' + (props.formData.country ? `?country_id=${props.formData.country}` : ''),
                    {
                    }
                );

                const data = await response.json();

                if (!data || data.length === 0) {
                    props.formData.state = '';
                }

                stateslist.value = data.map((item) => {
                    return { value: item.id, label: item.name }
                });
            };
            // const fetchYears = async () => {
            //     const response = await fetch(
            //         'years',
            //         {
            //         }
            //     );

            //     const data = await response.json();
            //     yearslist.value = data.map((item) => {
            //         return { value: item.id, label: item.title }
            //     });
            // };

            const countrieslist = ref([]);
            const fetchCountries = async () => {
                const response = await fetch('/api/countries');

                const { data } = await response.json();
                
                countrieslist.value = data.map((item: {id: number, name: string}) => {
                    return { value: item.id, label: item.name }
                });
            };
            const handleCountryChange = (selectedCountry) => {
                fetchStates();
            };
            const showStates = computed(() => {
                return stateslist.value && stateslist.value?.length > 0 && props.formData?.country;
            });
            watch(() => props.formData.country, (newValue) => {
                if (newValue) {
                    handleCountryChange(newValue);
                }
            });


            return {
                fetchStates,
                stateslist,
                // yearslist,
                genderlist,
                countrieslist,
                showStates,

            }
        }
    });
</script>
<style>
    .multiselect-clear-icon {
        display: none;
    }

    @media only screen and (max-width: 600px) {}
</style>