@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
@section('breadcrumbs', Breadcrumbs::render('staff'))
@section('content')
    <style>
        #staff-table th {
            font-family: '<PERSON>', sans-serif !important;
            letter-spacing: 2px;
            font-size: 16px;
            color: #000;
        }

        #staff-table .fa.fa-eye {
            color: #000;
        }

        .tooltip-inner {
            text-align: left;
        }
    </style>
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"></a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Search
                    </div>
                </div>
                <div class="card-block">
                    <form class="" action="" method="post" id="search-form">
                        {{ csrf_field() }}
                        <div class="row clearfix">
                            <div class="col-md-3">
                                <div class="form-group form-group-default">
                                    <label>Staff name</label>
                                    <input type="text" class="form-control" name="name" />
                                </div>
                            </div>

                            @if ($campuses->count() > 1)
                                <div class="col-md-3">
                                    <div class="form-group form-group-default form-group-default-select2">
                                        <label>Campuses</label>
                                        <select class="full-width" name="campus" data-init-plugin="select2">
                                            <option value="" selected>Any</option>
                                            @foreach ($campuses as $campus)
                                                <option value="{{ $campus->id }}"> {{ $campus->name }} </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-12 text-right">
                                <br>
                                <button class="btn btn-primary" type="submit">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card card-default">
                <div class="card-header  separator">
                    <div class="card-title">
                        Staff
                    </div>
                    <div class="card-controls">
                        <ul>
                            <li>
                                {{-- <button data-target="#modalTeachersImport" data-toggle="modal" id="btnTeachersImport"
                            class="btn btn-primary btn-cons">
                            <i class="fa fa-upload"></i> Bulk Import
                        </button> --}}
                            </li>
                            <li>
                                <button data-target="#modalStaffAdd" data-toggle="modal" id="btnFillSizeToggler" class="btn btn-primary btn-cons">
                                    <i class="fa fa-plus"></i> Add New
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-block">
                    <table class="table table-hover custom-datatable no-footer" id="staff-table">
                        <thead>
                            <tr>
                                <th>First Name</th>
                                <th>Last Name</th>
                                <th>Email</th>
                                <th>Access</th>
                                <th>Country</th>
                                <th>State</th>
                                @if (Auth::user()->organisation->campuses()->exists())
                                    <th>Campuses</th>
                                @endif
                                @if (Auth::user()->isAdmin())
                                    <th>Position</th>
                                @endif
                                <th>Registration Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @push('modals')
        @include ('staff.modals.add')
        @include ('staff.modals.edit')
        {{-- @include ('staff.modals.import') --}}
    @endpush
    @push('scripts')
        <script>
            jQuery(document).ready(function() {
                $.fn.dataTable.ext.errMode = 'none';
                var oTable = $('#staff-table').DataTable({
                    "order": [
                        [0, "asc"]
                    ],
                    processing: true,
                    serverSide: true,
                    stateSave: true,
                    dom: "<'row'<'col-md-12'lB>>" + "<'table-responsive'rt><'row'<'col-md-12'pi>>",
                    "autoWidth": false,
                    "lengthMenu": [
                        [10, 25, 50, 100 /* , -1 */ ],
                        [10, 25, 50, 100 /* , "All" */ ]
                    ],
                    buttons: [{
                        extend: 'csv',
                        text: '<i class="fa fa-download"></i> CSV',
                        exportOptions: {
                            columns: 'th:not(:last-child)'
                        }
                    }],
                    ajax: {
                        url: 'staff/getdata',
                        data: function(d) {
                            d.name = $('input[name=name]').val();
                            d.position = $('select[name=position]').val();
                            d.campus = $('select[name=campus]').val();
                            return d;
                        }
                    },
                    columns: [{
                            data: 'firstname',
                            name: 'firstname'
                        },
                        {
                            data: 'lastname',
                            name: 'lastname'
                        },
                        {
                            data: 'email',
                        },
                        {
                            data: 'access_level',
                        },
                        {
                            // data: 'state.country.code',
                            data: 'country_name'
                        },
                        {
                            data: 'state.code',
                        },
                        @if (Auth::user()->organisation->campuses()->exists())
                            {
                                data: 'campuses',
                            },
                        @endif
                        @if (Auth::user()->isAdmin())
                            {
                                data: 'positions.0.name',
                                orderable: false,
                            },
                        @endif {
                            data: 'registration_date',
                        },
                        {
                            data: 'action',
                            searchable: false,
                            orderable: false,
                        },
                    ],
                    "drawCallback": function(settings) {
                        laravel.initialize();
                    }
                });
                $('#search-form').on('submit', function(e) {
                    oTable.draw();
                    e.preventDefault();
                });
            });
        </script>
    @endpush
@endsection
