<?php

namespace App\Http\Controllers\Auth;

use App\ChildInvitee;
use App\ChildParent;
use App\Http\Controllers\Controller;
use App\IndividualStudent;
use App\Jobs\ProcessChildInvitation;
use App\Jobs\ProcessChildRegistration;
use App\ParentInvitee;
use App\Role;
use App\Standard;
use App\Stage;
use App\User;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Mail;
use Stripe\Stripe;
use Stripe\Coupon;
use App\Country;
use App\Events\LicenseRenewed;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;


use DB;
use App\Licence;
use App\Profile;
use Carbon\Carbon;
use Success;

class ParentChildLicenseController extends Controller
{

    public function parentBuyLicense($token = '')
    {
        $intent = $invitation = false;
        $countries = Country::All();
        if ($token) {
            $invitation = ParentInvitee::whereToken($token)->with('child:id,email,role_id,school_id,organisation_id', 'child.profile:user_id,firstname,lastname')->first(); //'child:id,email,role_id,school_id,organisation_id' this columns are used to find active licence
            if ($invitation) {
                $parent = User::whereEmail($invitation->email)->first();
                if ($parent) {
                    if (Auth::check()) {
                        if ($parent->email != Auth::user()->email) {
                            return redirect()->route('profile-edit')->with('message', 'You have to log out from this account and log in using the email address you got the invitation on.');
                        } elseif (!$parent->isParent()) {
                            return redirect()->route('profile-edit')->with('message', "Sorry! You can't use the account to purchase the child licence as this is not a parent account.");
                        }
                        return redirect('/profiles/edit#children')->with('message', "You can connect your account with your child from the 'CHILDREN' section below.");
                    }
                    session(['link' => '/profiles/edit#children', 'prevId' => $parent->id]);
                    return redirect('/login')->with('message', "Please login with the parent account to continue!");
                }
            }
        }
        return view('auth.license.parentlicense', compact('invitation', 'countries', 'intent', "token"));
    }

    public function validateCoupon($coupon)
    {
        dd($coupon);
        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            return Coupon::retrieve($coupon);
            // return true;
        } catch (Exception $e) {
            return $e;
        };
    }

    public function storeParentLicense(Request $request) // On new parent license when there is no inactive child
    {
        DB::beginTransaction();

        try {
            $parentRoleId = Role::where('name', "Parent")->value('id');

            $child_emails = array_filter($request->child_emails);

            if ($child_emails) {
                if (User::whereIn('email', $request->child_emails)->exists()) {
                    $parent = ChildParent::create([
                        'name' => $request->fname . " " . $request->lname,
                        'email' => $request->email,
                        'password' => bcrypt($request->password),
                        'state_id' => $request->state,
                        'country_id' => $request->country,
                        'postcode' => $request->postcode,
                        'role_id' => $parentRoleId,
                    ]);
                    if ($parent) {
                        $parent->profile()->create([
                            'firstname' => $request->fname,
                            'lastname' => $request->lname,
                            'accountcreated' => true,
                        ]);



                        // User::addHelpcrunchAccount($parent->id);
                        // User::updateUserMailchimpDetail($parent->id);
                        // $user = User::find($parent->id);
                        // $pm = $user->paymentMethods();
                        // if ($pm) {
                        //     $user->addPaymentMethod($pm->first()->id);
                        //     $card = $user->paymentMethods()->first()->card;
                        //     $user->card_last_four = $card->last4;
                        //     $user->card_brand = $card->brand;
                        //     $user->save();
                        // }
                        // $fnames = $request->child_fnames;
                        // $lnames = $request->child_lnames;
                        $emails = $request->child_emails;
                        $children = [];
                        if ($emails) {
                            foreach ($emails as $key => $email) {
                                if ($email) {
                                    $child = User::where('email', $email)->first();
                                    if ($child && $child->activeLicense()) {
                                        $children[] = $child->name;
                                        $child_id = $child->id;

                                        $processed = false;
                                        if (isset($request->child_invitation[$key])) {
                                            ParentInvitee::whereToken($request->child_invitation[$key])->update([
                                                'processed' => '1',
                                            ]);

                                            $processed = true;
                                            $parent->children()->syncWithoutDetaching($child_id);
                                            $parentprofile = $parent->profile;
                                            $parentprofile->premium_access = true;
                                            $parentprofile->save();
                                        } elseif ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child->id)->first()) {
                                            $parentInvitation->processed = '1';
                                            $parentInvitation->save();

                                            $processed = true;
                                            $parent->children()->syncWithoutDetaching($child->id);
                                        }
                                        $invitation = ChildInvitee::firstOrCreate(
                                            [
                                                'parent_id' => $parent->id,
                                                'child_id' => $child_id,
                                            ],
                                            [
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => $processed ? '1' : '0',
                                            ]
                                        );

                                        if (!$processed) {
                                            dispatch(new ProcessChildInvitation($invitation));
                                        }
                                    }
                                }
                            }
                        }
                    }

                    \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
                    DB::commit();

                    $data = [];
                    if (count($children) == 1) {
                        $data = ['child' => $children[0]];
                    }

                    Mail::send('emails.parentaccountcreated', $data, function ($message) use ($parent) {
                        $message->to($parent->email)->subject('Your account is ready! Log in now');
                    });

                    if (Auth::attempt(['email' => $parent->email, 'password' => $request->password])) {
                        return redirect('/home')->with('confirmation', "Your child has been emailed their next steps!");
                    }
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex);
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function storeChildren(Request $request) //request from parent profile page when there is no inactive child
    {
        DB::beginTransaction();

        try {
            $cart = $request->session()->get('cart.parentlicense');
            $request->session()->forget('cart.parentlicense');
            $form = unserialize($cart['parentlicense']);
            $child_emails = array_column($form['children'], 'email');

            if ($child_emails) {
                if (User::whereIn('email', $child_emails)->exists()) {
                    $parent = ChildParent::findOrFail(Auth::id());
                    if ($parent) {
                        $emails = $child_emails;
                        // $children = [];
                        if ($emails) {
                            foreach ($emails as $key => $email) {
                                if ($email) {
                                    $child = User::where('email', $email)->first();
                                    if ($child && $child->activeLicense()) {
                                        $child_id = $child->id;
                                        $invitation = ChildInvitee::firstOrCreate(
                                            [
                                                'parent_id' => $parent->id,
                                                'child_id' => $child_id,
                                            ],
                                            [
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => '0',
                                            ]
                                        );

                                        dispatch(new ProcessChildInvitation($invitation));
                                    }
                                }
                            }
                        }
                    }

                    \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
                    DB::commit();

                    $redirectUrl = url()->previous();
                    if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                        $redirectUrl = session()->get('previousUrl');
                    }
                    return ['status' => 'success', 'message' => "Your child has been emailed their next steps!"];
                    // return redirect($redirectUrl)->with('message', "Your child's account has been connected to yours!");
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            // $redirectUrl = url()->previous();
            // if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            //     $redirectUrl = session()->get('previousUrl');
            // }
            // return redirect($redirectUrl)->with('message', "Whoops! Something went wrong. Please try again.");
            return ['status' => 'failed', 'error' => $ex->getMessage()];
        }
    }


    public function storeRenewparent(Request $request)
    {
        try {
            DB::beginTransaction();
            $parent = ChildParent::find(Auth::id());
            $old_ids = isset($form['child_ids']) ? $form['child_ids'] : [];
            $invitees = Auth::user()->childInvitees()->pluck('child_id');
            $removed = $invitees->diff($old_ids);
            if ($removed) {
                $parent->childInvitees()->whereIn('child_id', $removed)->delete();
                ParentInvitee::whereEmail($parent->email)->whereIn('child_id', $removed)->delete();
                $parent->children()->detach($removed);
                foreach ($removed as $id) {
                    Cache::forget('isChild' . $id);
                }
            }

            if (array_filter($request->child_emails)) {
                $fnames = $request->child_fnames;
                $lnames = $request->child_lnames;
                $emails = $request->child_emails;
                if ($emails) {
                    foreach ($emails as $key => $email) {
                        if ($email && $fnames[$key] && $lnames[$key]) {
                            $child = User::where('email', $email)->first();
                            if ($child && $child->activeLicense()) {
                                $child_id = $child->id;

                                $processed = false;
                                if ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child_id)->first()) {
                                    $parentInvitation->processed = '1';
                                    $parentInvitation->save();

                                    $processed = true;
                                    $parent->children()->syncWithoutDetaching($child_id);
                                }

                                $invitation = ChildInvitee::firstOrCreate(
                                    [
                                        'parent_id' => $parent->id,
                                        'child_id' => $child_id,
                                    ],
                                    [
                                        'token' => uniqid(Str::random(27)),
                                        'processed' => $processed ? '1' : '0',
                                    ]
                                );

                                if (!$processed) {
                                    dispatch(new ProcessChildInvitation($invitation));
                                }
                            }
                        }
                    }

                    // $user = User::find($parent->id);
                    // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                }

                \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));

                DB::commit();
                return redirect('home')->with('message', "Your Account is now active. You can access the site normally!");
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect('profiles/edit')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function stripepurchasechildlicensesession(Request $request)
    {

        $parentdata = serialize($request->all());
        // dd($parentdata);

        $success_url = Auth::check() ?  route('stripe.purchasechildsuccess') : route('stripe.parentchildsuccess');
        $parent_email = Auth::check() ?  Auth::user()->email : $request->email;

        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'customer_email' => $parent_email,
            'line_items' => [[
                'price' => config('services.stripe.child_key'),
                'quantity' => $request->licensecount,
            ]],
            'mode' => 'subscription',
            'success_url' => $success_url,
            'allow_promotion_codes' => true,
            'cancel_url' => route('stripe.childfailed'),
            'metadata' => [
                'child_id' => $request->child_id,
            ]
        ]);
        $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
        return $session;
    }
    public function stripepurchasechildsuccess(Request $request)
    {
        $cart = $request->session()->get('cart.parentlicense');
        $request->session()->forget('cart.parentlicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    $form = unserialize($cart['parentlicense']);
                    /*Payment successful*/
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();

                    // $parent = ChildParent::find(Auth::id());

                    // if ($sessiondetail->metadata->child_id) {
                    //     ParentInvitee::where('child_id', $request->child_id)->update([
                    //         'processed' => '1',
                    //     ]);

                    //     $parent->children()->attach($sessiondetail->metadata->child_id);
                    // }

                    $licence = new Licence();
                    $licence->stripe_id = $subscriptionid;
                    $licence->purchased_by = Auth::id();
                    $licence->assigned_to = $sessiondetail->metadata->child_id;
                    $licence->number = strtoupper(sha1(time()));
                    $licence->type = 'Child';
                    $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                    $licence->save();

                    \Event::dispatch(new LicenseRenewed(User::where("id", Auth::id())->first()));

                    DB::commit();
                    return redirect('parent-children')->with('message', "License has been purchased for your child!");
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect('parent-children')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function stripecheckoutparentsession(Request $request)
    {
        $parentdata = serialize($request->all());

        $success_url = Auth::check() ?  route('stripe.parentshildssuccess') : route('stripe.parentchildsuccess');
        $parent_email = Auth::check() ?  Auth::user()->email : $request->email;

        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'customer_email' => $parent_email,
            'line_items' => [[
                'price' => config('services.stripe.child_key'),
                'quantity' => $request->licensecount,
            ]],
            'mode' => 'subscription',
            'allow_promotion_codes' => true,
            'success_url' => $success_url,
            'cancel_url' => route('stripe.childfailed'),
        ]);
        $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
        return $session;
    }

    public function removechildlicense($id)
    {
        return  Auth::user()->removeChild($id);
        // dd($child);
        return 'successfully';
    }


    public function stripeparentchildsuccess(Request $request) //When purchasing new parent license
    {
        $cart = $request->session()->get('cart.parentlicense');
        $request->session()->forget('cart.parentlicense');

        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);

            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {

                    $stripe_id = $sessiondetail->customer;
                    $form = unserialize($cart['parentlicense']);
                    /*Payment successful*/
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();


                    $roleId = Role::where('name', "Individual Student")->value('id');

                    $parentRoleId = Role::where('name', "Parent")->value('id');

                    if (array_filter($form['children'])) {
                        $parent = ChildParent::create([
                            'name' => $form['firstname'] . " " . $form['lastname'],
                            'email' => $form['email'],
                            'password' => bcrypt($form['password']),
                            'state_id' => $form['state'] ?? null,
                            'country_id' => $form['country'] ?? null,
                            'postcode' => $form['postcode'],
                            'stripe_id' => $stripe_id,
                            'role_id' => $parentRoleId,
                        ]);
                        if ($parent) {
                            $user = User::find($parent->id);
                            $pm = $user->paymentMethods();
                            if ($pm) {
                                $user->addPaymentMethod($pm->first()->id);
                                $card = $user->paymentMethods()->first()->card;
                                $user->card_last_four = $card->last4;
                                $user->card_brand = $card->brand;
                                $user->save();
                            }

                            $parent->profile()->create([
                                'firstname' => $form['firstname'],
                                'lastname' => $form['lastname'],
                                'accountcreated' => true,
                                'premium_access' => true
                            ]);



                            // User::addHelpcrunchAccount($parent->id);
                            // User::updateUserMailchimpDetail($parent->id);
                            // if ($request->child_id) {
                            //     ParentInvitee::where('child_id', $request->child_id)->email()->update([
                            //         'processed' => '1',
                            //     ]);

                            //     $parent->children()->attach($request->child_id);
                            // }
                            // $fnames = $form['child_fnames'];
                            // $lnames = $form['child_lnames'];
                            // $emails = $form['child_emails'];
                            $child_invitation = [];
                            $quantity = 1;
                            $children = [];
                            if (!empty($form['children'])) {
                                foreach ($form['children'] as $key => $childdata) {
                                    if ($childdata['email']) {
                                        if (User::where('email', $childdata['email'])->exists()) {
                                            $child = User::where('email', $childdata['email'])->first();

                                            $children[] = $child->name;

                                            // $invitation = ChildInvitee::where('child_id', $child->id)->where('parent_id', Auth::id())->where('processed', 0)->first();
                                            // $invitation = ChildInvitee::create([
                                            //     'parent_id' => $parent->id,
                                            //     'child_id' => User::where('email', $email)->value('id'),
                                            //     'token' => uniqid(Str::random(27))
                                            //     'processed' => '0',
                                            // ]);
                                            if (isset($child_invitation[$key])) {
                                                ParentInvitee::whereToken($child_invitation[$key])->update([
                                                    'processed' => '1',
                                                ]);


                                                $invitation = ChildInvitee::firstOrCreate(
                                                    [
                                                        'parent_id' => $parent->id,
                                                        'child_id' => $child->id,
                                                    ],
                                                    [
                                                        'token' => uniqid(Str::random(27)),
                                                        'processed' => '1',
                                                    ]
                                                );

                                                $parent->children()->syncWithoutDetaching($child->id);
                                            } elseif ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child->id)->first()) {
                                                $parentInvitation->processed = '1';
                                                $parentInvitation->save();

                                                $invitation = ChildInvitee::firstOrCreate(
                                                    [
                                                        'parent_id' => $parent->id,
                                                        'child_id' => $child->id,
                                                    ],
                                                    [
                                                        'token' => uniqid(Str::random(27)),
                                                        'processed' => '1',
                                                    ]
                                                );

                                                $parent->children()->syncWithoutDetaching($child->id);
                                            } else {
                                                $invitation = ChildInvitee::create(
                                                    [
                                                        'parent_id' => $parent->id,
                                                        'child_id' => $child->id,
                                                    ],
                                                    [
                                                        'token' => uniqid(Str::random(27)),
                                                        'processed' => '0',
                                                    ]
                                                );

                                                dispatch(new ProcessChildInvitation($invitation));
                                            }

                                            if (!User::where('email', $childdata['email'])->first()->activeLicense()) {

                                                $licence = Licence::firstOrCreate(
                                                    ['assigned_to' => $child->id, 'type' => 'Child'],
                                                    [
                                                        'stripe_id' => $subscriptionid,
                                                        'purchased_by' => $parent->id,
                                                        'number' => strtoupper(sha1(microtime(true))),
                                                        'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                                    ]
                                                );
                                            }
                                        } else {
                                            $child = '';
                                            $child = IndividualStudent::create([
                                                'name' => "",
                                                'email' => ($childdata['email']) ? $childdata['email'] : uniqid(Str::random(27)),
                                                'password' => bcrypt(Str::random(10)),
                                                'role_id' => $roleId,
                                            ]);
                                            $children[] = $childdata['email'];
                                            $child->profile()->create([
                                                'firstname' => "",
                                                'lastname' => "",
                                            ]);

                                            if ($child) {
                                                $parent->children()->syncWithoutDetaching($child->id);

                                                $licence = new Licence();
                                                $licence->stripe_id = $subscriptionid;
                                                $licence->purchased_by = $parent->id;
                                                $licence->assigned_to = $child->id;
                                                $licence->number = strtoupper(sha1(microtime(true)));
                                                $licence->type = 'Child';
                                                $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                                                $licence->save();


                                                $invitation = ChildInvitee::create([
                                                    'parent_id' => $parent->id,
                                                    'child_id' => $child->id,
                                                    'token' => uniqid(Str::random(27)),
                                                    'processed' => '0',
                                                ]);
                                                dispatch(new ProcessChildRegistration($invitation));
                                            }
                                            $quantity++;
                                        }
                                    }
                                }

                                // $user = User::find($parent->id);
                                // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                            }
                        }

                        \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));

                        DB::commit();

                        $data = [];

                        if (count($children) == 1) {
                            $data = ['child' => $children[0]];
                        }

                        Mail::send('emails.parentaccountcreated', $data, function ($message) use ($parent) {
                            $message->to($parent->email)->subject('Account created successfully.');
                        });

                        if (Auth::attempt(['email' => $parent->email, 'password' => $form['password']])) {
                            return  redirect(route('landing') . '#/dashboard');
                        }
                    }
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return  redirect(route('landing') . '#/sign-in')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function stripecheckoutsession(Request $request)  //When purchasing new parent licence and adding more child from profile page
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $paid = array_filter($request->children, function ($child) {
            return $child['paid'];
        });
        $parentdata = serialize($request->all());

        if (count($paid)) {

            $success_url = Auth::check() ? /* Adding child from profile edit page */ route('stripe.childsuccess') : /* Buying  parent/child licence from frontend */ route('stripe.parentchildsuccess');
            $parent_email = Auth::check() ?  Auth::user()->email : $request->email;

            $session = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'subscription_data' => [
                    // 'trial_period_days' => 30,
                ],
                'customer_email' => $parent_email,
                'line_items' => [[
                    'price' => config('services.stripe.child_key'),
                    'quantity' => count($paid),
                ]],
                'mode' => 'subscription',
                'allow_promotion_codes' => true,
                'success_url' => $success_url,
                'cancel_url' => route('stripe.childfailed'),
            ]);

            $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
            return $session;
        } else {
            $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => false]);
            return $this->storeChildren($request);
            // $this->
        }
    }

    public function stripechildsuccess(Request $request)   //When purchased from parent profile edit
    {
        $cart = $request->session()->get('cart.parentlicense');
        $request->session()->forget('cart.parentlicense');
        $form = unserialize($cart['parentlicense']);
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    // Payment successful
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();
                    $roleId = Role::where('name', "Individual Student")->value('id');

                    $parent = ChildParent::find(Auth::id());
                    if (isset($sessiondetail->setup_intent)) {
                        Auth::user()->updateCardInfoFromIntent($sessiondetail->setup_intent);
                    }
                    foreach ($form['children'] as $child) {
                        $existingUser = User::select('id', 'role_id', 'school_id', 'organisation_id')->where('email', $child['email'])->with('profile:user_id,removed,is_active,accountcreated', 'license:assigned_to,valid_upto', 'school:id,role_id', 'school.detail:school_id,subscription_ending_on', 'organisation:id,role_id', 'organisation.detail:school_id,subscription_ending_on')->first();

                        if ($existingUser) {
                            if (!$existingUser->activeLicense()) {

                                $licence = Licence::firstOrCreate(
                                    ['assigned_to' => $existingUser->id, 'type' => 'Child'],
                                    [
                                        'stripe_id' => $subscriptionid,
                                        'purchased_by' => $parent->id,
                                        'number' => strtoupper(sha1(time())),
                                        'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                    ]
                                );
                            }
                            $invitation = ChildInvitee::firstOrCreate(
                                [
                                    'parent_id' => $parent->id,
                                    'child_id' => $existingUser->id,
                                ],
                                [
                                    'token' => uniqid(Str::random(27)),
                                    'processed' => '0',
                                ]
                            );

                            dispatch(new ProcessChildInvitation($invitation));
                        } else {
                            $individual = '';
                            $individual = IndividualStudent::create([
                                'email' => $child['email'],
                                'password' => bcrypt(Str::random(10)),
                                'role_id' => $roleId,
                            ]);

                            if ($individual) {
                                $profile = new Profile();
                                $individual->profile()->save($profile);

                                $parent->children()->syncWithoutDetaching($individual->id);

                                $licence = new Licence();
                                $licence->stripe_id = $subscriptionid;
                                $licence->purchased_by = $parent->id;
                                $licence->assigned_to = $individual->id;
                                $licence->number = strtoupper(sha1(time()));
                                $licence->type = 'Child';
                                $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                                $licence->save();


                                $invitation = ChildInvitee::create([
                                    'parent_id' => $parent->id,
                                    'child_id' => $individual->id,
                                    'token' => uniqid(Str::random(27)),
                                    'processed' => '0',
                                ]);
                                dispatch(new ProcessChildRegistration($invitation));
                            }
                        }

                        // $user = User::find($parent->id);
                        // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                    }

                    \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));

                    DB::commit();

                    return  redirect(route('landing') . '#/dashboard')->with('message', 'Your child has been emailed their next steps!');
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return  redirect(route('landing') . '#/dashboard')->with('error', "Woops! There was an error in the process. Please contact the site owner if the amount has been debited from your account.");
        }
    }

    /*  public function stripechildsuccess(Request $request)   //When purchased from parent profile edit
    {
        $cart = $request->session()->get('cart.parentlicense');
        $request->session()->forget('cart.parentlicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    $form = unserialize($cart['parentlicense']);
                    // Payment successful
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();
                    $roleId = Role::where('name', "Individual Student")->value('id');

                    if (array_filter($form['child_emails'])) {
                        $parent = ChildParent::find(Auth::id());
                        if (isset($sessiondetail->setup_intent)) {
                            Auth::user()->updateCardInfoFromIntent($sessiondetail->setup_intent);
                        }
                        $fnames = $form['child_fnames'];
                        $lnames = $form['child_lnames'];
                        $emails = $form['child_emails'];
                        $child_invitation = isset($form['invitationtoken']) ? $form['invitationtoken'] : [];
                        $children = [];
                        if ($emails) {
                            foreach ($emails as $key => $email) {
                                if ($email && $fnames[$key] && $lnames[$key]) {
                                    if (User::where('email', $email)->exists()) {
                                        $child_id = User::where('email', $email)->value('id');

                                        if (!User::where('email', $email)->first()->activeLicense()) {

                                            $licence = Licence::firstOrCreate(
                                                ['assigned_to' => $child_id, 'type' => 'Child'],
                                                [
                                                    'stripe_id' => $subscriptionid,
                                                    'purchased_by' => $parent->id,
                                                    'number' => strtoupper(sha1(time())),
                                                    'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                                ]
                                            );
                                        }

                                        if (isset($child_invitation[$key])) {
                                            ParentInvitee::whereToken($child_invitation[$key])->update([
                                                'processed' => '1',
                                            ]);

                                            $invitation = ChildInvitee::firstOrCreate(
                                                [
                                                    'parent_id' => $parent->id,
                                                    'child_id' => $child_id,
                                                ],
                                                [
                                                    'token' => uniqid(Str::random(27)),
                                                    'processed' => '1',
                                                ]
                                            );

                                            $parent->children()->syncWithoutDetaching($child_id);
                                        } else {
                                            $invitation = ChildInvitee::firstOrCreate(
                                                [
                                                    'parent_id' => $parent->id,
                                                    'child_id' => $child_id,
                                                ],
                                                [
                                                    'token' => uniqid(Str::random(27)),
                                                    'processed' => '0',
                                                ]
                                            );

                                            dispatch(new ProcessChildInvitation($invitation));
                                        }
                                    } else {
                                        $child = '';
                                        $child = IndividualStudent::create([
                                            'name' => $fnames[$key] . " " . $lnames[$key],
                                            'email' => ($email) ? $email : uniqid(Str::random(27)),
                                            'password' => bcrypt(Str::random(10)),
                                            'role_id' => $roleId,
                                        ]);
                                        $children[] = $child->id;
                                        $child->profile()->create([
                                            'firstname' => $fnames[$key],
                                            'lastname' => $lnames[$key],
                                        ]);

                                        if ($child) {
                                            $parent->children()->syncWithoutDetaching($child->id);

                                            $licence = new Licence();
                                            $licence->stripe_id = $subscriptionid;
                                            $licence->purchased_by = $parent->id;
                                            $licence->assigned_to = $child->id;
                                            $licence->number = strtoupper(sha1(time()));
                                            $licence->type = 'Child';
                                            $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                                            $licence->save();


                                            $invitation = ChildInvitee::create([
                                                'parent_id' => $parent->id,
                                                'child_id' => $child->id,
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => '0',
                                            ]);
                                            dispatch(new ProcessChildRegistration($invitation));
                                        }
                                    }
                                }
                            }

                            // $user = User::find($parent->id);
                            // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                        }

                        \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));

                        DB::commit();
                        return redirect('profiles/edit')->with('message', "Your child has been emailed their next steps!");
                    }
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect('profiles/edit')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    } */

    public function stripechildfailed()
    {
        $link = Auth::check() ? 'profiles/edit' : 'parent/buylicense';
        return redirect($link)->with('message', "Whoops! Something went wrong. Please try again.");
    }

    public function renewlicense()
    {
        if (Auth::check()) {
            $invitees = Auth::user()->childInvitees()->with('child', 'child.profile')->get();
            return view('auth.license.renewparentlicense', compact('invitees'));
        }
    }

    public function renewparentcheckout(Request $request)
    {
        $parentdata = serialize($request->all());

        $success_url =  route('stripe.renewparentsuccess');
        $parent_email = Auth::check() ?  Auth::user()->email : $request->email;
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'customer_email' => $parent_email,
            'line_items' => [[
                'price' => config('services.stripe.child_key'),
                'quantity' => $request->licensecount,
            ]],
            'mode' => 'subscription',
            'success_url' => $success_url,
            'allow_promotion_codes' => true,
            'cancel_url' => route('stripe.childfailed'),
        ]);
        $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
        return $session;
    }

    public function renewparentsuccess(Request $request)
    {
        $cart = $request->session()->get('cart.parentlicense');
        $request->session()->forget('cart.parentlicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail) {
                if ($sessiondetail->payment_status == "paid") {
                    $form = unserialize($cart['parentlicense']);
                    /*Payment successful*/
                    $subscriptionid = $sessiondetail->subscription;
                    DB::beginTransaction();
                    $roleId = Role::where('name', "Individual Student")->value('id');
                    $parent = ChildParent::find(Auth::id());
                    if (isset($sessiondetail->setup_intent)) {
                        Auth::user()->updateCardInfoFromIntent($sessiondetail->setup_intent);
                    }
                    $old_ids = isset($form['child_ids']) ? $form['child_ids'] : [];
                    $invitees = Auth::user()->childInvitees()->pluck('child_id');
                    $removed = $invitees->diff($old_ids);
                    if ($removed) {
                        $parent->childInvitees()->whereIn('child_id', $removed)->delete();
                        $parent->children()->detach($removed);
                        foreach ($removed as $id) {
                            Cache::forget('isChild' . $id);
                        }
                    }
                    if ($old_ids) {
                        foreach ($old_ids as $child_id) {
                            if (!User::find($child_id)->activeLicense()) {

                                $licence = Licence::firstOrCreate(
                                    ['assigned_to' => $child_id, 'purchased_by' => $parent->id, 'type' => 'Child'],
                                    [
                                        'stripe_id' => $subscriptionid,
                                        'number' => strtoupper(sha1(time())),
                                        'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                    ]
                                );
                                \Event::dispatch(new LicenseRenewed(User::where("id", $child_id)->first()));
                            }
                        }
                    }

                    if (isset($form['child_emails']) && array_filter($form['child_emails'])) {
                        $fnames = $form['child_fnames'];
                        $lnames = $form['child_lnames'];
                        $emails = $form['child_emails'];
                        $children = [];
                        if ($emails) {
                            foreach ($emails as $key => $email) {
                                if ($email && $fnames[$key] && $lnames[$key]) {
                                    if (User::where('email', $email)->exists()) {
                                        $child_id = User::where('email', $email)->value('id');

                                        $processed = false;
                                        if ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId($child_id)->first()) {
                                            $parentInvitation->processed = '1';
                                            $parentInvitation->save();

                                            $processed = true;
                                            $parent->children()->syncWithoutDetaching($child_id);
                                        }

                                        if (!User::where('email', $email)->first()->activeLicense()) {

                                            $licence = Licence::firstOrCreate(
                                                ['assigned_to' => $child_id, 'type' => 'Child'],
                                                [
                                                    'stripe_id' => $subscriptionid,
                                                    'purchased_by' => $parent->id,
                                                    'number' => strtoupper(sha1(time())),
                                                    'valid_upto' => Carbon::now()->addYears(1)->toDateString(),
                                                ]
                                            );
                                        }

                                        $invitation = ChildInvitee::firstOrCreate(
                                            [
                                                'parent_id' => $parent->id,
                                                'child_id' => $child_id,
                                            ],
                                            [
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => $processed ? '1' : '0',
                                            ]
                                        );

                                        if (!$processed) {
                                            dispatch(new ProcessChildInvitation($invitation));
                                        }
                                    } else {
                                        $child = '';
                                        $child = IndividualStudent::create([
                                            'name' => $fnames[$key] . " " . $lnames[$key],
                                            'email' => ($email) ? $email : uniqid(Str::random(27)),
                                            'password' => bcrypt(Str::random(10)),
                                            'role_id' => $roleId,
                                        ]);
                                        $children[] = $child->id;
                                        $child->profile()->create([
                                            'firstname' => $fnames[$key],
                                            'lastname' => $lnames[$key],
                                        ]);

                                        if ($child) {
                                            $parent->children()->syncWithoutDetaching($child->id);

                                            $licence = new Licence();
                                            $licence->stripe_id = $subscriptionid;
                                            $licence->purchased_by = $parent->id;
                                            $licence->assigned_to = $child->id;
                                            $licence->number = strtoupper(sha1(time()));
                                            $licence->type = 'Child';
                                            $licence->valid_upto = Carbon::now()->addYears(1)->toDateString();
                                            $licence->save();


                                            $invitation = ChildInvitee::create([
                                                'parent_id' => $parent->id,
                                                'child_id' => $child->id,
                                                'token' => uniqid(Str::random(27)),
                                                'processed' => '0',
                                            ]);
                                            dispatch(new ProcessChildRegistration($invitation));
                                        }
                                    }
                                }
                            }

                            // $user = User::find($parent->id);
                            // $user->newSubscription('Parent/Child', config('services.stripe.child_key'))->quantity($quantity)->create($request->stripeToken);
                        }
                    }

                    if ($old_ids || (isset($form['child_emails']) && array_filter($form['child_emails']))) {
                        DB::commit();
                        \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
                        return redirect('home')->with('message', "Your Account is now active. You can access the site normally!");
                    }
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex);
            return redirect('parent/renewlicense')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }

    public function updatecardsession(Request $request)
    {
        $stripe_id = Auth::user()->stripe_id;
        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'mode' => 'setup',
            'customer' => $stripe_id,
            'allow_promotion_codes' => true,
            'setup_intent_data' => [
                'metadata' => [
                    'customer_id' => $stripe_id,
                    'subscription_id' => 'sub_JIFIK3sA9CTB6X',
                ],
            ],
            'success_url' => route('stripe.cardUpdateSuccess') . '?session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => route('stripe.cardUpdateFailed')
        ]);
        $request->session()->put('cart.updateCard', ['session' => $session]);
        return $session;
    }

    public function stripecardUpdateFailed()
    {
        $link = Auth::check() ? 'profiles/edit' : 'parent/buylicense';
        return redirect($link)->with('message', "Whoops! Something went wrong. Please try again.");
    }

    public function stripecardUpdateSuccess(Request $request)
    {
        $sessionid = $request->session_id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        try {
            $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
            if ($sessiondetail && isset($sessiondetail->setup_intent)) {

                $saved = Auth::user()->updateCardInfoFromIntent($sessiondetail->setup_intent);
                if ($saved) {
                    return redirect('profiles/edit')->with('message', "Your card has been updated successfully!");
                }
            }
            throw new \Exception();
        } catch (\Exception $ex) {
            DB::rollback();
            Log::error($ex->getMessage());
            return redirect('profiles/edit')->with('message', "Whoops! Something went wrong. Please try again.");
        }
    }


    public function renewToggle(Request $request)
    {
        $user = User::find(Auth::id());
        if ($request->autorenew) {
            $user->subscription('Parent')->resume();
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'The auto renewal for you Parent/Child licence has been re-activated.');
        }
        $user->subscription('Parent')->cancel();
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'The auto renewal of you Parent/Child licence has been disabled.');
    }

    public function cardUpdate(Request $request)
    {
        try {
            $user = User::find(Auth::id());
            $user->updateCard($request->stripeToken);
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Your card has been updated successfully!');
        } catch (\Throwable $th) {
            throw $th;
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Woops! There was some error updating your card. Please try again.');
        }
    }

    public function confirm($token)
    {
        $invitation = ChildInvitee::where('token', $token)->first();
        if (Auth::id() == $invitation->child_id) {
            ChildInvitee::where('token', $token)->update([
                'processed' => '1',
            ]);
            $parent = ChildParent::find($invitation->parent_id);
            if ($parentInvitation = ParentInvitee::whereEmail($parent->email)->whereChildId(Auth::id())->first()) {
                $parentInvitation->processed = '1';
                $parentInvitation->save();
            }
            $parent->children()->syncWithoutDetaching($invitation->child_id);

            \Event::dispatch(new LicenseRenewed(User::where("id", $parent->id)->first()));
            // User::addHelpcrunchAccount($invitation->child_id);
            // User::updateAssociatedUsersMailchimpDetail($invitation->child_id);
            // User::updateUserMailchimpDetail($invitation->child_id);
            return redirect('/home')->with('confirmation', "Thanks! '" . $parent->name . "' has now been linked to your account.");
        } else {
            return abort(404);
        }
    }
}
