<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_name' => ['required', 'string', 'max:255'],
            'logo' => ['required', 'file', 'mimes:png,jpg,jpeg', 'max:2048'],
            'states' => ['required', 'array', 'min:1'],
            'states.*' => ['exists:states,id'],
            'industries' => ['required', 'array', 'min:1', 'max:4'],
            'industries.*' => ['exists:industry_categories,id'],
            'industryunits' => ['nullable', 'array'],
            'industryunits.*' => ['exists:industryunits,id'],
            'workexperience_templates' => ['nullable', 'array'],
            'workexperience_templates.*' => ['exists:workexperience_templates,id'],
            'skillstraining_templates' => ['nullable', 'array'],
            'skillstraining_templates.*' => ['exists:skillstraining_templates,id'],
            'lessons' => ['nullable', 'array'],
            'lessons.*' => ['exists:lessons,id'],
            'contact_person' => ['required', 'string', 'max:255'],
            'position' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:255'],
            'subscription_start_date' => ['required', 'date'],
            'subscription_ending_on' => ['required', 'date', 'after:subscription_start_date'],
            'status' => ['required', 'in:draft,published'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'industries.max' => 'You can select at most 4 industries.',
            'industries.min' => 'Please select at least one industry.',
            'industries.required' => 'Please select at least one industry.',
            'subscription_ending_on.after' => 'The subscription end date must be after the start date.',
        ];
    }
}
