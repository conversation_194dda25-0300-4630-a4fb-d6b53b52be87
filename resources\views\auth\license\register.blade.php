@extends('layouts.auth')
@section('content')
<div class="register-bg m-t-50">
    <div class="register-container full-height sm-p-t-30">
        <div class="card no-border">
            <div class="card-block">
                @if(session()->has('message'))
                <div class="alert alert-success text-center">
                    <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
                    {{ session()->get('message') }}
                </div>
                @endif
                <div class="d-flex justify-content-center flex-column full-height ">
                    <a href="/"><img src="{{asset('images/favicon.png')}}" alt="logo" data-src="{{asset('images/favicon.png')}}" data-src-retina="{{asset('images/favicon.png')}}" width="60" height=60></a>
                    <h3 class="oswald ls-3 bold uppercase">Register With Us</h3>
                    <p class="bold">Create your account</p>
                    <form class="" id="form-register" role="form" method="POST" action="{{url('individual/createaccount')}}">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="PUT">
                        <input type="hidden" name="id" value="{{ $student->id }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required @if(($student->profile->firstname)) disabled @endif">
                                    <label>First Name</label>
                                    <input id="firstname" type="text" class="form-control" placeholder="Enter first name " name="firstname" value="{{ $student->profile->firstname }}" required autofocus @if(($student->profile->firstname)) readonly="readonly" @endif>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required @if(($student->profile->lastname)) disabled @endif">
                                    <label>Last Name</label>
                                    <input id="lastname" type="text" class="form-control" placeholder="Enter last name " name="lastname" value="{{ $student->profile->lastname }}" required autofocus @if(($student->profile->lastname)) readonly="readonly" @endif>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group form-group-default required @if(($student->email)) disabled @endif">
                                    <label>Email</label>
                                    <input id="email" type="email" class="form-control" placeholder="Enter email-address " name="email" value="{{ $student->email }}" required @if(($student->email)) disabled @endif>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Password</label>
                                    <input id="password" type="password" placeholder="Enter password" class="form-control" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Confirm Password</label>
                                    <input id="password-confirm" type="password" placeholder="Confirm password" class="form-control" name="password_confirmation" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group form-group-default required form-group-default-select2">
                                    <label>Country</label>
                                    <select class="full-width" name="country" data-init-plugin="select2" data-placeholder="Select.." required>
                                        <option value=""></option>
                                        @foreach ($countries as $country)
                                        <option value="{{$country->id}}">{{$country->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6" id="state-container" style="display: none;">
                                <div class="form-group form-group-default required form-group-default-select2">
                                    <label>State</label>
                                    <select id="state"  class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select.." >
                                        <option value=""></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>Postcode</label>
                                    <input id="postcode" type="number" placeholder="Enter postcode" class="form-control" name="postcode" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Gender</label>
                                    <select class="form-control" id="gender" name="gender" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Select...</option>
                                        <option value="M">Male</option>
                                        <option value="F">Female</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6" id="other_gender">
                                <div class="form-group form-group-default required">
                                    <label>Other Gender</label>
                                    <input type="text" class="form-control other_gender" name="other_gender" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Stage of life</label>
                                    <select class="form-control" id="stage" name="stage" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Select...</option>
                                        <option value="school">I'm still in high school</option>
                                        <option value="done">I've finished high school</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row d-none" id="what-stage">
                            <div class="col-md-12">
                                {{-- <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>What stage of life are you at?</label>
                                    <select class="form-control full-width" name="stage_id" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Select..</option>
                                        @foreach ($stages as $stage)
                                        <option value="{{$stage->id}}">{{$stage->title}}</option>
                                        @endforeach
                                    </select>
                                </div> --}}
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Graduated Year</label>
                                    <select class="full-width" name="graduate_year" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="" selected></option>
                                        @for ($i = date('Y'); $i >= 2015; $i--)
                                        <option>{{$i}}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row d-none" id="school-detail">
                            <div class="col-md-6">
                                <div class="form-group form-group-default form-group-default-select2 required">
                                    <label>Year</label>
                                    <select class="form-control full-width" name="year" data-init-plugin="select2" data-placeholder="Select..">
                                        <option value="">Select..</option>
                                        @foreach ($years as $year)
                                        <option value="{{$year->id}}">{{$year->title}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required">
                                    <label>What School do you go to?</label>
                                    <input id="school-name" type="text" placeholder="Enter school name" class="form-control" name="school" required>
                                </div>
                            </div>
                        </div>
                        <div class="row m-t-10">
                            <div class="col-lg-6">
                                <p>
                                    <small> I agree to the
                                        <a href="/terms" target="_blank" class="text-info">Terms</a> and
                                        <a href="/privacy" target="_blank" class="text-info">Privacy</a>.
                                    </small>
                                </p>
                            </div>
                            <div class="col-lg-6 text-right">
                                <a href="https://www.thecareersdepartment.com/contact" {{-- data-toggle="modal" data-target="#contactModal" --}} class="text-info small">Help? Contact Support</a>{{--  |
                                <a href="/faq" class="text-info" target="_blank">FAQ</a> --}}
                            </div>
                        </div>
                        <button class="btn btn-primary btn-cons m-t-10" type="submit">Create a new account</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@push('modals')
@include('partials.contactmodal')
@endpush
@push('scriptslib')
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
@endpush
@push('styles')
    <style>
        .select2-container{
            width: 100% !important;
        }
    </style>
@endpush
@push('scripts')
<script>
    $(function () {
        // $('#what-school').hide();
        $('#stage').change(function () {
            if ($(this).val() == 'school') {
                $('#school-detail').removeClass('d-none');
                $('#what-stage').addClass('d-none');
                $('#what-stage select').val('').trigger("change.select2");
            } else {
                $('#what-stage').removeClass('d-none');
                $('#school-detail').addClass('d-none');
                $('#school-detail select, #school-detail input').val('').trigger("change.select2");
            }
        });
    });
    $(document).ready(function () {

      jQuery("#other_gender").hide();
      jQuery('#gender').on('change', function() {
        if (this.value == 'Other') {
            jQuery("#other_gender").show();
        } else {
            jQuery("#other_gender").hide();
            jQuery('#form-register .other_gender').val('');
        }
    });

        jQuery("select[name=country]").change(function() {
            jQuery.ajax({
                type: "get",
                url: "{{route('getStates')}}",
                data: {
                    id: jQuery(this).val(),
                },
                success: function (response) {
                    jQuery("select[name=state]").html('<option value=""></option>');
                    $('select[name=state]').val('');
                    if (!response || response.length == 0) {
                        jQuery("#state-container").hide();
                        return;
                    }
                    jQuery("#state-container").show();

                    jQuery.each(response, function (i, v) {
                        jQuery("select[name=state]").append('<option value="'+v.id+'">'+v.name+'</option>')
                    });
                },
                fail: function() {
                    jQuery("select[name=state]").html('<option value=""></option>');
                }
            });
        });
        
        jQuery("select[name=country]").trigger('change');

        $("#form-register").validate({
            ignore: [],
            rules: {
                firstname: {
                    required: true
                },
                lastname: {
                    required: true
                },
                email: {
                    required: true,
                    email: true,
                    remote: {
                        url: "/checkUserName",
                        type: "get",
                        data: {
                            email: function () {
                                return $("input[name='email']").val();
                            }
                        },
                    }
                },
                gender: {
                    required: true
                },
                other_gender: {
                    required: function() {
                        return (jQuery('#gender').val() == 'Other');
                    }
                },
                age: {
                    required: true
                },
                password: {
                    required: true,
                    minlength: 5
                },
                password_confirmation: {
                    equalTo: "#password"
                },
                country: 'required',
                state: {
                    // required: true
                    required: function() {
                        return (jQuery('select[name=state] option[value]').length > 0);
                    }
                },
                postcode: {
                    required: true
                },
                stage: 'required',
                year: {
                    required: function () {
                        return (jQuery('#stage').val() == 'school');
                    }
                },
                school: {
                    required: function () {
                        return (jQuery('#stage').val() == 'school');
                    }
                },
                graduate_year: {
                    required: function () {
                        return (jQuery('#stage').val() == 'done');
                    }
                },
                stage_id: {
                    required: function () {
                        return (jQuery('#stage').val() == 'done');
                    }
                },
            },
            messages: {
                firstname: {
                    required: "Please enter first name here"
                },
                lastname: {
                    required: "Please enter last name here"
                },
                email: {
                    required: "Please enter a valid email address",
                    remote: "Email already exists! Please enter new email"
                },
                gender: {
                    required: "Please select"
                },
                age: {
                    required: "Please enter your age"
                },
                password: {
                    required: "Please enter a strong password"
                },
                password_confirmation: {
                    equalTo: "Please enter a same password again"
                },
                year: {
                    required: "Please select"
                },
            },
            submitHandler: function(form) {
                //this runs when the form validated successfully
                jQuery(form).find(":submit").attr('disabled', 'disabled');
                form.submit();
            }
        });
    });
</script>
@endpush
@endsection
