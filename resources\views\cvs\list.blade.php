@extends('layouts.admin', ['noGreyBg' => 'no-grey-bg'])
@section('breadcrumbs', Breadcrumbs::render('studentcv'))
@push('stylesheets')
<link rel="stylesheet" href="{{ asset('css/cvbuilder.css') }}?v={{ filemtime(public_path('css/cvbuilder.css')) }}
 " >
@endpush
@section('content')
{{-- @include('partials.studentbannertabs') --}}
@if(session()->has('message'))
<div class="alert alert-success text-center">
    <a href="#" class="close" data-dismiss="alert" aria-label="close"></a>
    {{ session()->get('message') }}
</div>
@endif
<div class="card card-transparent">
    <div class="card-header">
        <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
        <div class="card-title custom-card-title">Resume Builder</div>
    </div>
    <div class="card-block">
        <div class="cv-list">
            <ul class="list-inline text-center-sm-down cv-tiles">
                <li class="text-center create-new-tile">
                    <a href="{{ route('cvs.selecttemplate') }}">
                        <div class="cv-tile-content">
                            <div class="plus"><i class="fa fa-plus-circle" aria-hidden="true"></i></div>
                            <div class="title">Create new</div>
                        </div>
                    </a>
                </li>
                @forelse ($cvs as $id => $name)
                <li class="text-center">
                    <div class="cv-tile-content">
                        <div class="icons">
                            <a href="{{ route('cvs.destroy', $id) }}" data-method="delete" data-token="{{csrf_token()}}" data-confirm="Are you sure?" data-toggle="tooltip" title="Delete!">
                                <i class="fa fa-trash"></i>
                            </a>
                            <a href="{{ route('cvs.selecttemplate', $id) }}" data-toggle="tooltip" title="Edit!">
                                <i class="fa fa-pencil"></i>
                            </a>
                            <a href="{{ route('cvs.copy', $id) }}" data-toggle="tooltip" title="Copy!">
                                <i class="fa fa-copy"></i>
                            </a>
                        </div>
                        <div class="title">{{ $name }}</div>
                    </div>
                </li>
                @empty

                @endforelse
            </ul>
        </div>
    </div>
</div>
@push('modals')
@include ('cvs.modals.welcome')
@endpush
@push('scripts')
<script>
    jQuery(document).ready(function () {
        @if (!(session('hideWelcomePopup')))
            jQuery('#modalWelcome').modal('show');
        @endif

        jQuery('#modalWelcome').on('hide.bs.modal', function () {
            @php session(['hideWelcomePopup' => true]) @endphp
        });
    });
</script>
@endpush
@endsection