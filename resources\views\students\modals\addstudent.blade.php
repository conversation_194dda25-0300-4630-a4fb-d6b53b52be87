<div class="modal fade fill-in" id="modalStudentAdd" role="dialog" aria-hidden="true">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
        <i class="pg-close">
        </i>
    </button>
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="text-left p-b-5">
                    <span class="semi-bold">
                        Add Student
                    </span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class=" col-lg-12 ">
                        <!-- START card -->
                        <div class="card card-transparent">
                            <div class="card-block">
                                <form method="POST" action="" id="form-studentadd" role="form" autocomplete="off">
                                    {{ csrf_field() }}
                                    <div class="row clearfix">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>First Name</label>
                                                <input type="text" class="form-control" name="firstname" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="lastname" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default required">
                                                <label>Email</label>
                                                <input type="email" class="form-control" name="email" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default">
                                                <label>Password</label>
                                                <input type="password" class="form-control" name="password" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Gender</label>
                                                <select class="full-width" id="add_gender" name="gender" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    <option value="M">Male</option>
                                                    <option value="F">Female</option>
                                                    <option value="O">Other / Prefer not to say</option>
                                                </select>
                                            </div>
                                        </div>
                                        {{-- <div class="col-md-6" id="add_other_gender">
                                            <div class="form-group form-group-default required">
                                                <label>Other Gender</label>
                                                <input type="text" class="form-control" name="other_gender" />
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>Country</label>
                                                <select class="full-width" name="country" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    @foreach($countries as $country)
                                                    <option value="{{ $country->id }}"> {{ $country->name }} </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6" id="state-container" style="display: none;">
                                            <div class="form-group form-group-default form-group-default-select2 required">
                                                <label>State</label>
                                                <select class="full-width" name="state" data-init-plugin="select2" data-placeholder="Select..">
                                                    <option value=""></option>
                                                    {{-- @foreach ($states as $state)
                                                        <option value="{{$state->id}}">{{$state->name}}</option>
                                                        @endforeach --}}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default">
                                                    <label>Postcode</label>
                                                    <input type="number" placeholder="" class="form-control" name="postcode">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            @if (Auth::user()->role->name == 'Admin')
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>School</label>
                                                    <select class="full-width" name="school" id="add_student_school_id" data-placeholder="Select..">
                                                        <option value=""></option>
                                                        @foreach ($schools as $key => $school)
                                                        <option value="{{$school->id}}">{{$school->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="campus-add" style="display: none;">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Campus</label>
                                                    <select class="full-width" name="campus" data-init-plugin="select2" data-placeholder="Select..">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Organisation</label>
                                                    <select class="full-width" name="organisation" data-init-plugin="select2" data-placeholder="Select..">
                                                        <option value=""></option>
                                                        @foreach ($organisations as $key => $organisation)
                                                        <option value="{{$organisation->id}}">{{$organisation->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="orgCampus-add" style="display: none;">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Campus</label>
                                                    <select class="full-width" name="orgCampus" data-init-plugin="select2" data-placeholder="Select..">
                                                        <option value=""></option>
                                                    </select>
                                                </div>
                                            </div>
                                            @endif
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Year</label>
                                                    <select class="full-width" name="year" data-placeholder="Select.." data-init-plugin="select2">
                                                        <option value=""></option>
                                                        @foreach ($years as $key => $year)
                                                        <option value="{{$year->id}}">{{$year->title}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row clearfix pb-2">
                                            <div class="col-md-12">
                                                <label for="" class="p-r-10">Send welcome email to student</label>
                                                <input type="hidden" class="" name="welcome_email" value="0" />
                                                <input type="checkbox" class="switchery" name="welcome_email" value="1" />
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                        <button class="btn btn-primary pull-right" type="submit">
                                            Add Student
                                        </button>
                                    </form>
                                </div>
                            </div>
                            <!-- END card -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer"></div>
            </div>
            <!-- /.modal-content -->
        </div>
        <!-- /.modal-dialog -->
    </div>
    @push('scripts')
    <script>
        jQuery(document).ready(function () {
            $(`#add_student_school_id`).select2({
                dropdownParent: $('#modalStudentAdd'),
            });
            
            // jQuery("#add_other_gender").hide();
            // jQuery('#add_gender').on('change', function() {
            //     if (this.value == 'Other') {
            //         jQuery("#add_other_gender").show();
            //     } else {
            //         jQuery("#add_other_gender").hide();
            //         jQuery('#modalStudentAdd input[name=other_gender]').val('');
            //     }
            // });

            jQuery("#form-studentadd select[name=country]").change(function() {
                jQuery.ajax({
                    type: "get",
                    url: "{{route('getStates')}}",
                    data: {
                        id: jQuery(this).val()
                    },
                    success: function (response) {
                        // jQuery("#form-studentadd select[name=state]").html('<option value=""></option>');
                        jQuery("select[name=state]").html('<option selected disabled></option>');
                        // New logic for states - START
                            if (!response || response.length == 0) {
                                jQuery("#state-container").hide();
                                return;
                            }
                            jQuery("#state-container").show();
                            jQuery.each(response, function (i, v) {
                                jQuery("#form-studentadd select[name=state]").append('<option value="'+v.id+'">'+v.name+'</option>');
                            });
                        jQuery("#form-studentadd select[name=state]").trigger("change.select2");
                        // Newlogic for states - END

                    },
                    fail: function() {
                        jQuery("#form-studentadd select[name=state]").html('<option value="" disabled></option>');
                        jQuery("#state-container").hide();
                    }
                });
            });

            // Handle school selection and show campus fields
            jQuery('#form-studentadd select[name=school]').change(function() {
                jQuery('#form-studentadd select[name="campus"]').html('<option value=""></option>');
                if (jQuery(this).val()) {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getSchoolCampuses') }}",
                        data: {
                            id: jQuery(this).val()
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (Object.keys(response).length) {
                                jQuery.each(response, function(id, name) {
                                    jQuery('#form-studentadd select[name="campus"]').append('<option value="' + id + '">' + name + '</option>')
                                });
                                jQuery('#campus-add').show();
                                console.log(jQuery('#campus-add'));
                            } else {
                                jQuery('#campus-add').hide();
                            }
                        },
                    });
                } else {
                    jQuery('#campus-add').hide();
                }
            });

            // Handle organisation selection and show campus fields
            jQuery('#form-studentadd select[name=organisation]').change(function() {
                jQuery('#form-studentadd select[name="orgCampus"]').html('<option value=""></option>');
                if (jQuery(this).val()) {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getOrganisationCampuses') }}",
                        data: {
                            id: jQuery(this).val()
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (Object.keys(response).length) {
                                jQuery.each(response, function(id, name) {
                                    jQuery('#form-studentadd select[name="orgCampus"]').append('<option value="' + id + '">' + name + '</option>')
                                });
                                jQuery('#orgCampus-add').show();
                            } else {
                                jQuery('#orgCampus-add').hide();
                            }
                        },
                    });
                } else {
                    jQuery('#orgCampus-add').hide();
                }
            });

            jQuery(document).on('show.bs.modal', '#modalStudentAdd', function () {
                jQuery("#form-studentadd input:not([type=hidden]):not([type=checkbox]):not([type=radio]):not([type=submit]), #form-studentadd select:not([type=hidden]").val('');
                jQuery('#campus-add').hide();
                jQuery('#orgCampus-add').hide();
                jQuery('#state-container').hide();
            });

            jQuery(document).on('shown.bs.modal', '#modalStudentAdd', function () {
                jQuery('#form-studentadd .error-help-block').remove();
                jQuery('#form-studentadd .form-group').removeClass('has-error');
            });

            jQuery("#modalStudentAdd").on('hidden.bs.modal', function () {
                jQuery("#form-studentadd input:not([type=hidden]):not([type=checkbox]):not([type=radio]):not([type=submit]), #form-studentadd select:not([type=hidden]").val('');
                jQuery("#form-studentadd select").trigger("change.select2");
                jQuery('#campus-add').hide();
                jQuery('#orgCampus-add').hide();
                jQuery('#state-container').hide();
                // validator.destroy()
            });

            jQuery('#form-studentadd').validate({
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        email: 'required',
                        country: 'required',
                        state: {
                        // new logic for states - START
                            required: function() {
                                return (jQuery('select[name=state] option[value]').length > 0);
                            }
                        // new logic for states - START
                        },
                    },
                })
        });
    </script>
    {!! JsValidator::formRequest('App\Http\Requests\StoreStudent')->selector('#form-studentadd') !!}
    @endpush
