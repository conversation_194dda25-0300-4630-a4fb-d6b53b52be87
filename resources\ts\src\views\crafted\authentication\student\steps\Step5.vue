<template>
    <div class="w-100">
        <div class="pb-8 pb-lg-10 text-center">
            <p class="mb-2 fw-bold text-dark fs-2x">Your Details</p>
            <div class="text-gray-400 fw-semobold fs-6">
                Enter your details below to finalise your account
            </div>
        </div>
        <div class="w-100 text-gray-400">
            <div class="fv-row mb-3">
                <div class="form-floating ">
                    <Field id="studentEmail" class="form-control form-control-lg rounded-0" type="email"
                        placeholder="Email" name="email" autocomplete="off" v-model="formData.email" />
                    <label for="studentEmail">Email</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="email" />
                    </div>
                </div>
            </div>
            <div class="row fv-row py-3">
                <div class="col-xl-6">
                    <div class="form-floating ">
                        <Field id="studentFname" class="form-control form-control-lg rounded-0" type="text"
                            placeholder="First Name" name="firstName" autocomplete="off" v-model="formData.firstName" />
                        <label for="studentFname">First Name</label>
                    </div>
                    <div class="fv-plugins-message-container">
                        <div class="fv-help-block">
                            <ErrorMessage name="firstName" />
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="form-floating ">
                        <Field id="studentLname" class="form-control form-control-lg rounded-0" type="text"
                            placeholder="Last Name" name="lastName" autocomplete="off" v-model="formData.lastName" />
                        <label for="studentLname">Last Name</label>
                    </div>
                    <div class="fv-plugins-message-container">
                        <div class="fv-help-block">
                            <ErrorMessage name="lastName" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="py-3 fv-row" data-kt-password-meter="true">
                <div class="">
                    <div class="position-relative">
                        <div class="form-floating ">
                            <Field id="studentPass" class="form-control form-control-lg rounded-0" type="password"
                                placeholder="Password" name="password" autocomplete="off" v-model="formData.password"
                                @focus="showPasswordTooltip = true" @blur="onPasswordBlur"
                                @input="showPasswordTooltip = true" />
                            <label for="studentPass">Password</label>
                        </div>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="password" />
                            </div>
                        </div>
                        <!-- Password requirements tooltip -->
                        <transition name="fade">
                            <div v-if="showPasswordTooltip && (formData.password !== undefined && formData.password !== '')"
                                class="password-tooltip position-absolute bg-light border rounded shadow p-3 mt-1 tooltip-below"
                                style="z-index: 10; width: 100%; left: 0; top: 110%; min-width: unset; max-width: 320px;">
                                <div class="small mb-1 fw-bold">Password must</div>
                                <ul class="list-unstyled mb-0">
                                    <li
                                        :class="{ 'text-success': passwordChecks.minLength, 'text-danger': !passwordChecks.minLength }">
                                        <i
                                            :class="passwordChecks.minLength ? 'fas fa-check-circle' : 'far fa-circle'" />
                                        At least 8 characters
                                    </li>
                                    <li
                                        :class="{ 'text-success': passwordChecks.hasNumberOrSpecial, 'text-danger': !passwordChecks.hasNumberOrSpecial }">
                                        <i
                                            :class="passwordChecks.hasNumberOrSpecial ? 'fas fa-check-circle' : 'far fa-circle'" />
                                        At least one number or special character
                                    </li>
                                </ul>
                            </div>
                        </transition>
                        <!-- End password requirements tooltip -->
                    </div>
                </div>
                <div class="fv-row py-3">
                    <div class="form-floating ">
                        <Field id="studentConfirmPass" class="form-control form-control-lg rounded-0" type="password"
                            placeholder="Confirm Password" name="password_confirmation" autocomplete="off"
                            v-model="formData.password_confirmation" />
                        <label for="studentConfirmPass">Confirm Password</label>
                    </div>
                    <div class="fv-plugins-message-container">
                        <div class="fv-help-block">
                            <ErrorMessage name="password_confirmation" />
                        </div>
                    </div>
                </div>
                <div class="row fv-row py-3">
                    <div class="col-xl-12">
                        <Field type="text" name="country" v-slot="{ field }">
                            <Multiselect
                                class="rounded-0  form-control"
                                v-bind="field"
                                :searchable="true"
                                placeholder="Country"
                                noOptionsText="Type a character to search your country"
                                :resolve-on-load="false"
                                :options="countrieslist"
                                v-model="formData.country"
                            ></Multiselect>
                        </Field>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="country" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row fv-row py-3">
                    <div class="col-xl-6" v-if="showStates">
                        <Field type="text" name="state" v-slot="{ field }">
                            <Multiselect class="rounded-0  form-control " v-bind="field" :searchable="false"
                                placeholder="State" noOptionsText="Type a character to search your school"
                                :resolve-on-load="false" :options="stateslist" v-model="formData.state"></Multiselect>
                        </Field>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="state" />
                            </div>
                        </div>
                    </div>
                    <div :class="showStates ? 'col-xl-6' : 'col-xl-12'">
                        <div class="form-floating ">
                            <Field id="studentPostcode" class="form-control form-control-lg rounded-0" type="text"
                                placeholder="Postcode" name="postcode" autocomplete="off" v-model="formData.postcode" />
                            <label for="studentPostcode fs-6">Postcode</label>
                        </div>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="postcode" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row fv-row py-3">
                    <div :class="graduated() ? 'col-xl-12' : 'col-xl-6'">
                        <Field v-slot="{ field }" v-model="formData.gender" name="gender">
                            <Multiselect class="rounded-0  form-control fs-6" v-bind="field" :searchable="false"
                                placeholder="Gender" :resolve-on-load="false" :options="genderlist"></Multiselect>
                        </Field>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="gender" />
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-6" v-if="graduated()">
                        <!-- Optionally, you can show a message or leave this blank for notinschool -->
                    </div>
                    <div class="col-xl-6" v-if="!graduated()">
                        <Field type="text" name="year" v-model="formData.year" v-slot="{ field }">
                            <Multiselect class="rounded-0  form-control fs-6" v-bind="field" :searchable="false"
                                placeholder="Year" :resolve-on-load="false" :options="fetchYears()"></Multiselect>
                        </Field>
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <ErrorMessage name="year" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, computed, watch } from "vue";
import { Field, ErrorMessage } from "vee-validate";
import { PasswordMeterComponent } from "@/assets/ts/components";
import Multiselect from '@vueform/multiselect'
import { useRegisterStore } from "@/stores/Auth/RegisterStore";

export default defineComponent({
    name: "step-5",
    components: { Field, ErrorMessage, Multiselect },
    props: ['formData'],
    setup(props) {
        onMounted(() => {
            fetchCountries();
            fetchStates();
            nextTick(() => {
                PasswordMeterComponent.bootstrap();
            });

        });
        const authStore = useRegisterStore();

        const stateslist = ref();
        const yearslist = ref();
        const genderlist = [
            { value: "M", label: "Male" },
            { value: "F", label: "Female" },
            { value: "O", label: "Other / Prefer not to say" }
        ]

        const gradYearOptions: { value: number; label: any }[] = [];

        for (let i = new Date().getFullYear(); i >= 2015; i--) {
            gradYearOptions.push({
                value: i,
                label: i, // Convert to string if needed
            });
        }
        const fetchStates = async () => {
            const response = await fetch(
                `states` + (props.formData.country ? `?country_id=${props.formData.country}` : ''),
                {
                }
            );

            const data = await response.json();

            if (!data || data.length === 0) {
                props.formData.state = '';
            }

            stateslist.value = data.map((item) => {
                return { value: item.id, label: item.name }
            });
        };
        const fetchYears = () => {
            return authStore.studentDetail.school.years;
        };
        const graduated = () => {
            return (authStore.studentDetail.inSchool == 'notinschool');
        }
        const passwordChecks = computed(() => {
            const val = props.formData.password || '';
            return {
                minLength: val.length >= 8,
                hasNumberOrSpecial: /[0-9!@#$%^&*(),.?":{}|<>]/.test(val)
            };
        });
        const showPasswordTooltip = ref(false);
        const onPasswordBlur = () => {
            if (!props.formData.password) {
                showPasswordTooltip.value = false;
            } else {
                setTimeout(() => { showPasswordTooltip.value = false; }, 500);
            }
        };

        const countrieslist = ref([]);
        const fetchCountries = async () => {
            const response = await fetch('/api/countries');

            const { data } = await response.json();
            
            countrieslist.value = data.map((item: {id: number, name: string}) => {
                return { value: item.id, label: item.name }
            });
        };
        const handleCountryChange = (selectedCountry) => {
            fetchStates();
        };
        const showStates = computed(() => {
            return stateslist.value && stateslist.value?.length > 0 && props.formData?.country;
        });
        watch(() => props.formData.country, (newValue) => {
            if (newValue) {
                handleCountryChange(newValue);
            }
        });

        return {
            fetchStates,
            stateslist,
            yearslist,
            genderlist,
            gradYearOptions,
            fetchYears,
            graduated,
            passwordChecks,
            showPasswordTooltip,
            onPasswordBlur,
            countrieslist,
            showStates,
        }
    }
});
</script>
<style>
.multiselect-placeholder {
    font-size: 1.075rem !important;
}

.password-requirements li,
.password-tooltip li {
    display: flex;
    align-items: center;
    gap: 0.5em;
}

.password-requirements .text-success,
.password-tooltip .text-success {
    color: #198754 !important;
}

.password-requirements .text-danger,
.password-tooltip .text-danger {
    color: #dc3545 !important;
}

.password-tooltip {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    animation: fadeIn 0.2s;
    left: 0 !important;
    top: 110% !important;
    width: 100% !important;
    min-width: unset !important;
    max-width: 320px;
}

.tooltip-below {
    left: 0 !important;
    top: 110% !important;
    width: 100% !important;
    min-width: unset !important;
    max-width: 320px;
}

@media (max-width: 575.98px) {
    .password-tooltip {
        left: 0 !important;
        top: 110% !important;
        width: 100% !important;
        min-width: unset !important;
        max-width: 320px;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}
</style>
