<template>
    <div class="tab-pane fade show active" id="kt_project_users_card_pane">
        <div class="row g-10">
            <!-- Loop over companies -->
            <div v-for="company in companiesList" :key="company.id" class="col-12 col-md-6 col-xl-4">
                <div class="card p-8 h-100">
                    <!-- Header row -->
                    <div class="row align-items-center mb-5">
                        <div class="col-auto">
                            <img :src="company.logo" style="height:37px; width:37px; object-fit: cover;" />
                        </div>
                        <div class="col">
                            <p class="mb-0 fs-2 fw-bold">{{ company.name }}</p>
                        </div>
                        <div class="col-auto ms-auto">
                            <button @click="toggleFav(company)" class="bg-transparent border-0 p-0">
                                <svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="25" cy="25" r="20" :fill="company.isFav ? 'black' : '#eeeeee'"
                                        stroke="none" />
                                    <svg width="45" height="40" viewBox="-2 -4 34 33"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M17 11.1388C20.8834 7.18512 30.5926 14.1037 17 23C3.40738 14.1046 13.1166 7.18512 17 11.1388Z"
                                            :fill="company.isFav ? 'white' : '#eeeeee'" stroke="gray" />
                                    </svg>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Location -->
                    <!-- <p class="text-black mb-5 mt-3">
                        <i class="bi bi-geo-alt-fill text-black"></i> {{ company.location }}
                    </p> -->

                    <p class="text-black mb-5 mt-3">
                        <span v-for="(state, index) in company.location" :key="index">
                            <i class="bi bi-geo-alt-fill text-black"></i> {{ state }}
                            <span v-if="index < company.location.length - 1">&nbsp;</span>
                        </span>
                    </p>

                    <!-- Description -->
                    <p class="text-muted">
                        {{ company.description }}
                    </p>

                    <!-- Buttons row -->
                    <div class="row mt-3">
                        <div class="col-auto">
                            <button class="btn btn-sm fw-semibold" style="font-size:1.1rem;"
                                :class="company.isInGameplan ? '' : 'btn-light'"
                                @click="toggleGameplanCompany(company)">
                                <i v-if="!company.isInGameplan" class="fa-solid fa-plus"></i>
                                <i v-else class="fa-solid fa-check"></i>
                                {{ company.isInGameplan ? 'Added to Game Plan' : 'Add To Game Plan' }}
                            </button>
                        </div>

                        <div class="col text-end">
                            <router-link :to="{ name: 'employer-company', params: { id: company.id } }"
                                class="btn btn-light">
                                View Company Profile
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"
                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
                                </svg>
                            </router-link>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /End loop -->
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, reactive, watch} from 'vue';
import axios from 'axios';

interface Company {
    id: number;
    name: string;
    logo: string;
    location: string;
    description: string;
    profileUrl: string;
    isInGameplan?: boolean;
    isFav?: boolean;
}

export default defineComponent({
    name: 'EmployerCourseBlockView',
    props: {
        companies: {
            type: Array as () => Company[],
            required: true,
            default: () => [],
        },
        loading: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    setup(props) {
        const latestGameplanId = ref<number | null>(null);
        const companiesList = reactive<any[]>([]);


        const markCompaniesInGameplan = async () => {
            if (!companiesList.length) return;

            try {
                const response = await axios.get('/api/companies/gameplans/latest');
                latestGameplanId.value = response.data.id;

                const companiesRes = await axios.get(`/api/gameplans/${latestGameplanId.value}/companies`);
                const companiesInGameplan = companiesRes.data.map((c: any) => c.company_id);

                companiesList.forEach(company => {
                    company.isInGameplan = companiesInGameplan.includes(company.id);
                });

            } catch (error) {
                console.error('Error fetching latest gameplan:', error);
            }
        };


        watch(
            () => props.companies,
            (newCompanies) => {
                companiesList.splice(0, companiesList.length, ...newCompanies.map(c => ({ ...c, isInGameplan: false })));
                markCompaniesInGameplan();
            },
            { immediate: true }
        );


        const toggleFav = async (company: any) => {
            try {
                company.isFav = !company.isFav;
                const response = await axios.post(`/api/companies/${company.id}/toggle-fav`);
                company.isFav = response.data.isFav;
            } catch (error) {
                console.error('Error toggling favorite:', error);
                company.isFav = !company.isFav;
            }
        };

        const toggleGameplanCompany = async (company: any) => {
            if (!latestGameplanId.value) {
                console.error('No active gameplan found.');
                return;
            }

            try {
                const response = await axios.post(`/api/companies/toggle-gameplan/${latestGameplanId.value}`, {
                    company_id: company.id,
                    company_name: company.name,
                });

                company.isInGameplan = !response.data.removed;
            } catch (error) {
                console.error('Error adding to gameplan:', error);
            }
        };

        return {
            companiesList,
            toggleFav,
            toggleGameplanCompany
        };
    }
});
</script>